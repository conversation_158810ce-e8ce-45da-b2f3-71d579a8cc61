generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BaseModel {
  id        String    @id @default(uuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  active    Boolean   @default(true)
  deletedAt DateTime?

  @@ignore
}

model ProfessionGroup {
  id                 String         @id @default(uuid())
  name               String
  description        String?
  active             Boolean        @default(true)
  companyId          String?
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt
  deletedAt          DateTime?
  defaultModules     SystemModule[] @default([BASIC])
  defaultPermissions String[]       @default([])
  professions        Profession[]
  company            Company?       @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
  @@index([deletedAt])
  @@index([name])
}

model Profession {
  id          String           @id @default(uuid())
  name        String
  description String?
  active      Boolean          @default(true)
  groupId     String?
  companyId   String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  deletedAt   DateTime?
  company     Company?         @relation(fields: [companyId], references: [id])
  group       ProfessionGroup? @relation(fields: [groupId], references: [id])
  users       User[]

  @@index([companyId])
  @@index([groupId])
  @@index([active])
  @@index([deletedAt])
  @@index([name])
}

model User {
  id                              String                    @id @default(uuid())
  email                           String                    @unique
  password                        String
  active                          Boolean                   @default(true)
  createdAt                       DateTime                  @default(now())
  updatedAt                       DateTime                  @updatedAt
  address                         String?
  birthDate                       DateTime?
  cnpj                            String?                   @unique
  cpf                             String?                   @unique
  createdById                     String?
  fullName                        String
  login                           String                    @unique
  modules                         SystemModule[]
  permissions                     String[]
  phone                           String?
  companyId                       String?
  deletedAt                       DateTime?
  deletedById                     String?
  failedLoginAttempts             Int                       @default(0)
  lastLoginAt                     DateTime?
  lastLoginIp                     String?
  passwordChangedAt               DateTime?
  role                            UserRole                  @default(EMPLOYEE)
  professionId                    String?
  profileImageUrl                 String?
  city                            String?
  postalCode                      String?
  state                           String?
  branchId                        String?
  neighborhood                    String?
  modulePreferences               Json?
  AuditLog                        AuditLog[]
  createdClients                  Client[]                  @relation("CreatedClients")
  deletedClients                  Client[]                  @relation("DeletedClients")
  createdContacts                 Contact[]                 @relation("CreatedContacts")
  deletedContacts                 Contact[]                 @relation("DeletedContacts")
  createdConversations            Conversation[]            @relation("ConversationCreator")
  participatedConversations       ConversationParticipant[]
  createdCurriculumFolders        CurriculumFolder[]        @relation("CurriculumFolderCreator")
  createdCurriculumFolderPrograms CurriculumFolderProgram[] @relation("CurriculumFolderProgramCreator")
  createdDocuments                Document[]                @relation("DocumentCreatedBy")
  documents                       Document[]                @relation("UserDocuments")
  createdEvaluations              Evaluation[]              @relation("EvaluationCreator")
  sentMessages                    Message[]
  createdPersons                  Person[]                  @relation("CreatedPersons")
  deletedPersons                  Person[]                  @relation("DeletedPersons")
  createdPrograms                 Program[]                 @relation("ProgramCreator")
  createdAnamneses                Anamnese[]                @relation("AnamneseCreator")
  anamneseUltimaAtualizacao       Anamnese[]                @relation("AnamneseUltimaAtualizacao")

  // Relações para Evolução Diária
  evolucoesDiariasProfissional    EvolucaoDiaria[]        @relation("EvolucaoDiariaProfissional")
  createdEvolucoesDiarias         EvolucaoDiaria[]        @relation("EvolucaoDiariaCreator")
  evolucaoDiariaUltimaAtualizacao EvolucaoDiaria[]        @relation("EvolucaoDiariaUltimaAtualizacao")
  createdArquivosEvolucaoDiaria   ArquivoEvolucaoDiaria[]
  recurrencesAsCreator            Recurrence[]            @relation("CreatorRecurrence")
  recurrencesAsProvider           Recurrence[]            @relation("ProviderRecurrence")
  schedulingsAsCreator            Scheduling[]            @relation("CreatorScheduling")
  schedulingsAsProvider           Scheduling[]            @relation("ProviderScheduling")
  branch                          Branch?                 @relation(fields: [branchId], references: [id])
  company                         Company?                @relation(fields: [companyId], references: [id])
  createdBy                       User?                   @relation("UserCreatedBy", fields: [createdById], references: [id])
  createdUsers                    User[]                  @relation("UserCreatedBy")
  deletedBy                       User?                   @relation("UserDeletedBy", fields: [deletedById], references: [id])
  deletedUsers                    User[]                  @relation("UserDeletedBy")
  professionObj                   Profession?             @relation(fields: [professionId], references: [id])
  WorkingHours                    WorkingHours[]

  @@index([companyId])
  @@index([branchId])
  @@index([email])
  @@index([login])
  @@index([role])
}

model Client {
  id              String            @id @default(uuid())
  login           String            @unique
  email           String            @unique
  password        String
  active          Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  createdById     String
  companyId       String?
  deletedAt       DateTime?
  deletedById     String?
  Company         Company?          @relation(fields: [companyId], references: [id])
  createdBy       User              @relation("CreatedClients", fields: [createdById], references: [id])
  deletedBy       User?             @relation("DeletedClients", fields: [deletedById], references: [id])
  ClientInsurance ClientInsurance[]
  Document        Document[]
  persons         Person[]
  Recurrence      Recurrence[]
  Scheduling      Scheduling[]

  @@index([companyId])
  @@index([email])
  @@index([login])
}

model Person {
  id                     String                        @id @default(uuid())
  email                  String?
  phone                  String?
  address                String?
  city                   String?
  state                  String?
  birthDate              DateTime?
  notes                  String?
  active                 Boolean                       @default(true)
  createdAt              DateTime                      @default(now())
  updatedAt              DateTime                      @updatedAt
  deletedAt              DateTime?
  neighborhood           String?
  postalCode             String?
  clientId               String?
  cpf                    String?                       @unique
  createdById            String
  deletedById            String?
  fullName               String
  gender                 String?
  profileImageUrl        String?
  relationship           String?
  useClientEmail         Boolean                       @default(false)
  useClientPhone         Boolean                       @default(false)
  contacts               Contact[]                     @relation("PersonContacts")
  curriculumFolders      CurriculumFolder[]
  documents              Document[]                    @relation("PersonDocuments")
  client                 Client?                       @relation(fields: [clientId], references: [id])
  createdBy              User                          @relation("CreatedPersons", fields: [createdById], references: [id])
  deletedBy              User?                         @relation("DeletedPersons", fields: [deletedById], references: [id])
  personInsurances       PersonInsurance[]
  insuranceServiceLimits PersonInsuranceServiceLimit[]
  anamneses              Anamnese[]
  Recurrence             Recurrence[]
  schedulings            Scheduling[]                  @relation("PersonToScheduling")
  evolucoesDiarias       EvolucaoDiaria[]

  @@index([fullName])
  @@index([cpf])
  @@index([clientId])
}

model Document {
  id          String   @id @default(uuid())
  filename    String
  path        String
  type        String
  createdAt   DateTime @default(now())
  clientId    String?
  userId      String?
  companyId   String?
  createdById String?
  externalUrl String?
  mimeType    String
  ownerType   String
  size        Int
  personId    String?
  Client      Client?  @relation(fields: [clientId], references: [id])
  company     Company? @relation(fields: [companyId], references: [id])
  createdBy   User?    @relation("DocumentCreatedBy", fields: [createdById], references: [id])
  person      Person?  @relation("PersonDocuments", fields: [personId], references: [id])
  user        User?    @relation("UserDocuments", fields: [userId], references: [id])

  @@index([personId])
  @@index([type])
  @@index([clientId])
  @@index([companyId])
  @@index([ownerType, clientId])
  @@index([ownerType, companyId])
  @@index([ownerType, userId])
  @@index([userId])
}

model Scheduling {
  id                 String           @id @default(uuid())
  userId             String
  clientId           String
  creatorId          String
  locationId         String
  title              String
  description        String?
  startDate          DateTime
  endDate            DateTime
  serviceTypeId      String
  insuranceId        String?
  status             SchedulingStatus @default(PENDING)
  createdAt          DateTime         @default(now())
  recurrenceId       String?
  companyId          String?
  confirmationSentAt DateTime?
  reminderSentAt     DateTime?
  branchId           String?
  branch             Branch?          @relation(fields: [branchId], references: [id])
  Client             Client           @relation(fields: [clientId], references: [id])
  company            Company?         @relation(fields: [companyId], references: [id])
  creator            User             @relation("CreatorScheduling", fields: [creatorId], references: [id])
  insurance          Insurance?       @relation(fields: [insuranceId], references: [id])
  location           Location         @relation(fields: [locationId], references: [id])
  recurrence         Recurrence?      @relation(fields: [recurrenceId], references: [id])
  serviceType        ServiceType      @relation(fields: [serviceTypeId], references: [id])
  provider           User             @relation("ProviderScheduling", fields: [userId], references: [id])
  Person             Person[]         @relation("PersonToScheduling")

  @@index([companyId])
  @@index([branchId])
  @@index([userId, startDate])
  @@index([status, startDate])
  @@index([locationId, startDate])
  @@index([serviceTypeId])
  @@index([clientId, startDate])
}

model Recurrence {
  id                  String              @id @default(uuid())
  title               String
  description         String?
  clientId            String
  userId              String
  locationId          String
  serviceTypeId       String
  insuranceId         String?
  recurrenceType      RecurrenceType
  numberOfOccurrences Int?
  endDate             DateTime?
  active              Boolean             @default(true)
  createdAt           DateTime            @default(now())
  createdById         String
  companyId           String?
  branchId            String?
  personId            String
  branch              Branch?             @relation(fields: [branchId], references: [id])
  Client              Client              @relation(fields: [clientId], references: [id])
  company             Company?            @relation(fields: [companyId], references: [id])
  creator             User                @relation("CreatorRecurrence", fields: [createdById], references: [id])
  insurance           Insurance?          @relation(fields: [insuranceId], references: [id])
  location            Location            @relation(fields: [locationId], references: [id])
  person              Person              @relation(fields: [personId], references: [id])
  serviceType         ServiceType         @relation(fields: [serviceTypeId], references: [id])
  provider            User                @relation("ProviderRecurrence", fields: [userId], references: [id])
  patterns            RecurrencePattern[]
  schedulings         Scheduling[]

  @@index([companyId])
  @@index([branchId])
  @@index([personId])
  @@index([userId])
  @@index([active])
  @@index([clientId])
}

model RecurrencePattern {
  id               String     @id @default(uuid())
  recurrenceId     String
  dayOfWeek        Int
  endTimeMinutes   Int
  startTimeMinutes Int
  recurrence       Recurrence @relation(fields: [recurrenceId], references: [id])

  @@index([recurrenceId])
  @@index([dayOfWeek])
}

model Location {
  id          String       @id @default(uuid())
  name        String
  address     String
  phone       String?
  active      Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  companyId   String?
  deletedAt   DateTime?
  branchId    String?
  branch      Branch?      @relation(fields: [branchId], references: [id])
  company     Company?     @relation(fields: [companyId], references: [id])
  recurrences Recurrence[]
  schedulings Scheduling[]

  @@index([companyId])
  @@index([active])
  @@index([branchId])
}

model ServiceType {
  id                          String                        @id @default(uuid())
  name                        String
  value                       Decimal                       @db.Decimal(10, 2)
  companyId                   String?
  PersonInsuranceServiceLimit PersonInsuranceServiceLimit[]
  recurrences                 Recurrence[]
  schedulings                 Scheduling[]
  company                     Company?                      @relation(fields: [companyId], references: [id])

  @@unique([name, companyId])
  @@index([companyId])
}

model Insurance {
  id               String                        @id @default(uuid())
  name             String
  companyId        String?
  ClientInsurance  ClientInsurance[]
  company          Company?                      @relation(fields: [companyId], references: [id])
  personInsurances PersonInsurance[]
  serviceLimits    PersonInsuranceServiceLimit[]
  recurrences      Recurrence[]
  schedulings      Scheduling[]

  @@unique([name, companyId])
  @@index([companyId])
}

model WorkingHours {
  id                String   @id @default(uuid())
  userId            String
  dayOfWeek         Int
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  breakEndMinutes   Int?
  breakStartMinutes Int?
  endTimeMinutes    Int
  startTimeMinutes  Int
  user              User     @relation(fields: [userId], references: [id])

  @@index([userId, dayOfWeek])
  @@index([isActive])
}

model Company {
  id                       String                    @id @default(uuid())
  name                     String
  tradingName              String?
  legalName                String?
  industry                 String?
  contactEmail             String?
  cnpj                     String                    @unique
  phone                    String?
  privacyPolicyUrl         String?
  termsOfServiceUrl        String?
  phone2                   String?
  address                  String?
  city                     String?
  state                    String?
  postalCode               String?
  website                  String?
  primaryColor             String?
  secondaryColor           String?
  description              String?
  socialMedia              Json?
  businessHours            Json?
  active                   Boolean                   @default(true)
  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @updatedAt
  defaultCurrency          String                    @default("BRL")
  deletedAt                DateTime?
  licenseValidUntil        DateTime?
  plan                     String?
  timeZone                 String                    @default("America/Sao_Paulo")
  branches                 Branch[]
  Client                   Client[]
  conversations            Conversation[]
  curriculumFolders        CurriculumFolder[]
  curriculumFolderPrograms CurriculumFolderProgram[]
  documents                Document[]
  emailConfigs             EmailConfig[]
  evaluations              Evaluation[]
  insurances               Insurance[]
  locations                Location[]
  professions              Profession[]
  professionGroups         ProfessionGroup[]
  programs                 Program[]
  anamneses                Anamnese[]
  evolucoesDiarias         EvolucaoDiaria[]
  arquivosEvolucaoDiaria   ArquivoEvolucaoDiaria[]
  recurrences              Recurrence[]
  schedulings              Scheduling[]
  serviceTypes             ServiceType[]
  skills                   Skill[]
  standardCriteria         StandardCriteria[]
  subscription             Subscription?
  users                    User[]

  @@index([active])
}

model EmailConfig {
  id               String   @id @default(uuid())
  companyId        String
  smtpHost         String
  smtpPort         Int
  smtpSecure       Boolean  @default(false)
  smtpUser         String
  smtpPassword     String
  emailFromName    String
  emailFromAddress String
  active           Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  company          Company  @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
}

model Subscription {
  id              String    @id @default(uuid())
  companyId       String    @unique
  plan            String
  pricePerMonth   Decimal   @db.Decimal(10, 2)
  billingCycle    String
  startDate       DateTime
  endDate         DateTime?
  autoRenew       Boolean   @default(true)
  status          String
  lastBillingDate DateTime?
  nextBillingDate DateTime?
  maxUsers        Int?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  company         Company   @relation(fields: [companyId], references: [id])

  @@index([status])
}

model AuditLog {
  id         String   @id @default(uuid())
  userId     String
  action     String
  entityType String
  entityId   String
  details    Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  companyId  String?
  user       User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([entityType, entityId])
  @@index([companyId])
  @@index([createdAt])
}

model PasswordReset {
  id        String    @id @default(uuid())
  userId    String
  token     String    @unique
  expiresAt DateTime
  usedAt    DateTime?
  createdAt DateTime  @default(now())

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

model Branch {
  id                  String         @id @default(uuid())
  name                String
  code                String?
  description         String?
  address             String
  city                String?
  state               String?
  postalCode          String?
  phone               String?
  email               String?
  active              Boolean        @default(true)
  isHeadquarters      Boolean        @default(false)
  deletedAt           DateTime?
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @updatedAt
  companyId           String
  neighborhood        String?
  defaultWorkingHours Json?
  company             Company        @relation(fields: [companyId], references: [id])
  conversations       Conversation[]
  locations           Location[]
  recurrences         Recurrence[]
  schedulings         Scheduling[]
  users               User[]

  @@unique([companyId, code])
  @@index([companyId])
  @@index([active])
}

model Contact {
  id           String   @id @default(uuid())
  name         String
  relationship String?
  email        String?
  phone        String?
  notes        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  personId     String
  createdById  String
  deletedById  String?
  createdBy    User     @relation("CreatedContacts", fields: [createdById], references: [id])
  deletedBy    User?    @relation("DeletedContacts", fields: [deletedById], references: [id])
  person       Person   @relation("PersonContacts", fields: [personId], references: [id])

  @@index([personId])
}

model ClientInsurance {
  clientId     String
  insuranceId  String
  notes        String?
  policyNumber String?
  validUntil   DateTime?
  Client       Client    @relation(fields: [clientId], references: [id])
  Insurance    Insurance @relation(fields: [insuranceId], references: [id])

  @@id([clientId, insuranceId])
  @@index([insuranceId])
}

model PersonInsurance {
  personId     String
  insuranceId  String
  policyNumber String?
  validUntil   DateTime?
  notes        String?
  insurance    Insurance @relation(fields: [insuranceId], references: [id])
  person       Person    @relation(fields: [personId], references: [id])

  @@id([personId, insuranceId])
  @@index([insuranceId])
}

model PersonInsuranceServiceLimit {
  id            String      @id
  personId      String
  insuranceId   String
  serviceTypeId String
  monthlyLimit  Int
  yearlyLimit   Int         @default(0)
  notes         String?
  Insurance     Insurance   @relation(fields: [insuranceId], references: [id])
  Person        Person      @relation(fields: [personId], references: [id])
  ServiceType   ServiceType @relation(fields: [serviceTypeId], references: [id])

  @@unique([personId, insuranceId, serviceTypeId])
  @@index([personId, insuranceId])
  @@index([serviceTypeId])
}

model Conversation {
  id            String                    @id @default(uuid())
  type          ConversationType
  title         String?
  createdAt     DateTime                  @default(now())
  updatedAt     DateTime                  @updatedAt
  companyId     String
  branchId      String?
  lastMessageAt DateTime?
  isActive      Boolean                   @default(true)
  createdById   String
  branch        Branch?                   @relation(fields: [branchId], references: [id])
  company       Company                   @relation(fields: [companyId], references: [id])
  createdBy     User                      @relation("ConversationCreator", fields: [createdById], references: [id])
  participants  ConversationParticipant[]
  messages      Message[]

  @@index([companyId])
  @@index([branchId])
  @@index([isActive])
  @@index([lastMessageAt])
}

model ConversationParticipant {
  id                String          @id @default(uuid())
  conversationId    String
  userId            String
  joinedAt          DateTime        @default(now())
  leftAt            DateTime?
  isAdmin           Boolean         @default(false)
  lastReadMessageId String?
  conversation      Conversation    @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  lastReadMessage   Message?        @relation("LastReadMessage", fields: [lastReadMessageId], references: [id])
  user              User            @relation(fields: [userId], references: [id])
  messageStatuses   MessageStatus[]

  @@unique([conversationId, userId])
  @@index([userId])
  @@index([conversationId])
}

model Message {
  id                  String                    @id @default(uuid())
  conversationId      String
  senderId            String
  content             String
  contentType         MessageContentType        @default(TEXT)
  createdAt           DateTime                  @default(now())
  updatedAt           DateTime                  @updatedAt
  isDeleted           Boolean                   @default(false)
  referencedMessageId String?
  metadata            Json?
  readByParticipants  ConversationParticipant[] @relation("LastReadMessage")
  conversation        Conversation              @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  referencedMessage   Message?                  @relation("MessageReference", fields: [referencedMessageId], references: [id])
  referencingMessages Message[]                 @relation("MessageReference")
  sender              User                      @relation(fields: [senderId], references: [id])
  statuses            MessageStatus[]

  @@index([conversationId])
  @@index([senderId])
  @@index([createdAt])
  @@index([conversationId, createdAt])
}

model MessageStatus {
  id            String                  @id @default(uuid())
  messageId     String
  participantId String
  status        MessageDeliveryStatus
  timestamp     DateTime                @default(now())
  message       Message                 @relation(fields: [messageId], references: [id], onDelete: Cascade)
  participant   ConversationParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@unique([messageId, participantId])
  @@index([messageId])
  @@index([participantId])
}

model Evaluation {
  id           String            @id @default(uuid())
  type         EvaluationType
  name         String
  observations String?
  active       Boolean           @default(true)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  deletedAt    DateTime?
  companyId    String
  createdById  String
  company      Company           @relation(fields: [companyId], references: [id])
  createdBy    User              @relation("EvaluationCreator", fields: [createdById], references: [id])
  skills       EvaluationSkill[]
  levels       Level[]
  scores       Score[]
  tasks        Task[]

  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([type])
}

model Level {
  id           String     @id @default(uuid())
  order        Int
  description  String
  ageRange     String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  tasks        Task[]

  @@index([evaluationId])
  @@index([order])
}

model Skill {
  id          String            @id @default(uuid())
  code        String?
  order       Int
  description String
  active      Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  deletedAt   DateTime?
  companyId   String
  evaluations EvaluationSkill[]
  company     Company           @relation(fields: [companyId], references: [id])
  tasks       Task[]

  @@index([companyId])
  @@index([active])
  @@index([order])
}

model EvaluationSkill {
  evaluationId String
  skillId      String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  skill        Skill      @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@id([evaluationId, skillId])
  @@index([evaluationId])
  @@index([skillId])
}

model Score {
  id           String     @id @default(uuid())
  type         ScoreType
  value        String?
  description  String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)

  @@index([evaluationId])
  @@index([type])
}

model Task {
  id           String     @id @default(uuid())
  order        Int
  name         String
  milestone    String?
  item         String?
  question     String?
  example      String?
  criteria     String?
  objective    String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  skillId      String?
  levelId      String?
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  level        Level?     @relation(fields: [levelId], references: [id])
  skill        Skill?     @relation(fields: [skillId], references: [id])

  @@index([evaluationId])
  @@index([skillId])
  @@index([levelId])
  @@index([order])
}

model StandardCriteria {
  id           String         @id @default(uuid())
  teachingType TeachingType
  acronym      String
  degree       CriteriaDegree
  active       Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  deletedAt    DateTime?
  companyId    String
  company      Company        @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
  @@index([teachingType])
  @@index([degree])
}

model Program {
  id                  String          @id @default(uuid())
  type                ProgramType
  name                String
  protocol            String?
  skill               String?
  milestone           String?
  teachingType        String?
  targetsPerSession   Int?            @default(1)
  attemptsPerTarget   Int?            @default(1)
  teachingProcedure   String?         @default("")
  instruction         String?         @default("")
  objective           String?         @default("")
  correctionProcedure String?         @default("")
  learningCriteria    String?         @default("")
  materials           String?         @default("")
  notes               String?         @default("")
  active              Boolean         @default(true)
  createdAt           DateTime        @default(now())
  updatedAt           DateTime        @updatedAt
  deletedAt           DateTime?
  companyId           String
  createdById         String
  promptStep          String?         @default("")
  company             Company         @relation(fields: [companyId], references: [id])
  createdBy           User            @relation("ProgramCreator", fields: [createdById], references: [id])
  targets             ProgramTarget[]

  @@index([companyId])
  @@index([active])
  @@index([type])
  @@index([createdById])
}

model ProgramTarget {
  id               String    @id @default(uuid())
  target           String
  order            Int
  group            String?
  situation        String?   @default("ACTIVE")
  startDate        DateTime?
  acquisitionDate  DateTime?
  maintenanceCount Int?      @default(0)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  programId        String
  program          Program   @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@index([programId])
  @@index([order])
}

model CurriculumFolder {
  id               String                    @id @default(uuid())
  name             String
  personId         String
  shareWithParents Boolean                   @default(false)
  shareWithSchools Boolean                   @default(false)
  active           Boolean                   @default(true)
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt
  deletedAt        DateTime?
  companyId        String
  createdById      String
  company          Company                   @relation(fields: [companyId], references: [id])
  createdBy        User                      @relation("CurriculumFolderCreator", fields: [createdById], references: [id])
  person           Person                    @relation(fields: [personId], references: [id])
  programs         CurriculumFolderProgram[]

  @@index([companyId])
  @@index([active])
  @@index([personId])
  @@index([createdById])
}

model CurriculumFolderProgram {
  id                  String           @id @default(uuid())
  name                String
  type                String
  protocol            String?
  skill               String?
  milestone           String?
  teachingType        String?
  targetsPerSession   Int?             @default(1)
  attemptsPerTarget   Int?             @default(1)
  teachingProcedure   String?          @default("")
  instruction         String?          @default("")
  objective           String?          @default("")
  promptStep          String?          @default("")
  correctionProcedure String?          @default("")
  learningCriteria    String?          @default("")
  materials           String?          @default("")
  notes               String?          @default("")
  status              ProgramStatus    @default(unallocated)
  originalProgramId   String?
  active              Boolean          @default(true)
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  deletedAt           DateTime?
  curriculumFolderId  String
  companyId           String
  createdById         String
  company             Company          @relation(fields: [companyId], references: [id])
  createdBy           User             @relation("CurriculumFolderProgramCreator", fields: [createdById], references: [id])
  curriculumFolder    CurriculumFolder @relation(fields: [curriculumFolderId], references: [id])

  @@index([curriculumFolderId])
  @@index([companyId])
  @@index([active])
  @@index([status])
  @@index([createdById])
  @@index([originalProgramId])
}

// Enum para os campos Sim, Não, Às vezes
enum SimNaoAsVezes {
  SIM
  NAO
  AS_VEZES
}

model Anamnese {
  id                                  String         @id @default(uuid())
  date                                DateTime
  personId                            String
  diagnostico                         String?        @db.Text
  cuidador                            String?        @db.Text
  profissaoCuidador                   String?
  telefone                            String?
  historicoPersonal                   String?        @db.Text
  patologiaAssociada                  String?        @db.Text
  convulsoes                          String?        @db.Text
  sentou                              String?
  engatinhou                          String?
  andou                               String?
  estereotipiasMotoras                String?        @db.Text
  alimentacaoSolidos                  Boolean        @default(false)
  alimentacaoLiquidos                 Boolean        @default(false)
  alimentacaoPastosos                 Boolean        @default(false)
  alergiasIntolerancias               String?        @db.Text
  // Campos adicionais para informações médicas
  historicoMedico                     String?        @db.Text
  medicacoes                          String?        @db.Text
  alergias                            String?        @db.Text
  historicoFamiliar                   String?        @db.Text
  // AVD
  avdAlimentacao                      String?        @db.Text
  avdBanho                            String?        @db.Text
  avdVestuario                        String?        @db.Text
  avdCuidadosPessoais                 String?        @db.Text
  avdSono                             String?        @db.Text
  avdEsfincter                        String?        @db.Text
  // Desenvolvimento da Linguagem - Não Verbal
  gestosElementares                   SimNaoAsVezes? @default(NAO)
  naoSimbolicosConvencionais          SimNaoAsVezes? @default(NAO)
  simbolicosRepresentacao             SimNaoAsVezes? @default(NAO)
  // Desenvolvimento da Linguagem - Verbal
  verbal                              SimNaoAsVezes? @default(NAO)
  balbucio                            SimNaoAsVezes? @default(NAO)
  palavrasIsoladas                    SimNaoAsVezes? @default(NAO)
  quaisPalavrasIsoladas               String?        @db.Text
  enunciadoDuasPalavras               SimNaoAsVezes? @default(NAO)
  frases                              SimNaoAsVezes? @default(NAO)
  estereotipiasVocais                 SimNaoAsVezes? @default(NAO)
  quaisEstereotipiasVocais            String?        @db.Text
  // Interação Social
  faltaExpressaoFacialAdequada        SimNaoAsVezes? @default(NAO)
  apresentaAtencaoDiminuida           SimNaoAsVezes? @default(NAO)
  apresentaPreferenciaIsolamento      SimNaoAsVezes? @default(NAO)
  ageComoSeFosseSurdo                 SimNaoAsVezes? @default(NAO)
  olhaParaAlguemQueLheFala            SimNaoAsVezes? @default(NAO)
  olhaQuandoChamadoPeloNome           SimNaoAsVezes? @default(NAO)
  fazPedidoItensInteresse             SimNaoAsVezes? @default(NAO)
  realizaImitacao                     SimNaoAsVezes? @default(NAO)
  brincaAdequadamenteBrinquedo        SimNaoAsVezes? @default(NAO)
  preferenciasObjetosEspecificos      String?        @db.Text
  apresentaAversoes                   String?        @db.Text
  autoEstimulacao                     Boolean?       @default(false)
  apresentaAutoAgressaoHeteroAgressao Boolean?       @default(false)
  apresentaBirrasIrritabilidade       Boolean?       @default(false)
  apresentaManiasRituais              Boolean?       @default(false)
  // Escola
  estuda                              Boolean?       @default(false)
  nomeEscola                          String?
  serie                               String?
  escolaRegular                       Boolean?       @default(false)
  professorApoio                      Boolean?       @default(false)
  // Outros
  outroCasoFamilia                    Boolean?       @default(false)
  outrosCasosDetalhamento             String?        @db.Text
  terapias                            String?        @db.Text
  expectativasFamilia                 String?        @db.Text
  observacoesGerais                   String?        @db.Text
  // Campos para controle de versão
  versao                              Int            @default(1)
  ultimaAtualizacaoPorId              String?
  // Campos padrão
  active                              Boolean        @default(true)
  createdAt                           DateTime       @default(now())
  updatedAt                           DateTime       @updatedAt
  deletedAt                           DateTime?
  companyId                           String
  createdById                         String
  company                             Company        @relation(fields: [companyId], references: [id])
  createdBy                           User           @relation("AnamneseCreator", fields: [createdById], references: [id])
  person                              Person         @relation(fields: [personId], references: [id])
  ultimaAtualizacaoPor                User?          @relation("AnamneseUltimaAtualizacao", fields: [ultimaAtualizacaoPorId], references: [id])

  @@index([personId])
  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([ultimaAtualizacaoPorId])
  @@index([versao])
}

// Enum para status da evolução diária
enum StatusEvolucaoDiaria {
  RASCUNHO
  FINALIZADA
}

// Modelo para Evolução Diária
model EvolucaoDiaria {
  id                   String               @id @default(uuid())
  personId             String // Aprendiz/Paciente
  profissionalId       String // Profissional responsável
  dataInicio           DateTime // Data e hora de início do atendimento
  dataFim              DateTime // Data e hora de fim do atendimento
  faltou               Boolean              @default(false) // Se o aprendiz faltou
  permitirVisualizacao Boolean              @default(false) // Permitir visualização pelos responsáveis
  atendimento          String?              @db.Text // Resumo do atendimento
  observacoes          String?              @db.Text // Observações adicionais
  status               StatusEvolucaoDiaria @default(RASCUNHO) // Status da evolução

  // Campos para controle de versão
  versao                 Int     @default(1)
  ultimaAtualizacaoPorId String?

  // Campos padrão
  active      Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  companyId   String
  createdById String

  // Relacionamentos
  person               Person                  @relation(fields: [personId], references: [id])
  profissional         User                    @relation("EvolucaoDiariaProfissional", fields: [profissionalId], references: [id])
  company              Company                 @relation(fields: [companyId], references: [id])
  createdBy            User                    @relation("EvolucaoDiariaCreator", fields: [createdById], references: [id])
  ultimaAtualizacaoPor User?                   @relation("EvolucaoDiariaUltimaAtualizacao", fields: [ultimaAtualizacaoPorId], references: [id])
  arquivos             ArquivoEvolucaoDiaria[]

  @@index([personId])
  @@index([profissionalId])
  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([ultimaAtualizacaoPorId])
  @@index([status])
  @@index([dataInicio])
  @@index([faltou])
}

// Modelo para Arquivos da Evolução Diária
model ArquivoEvolucaoDiaria {
  id               String @id @default(uuid())
  evolucaoDiariaId String
  nome             String
  tipo             String // Tipo/extensão do arquivo
  tamanho          Int // Tamanho em bytes
  url              String // URL para acessar o arquivo

  // Campos padrão
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  companyId   String
  createdById String

  // Relacionamentos
  evolucaoDiaria EvolucaoDiaria @relation(fields: [evolucaoDiariaId], references: [id])
  company        Company        @relation(fields: [companyId], references: [id])
  createdBy      User           @relation(fields: [createdById], references: [id])

  @@index([evolucaoDiariaId])
  @@index([companyId])
  @@index([createdById])
}

enum SystemModule {
  ADMIN
  RH
  FINANCIAL
  SCHEDULING
  BASIC
}

enum SchedulingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}

enum RecurrenceType {
  OCCURRENCES
  END_DATE
}

enum UserRole {
  SYSTEM_ADMIN
  COMPANY_ADMIN
  EMPLOYEE
}

enum ConversationType {
  INDIVIDUAL
  GROUP
}

enum MessageContentType {
  TEXT
  IMAGE
  FILE
  LINK
  SYSTEM
}

enum MessageDeliveryStatus {
  SENT
  DELIVERED
  READ
}

enum EvaluationType {
  SKILL_ACQUISITION
  BEHAVIOR_REDUCTION
}

enum ScoreType {
  ALWAYS
  FREQUENTLY
  SOMETIMES
  RARELY
  NEVER
  NOT_APPLICABLE
}

enum TeachingType {
  DISCRETE_TRIAL_STRUCTURED
  TASK_ANALYSIS
  NATURALISTIC_TEACHING
  DISCRETE_TRIAL_INTERSPERSED
}

enum CriteriaDegree {
  OMISSION
  ERROR
  MORE_INTRUSIVE
  PARTIALLY_INTRUSIVE
  LESS_INTRUSIVE
  INDEPENDENT
}

enum ProgramType {
  PROGRAM_CATALOG
  LEARNING_PROGRAM
}

enum ProgramStatus {
  unallocated
  inTraining
  completed
}