// scripts/run-base-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed base (empresas e usuário admin)...');

try {
  // Executar o script de seed
  execSync('node prisma/seed.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed base executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed base:', error);
  process.exit(1);
}
