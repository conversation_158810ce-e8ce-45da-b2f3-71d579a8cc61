module.exports = {

"[project]/src/components/ui/ExportMenu.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-ssr] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js [app-ssr] (ecmascript) <export default as FileSpreadsheet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript) <export default as ConstructionButton>");
"use client";
;
;
;
;
;
;
const ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' })=>{
    const [dropdownOpen, setDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dropdownPosition, setDropdownPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        top: 0,
        right: 0,
        width: 0
    });
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Montar o componente apenas no cliente
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setMounted(true);
        return ()=>setMounted(false);
    }, []);
    // Calcular a posição do dropdown quando aberto
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (dropdownOpen && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownPosition({
                top: rect.bottom + window.scrollY,
                right: window.innerWidth - rect.right,
                width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)
            });
        }
    }, [
        dropdownOpen
    ]);
    // Fecha o dropdown ao clicar fora dele
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (buttonRef.current && !buttonRef.current.contains(event.target) && dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setDropdownOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    const handleExport = (format)=>{
        onExport(format);
        setDropdownOpen(false);
    };
    // Se estiver em construção, mostrar o botão de construção
    if (underConstruction) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
            className: "flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors",
            title: "Exportação em Construção",
            content: "A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.",
            icon: "FileText",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                    size: 16
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: "Exportar"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                    size: 14
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 69,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/ExportMenu.js",
            lineNumber: 61,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                ref: buttonRef,
                onClick: ()=>setDropdownOpen(!dropdownOpen),
                className: `flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`,
                disabled: isExporting || disabled && !underConstruction,
                title: disabled ? "Não há dados para exportar" : "Exportar dados",
                children: [
                    isExporting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                        size: 16,
                        className: "animate-spin"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 84,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: isExporting ? "Exportando..." : "Exportar"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                        size: 14,
                        className: `transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 76,
                columnNumber: 7
            }, this),
            dropdownOpen && mounted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropdownRef,
                className: "fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700",
                style: {
                    top: `${dropdownPosition.top}px`,
                    right: `${dropdownPosition.right}px`,
                    width: `${dropdownPosition.width}px`
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-sm font-medium text-neutral-700 dark:text-gray-200",
                            children: "Formato de exportação"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ExportMenu.js",
                            lineNumber: 104,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 103,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('image'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                        size: 16,
                                        className: "text-blue-500 dark:text-blue-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Imagem (PNG)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('pdf'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                        size: 16,
                                        className: "text-red-500 dark:text-red-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "PDF"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 119,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('xlsx'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__["FileSpreadsheet"], {
                                        size: 16,
                                        className: "text-green-500 dark:text-green-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Excel (XLSX)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 126,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 94,
                columnNumber: 9
            }, this), document.body)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/ExportMenu.js",
        lineNumber: 75,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ExportMenu;
}}),
"[project]/src/components/ui/ModuleMaskedInput.js [app-ssr] (ecmascript) <export default as ModuleMaskedInput>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleMaskedInput": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleMaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleMaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleMaskedInput.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleFormGroup.js [app-ssr] (ecmascript) <export default as ModuleFormGroup>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleFormGroup": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleFormGroup.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/multi-select.js [app-ssr] (ecmascript) <export default as MultiSelect>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "MultiSelect": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/multi-select.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleTable.js [app-ssr] (ecmascript) <export default as ModuleTable>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleTable": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleTable.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleHeader.js [app-ssr] (ecmascript) <export default as ModuleHeader>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleHeader": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleHeader.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModalButton.js [app-ssr] (ecmascript) <export default as ModalButton>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModalButton": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModalButton.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/Modal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
'use client';
;
;
;
;
const Modal = ({ isOpen, onClose, title, children, size = 'md' })=>{
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Montar o componente apenas no cliente
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setMounted(true);
        return ()=>setMounted(false);
    }, []);
    // Efeito para prevenir scroll quando o modal estiver aberto
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isOpen) {
            document.body.style.overflow = 'hidden';
        } else {
            document.body.style.overflow = '';
        }
        return ()=>{
            document.body.style.overflow = '';
        };
    }, [
        isOpen
    ]);
    if (!isOpen || !mounted) return null;
    // Determinar a largura do modal com base no tamanho
    const sizeClasses = {
        sm: 'max-w-md',
        md: 'max-w-2xl',
        lg: 'max-w-4xl',
        xl: 'max-w-6xl',
        full: 'max-w-full mx-4'
    };
    // Usar createPortal para renderizar o modal no nível mais alto do DOM
    const modalContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black/50",
                onClick: onClose
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Modal.js",
                lineNumber: 43,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 ${sizeClasses[size]} w-full max-h-[90vh] flex flex-col z-[11050]`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold text-neutral-800 dark:text-white",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/Modal.js",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: onClose,
                                className: "text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                    size: 20
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/Modal.js",
                                    lineNumber: 55,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/Modal.js",
                                lineNumber: 51,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/Modal.js",
                        lineNumber: 47,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-y-auto flex-1",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/Modal.js",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/Modal.js",
                lineNumber: 45,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/Modal.js",
        lineNumber: 41,
        columnNumber: 5
    }, this);
    // Renderizar o modal usando um portal para garantir que ele fique acima de tudo
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(modalContent, document.body);
};
const __TURBOPACK__default__export__ = Modal;
}}),
"[project]/src/components/ui/ModuleTabs.js [app-ssr] (ecmascript) <export default as ModuleTabs>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleTabs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTabs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTabs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleTabs.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleLabel.js [app-ssr] (ecmascript) <export default as ModuleLabel>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleLabel": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleLabel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleLabel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleLabel.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/permissions/Protected.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AdminOnly": (()=>AdminOnly),
    "Protected": (()=>Protected),
    "ProtectedModule": (()=>ProtectedModule)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/usePermissions.js [app-ssr] (ecmascript)");
'use client';
;
;
function Protected({ permission, requireAll = false, fallback = null, children }) {
    const { can, canAll, canAny } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"])();
    const hasAccess = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>{
        if (!permission) return true;
        if (Array.isArray(permission)) {
            return requireAll ? canAll(permission) : canAny(permission);
        }
        return can(permission);
    }, [
        permission,
        requireAll,
        can,
        canAll,
        canAny
    ]);
    return hasAccess ? children : fallback;
}
function ProtectedModule({ module, requireAll = false, fallback = null, children }) {
    const { hasModule, isAdmin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"])();
    const hasAccess = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useMemo(()=>{
        if (isAdmin()) return true;
        if (!module) return true;
        if (Array.isArray(module)) {
            return requireAll ? module.every((m)=>hasModule(m)) : module.some((m)=>hasModule(m));
        }
        return hasModule(module);
    }, [
        module,
        requireAll,
        hasModule,
        isAdmin
    ]);
    return hasAccess ? children : fallback;
}
function AdminOnly({ fallback = null, children }) {
    const { isAdmin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$usePermissions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"])();
    return isAdmin() ? children : fallback;
}
}}),
"[project]/src/components/permissions/PermissionsModal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$userService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/admin/services/userService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/permissionConfig.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-ssr] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckSquare$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/square-check-big.js [app-ssr] (ecmascript) <export default as CheckSquare>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-ssr] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
"use client";
;
;
;
;
;
;
;
const PermissionsModal = ({ isOpen, onClose, user, onSuccess })=>{
    const { user: currentUser } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const [selectedPermissions, setSelectedPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [expandedModules, setExpandedModules] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [filteredPermissions, setFilteredPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Carregar permissões do usuário ao abrir o modal
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (user && isOpen) {
            setIsLoading(true);
            // Se usuário já tem módulos/permissões, configuramos o estado inicial
            if (user.permissions) {
                setSelectedPermissions(user.permissions);
            } else {
                // Se não tiver permissões, inicializamos com array vazio
                setSelectedPermissions([]);
            }
            // Inicialmente, expandimos apenas os módulos que o usuário tem acesso
            const initialExpandedState = {};
            user.modules?.forEach((moduleId)=>{
                initialExpandedState[moduleId] = true;
            });
            setExpandedModules(initialExpandedState);
            setIsLoading(false);
        }
    }, [
        user,
        isOpen
    ]);
    // Filtragem de permissões baseada na busca
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!searchTerm.trim()) {
            setFilteredPermissions((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllPermissions"])());
            return;
        }
        const lowerSearch = searchTerm.toLowerCase();
        const filtered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllPermissions"])().filter((permission)=>permission.name.toLowerCase().includes(lowerSearch) || permission.description.toLowerCase().includes(lowerSearch) || permission.id.toLowerCase().includes(lowerSearch) || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_CONFIG"][permission.moduleId].name.toLowerCase().includes(lowerSearch));
        setFilteredPermissions(filtered);
        // Expande automaticamente os módulos que têm permissões que correspondem à busca
        const modulesToExpand = {};
        filtered.forEach((permission)=>{
            modulesToExpand[permission.moduleId] = true;
        });
        setExpandedModules((prev)=>({
                ...prev,
                ...modulesToExpand
            }));
    }, [
        searchTerm
    ]);
    const isAdmin = currentUser?.modules?.includes("ADMIN");
    // Verificar se o usuário tem uma permissão específica
    const hasPermission = (permissionId)=>{
        return selectedPermissions.includes(permissionId);
    };
    // Alternar uma permissão específica
    const togglePermission = (permissionId)=>{
        setSelectedPermissions((prev)=>{
            if (prev.includes(permissionId)) {
                return prev.filter((id)=>id !== permissionId);
            } else {
                return [
                    ...prev,
                    permissionId
                ];
            }
        });
    };
    // Alternar todas as permissões de um módulo
    const toggleModulePermissions = (moduleId)=>{
        const modulePermissions = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_CONFIG"][moduleId].permissions.map((p)=>p.id);
        // Verificar se todas as permissões do módulo já estão selecionadas
        const allSelected = modulePermissions.every((p)=>selectedPermissions.includes(p));
        if (allSelected) {
            // Remover todas as permissões do módulo
            setSelectedPermissions((prev)=>prev.filter((p)=>!modulePermissions.includes(p)));
        } else {
            // Adicionar todas as permissões do módulo
            setSelectedPermissions((prev)=>{
                const newPermissions = [
                    ...prev
                ];
                modulePermissions.forEach((p)=>{
                    if (!newPermissions.includes(p)) {
                        newPermissions.push(p);
                    }
                });
                return newPermissions;
            });
        }
    };
    // Alternar a expansão de um módulo
    const toggleModuleExpansion = (moduleId)=>{
        setExpandedModules((prev)=>({
                ...prev,
                [moduleId]: !prev[moduleId]
            }));
    };
    // Salvar permissões
    const handleSave = async ()=>{
        setIsSaving(true);
        setError("");
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$userService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userService"].updatePermissions(user.id, selectedPermissions);
            onSuccess();
        } catch (error) {
            console.error("Erro ao atualizar permissões:", error);
            setError(error.response?.data?.message || "Erro ao atualizar permissões");
        } finally{
            setIsSaving(false);
        }
    };
    // Obter o ícone do módulo
    const getModuleIcon = (moduleId)=>{
        const icons = {
            ADMIN: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                className: "h-5 w-5"
            }, void 0, false, {
                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                lineNumber: 169,
                columnNumber: 14
            }, this),
            RH: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                className: "h-5 w-5"
            }, void 0, false, {
                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                lineNumber: 170,
                columnNumber: 11
            }, this),
            FINANCIAL: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                className: "h-5 w-5"
            }, void 0, false, {
                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                lineNumber: 171,
                columnNumber: 18
            }, this),
            SCHEDULING: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                className: "h-5 w-5"
            }, void 0, false, {
                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                lineNumber: 172,
                columnNumber: 19
            }, this),
            BASIC: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckSquare$3e$__["CheckSquare"], {
                className: "h-5 w-5"
            }, void 0, false, {
                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                lineNumber: 173,
                columnNumber: 14
            }, this)
        };
        return icons[moduleId] || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
            className: "h-5 w-5"
        }, void 0, false, {
            fileName: "[project]/src/components/permissions/PermissionsModal.js",
            lineNumber: 176,
            columnNumber: 31
        }, this);
    };
    // Renderizar as permissões de um módulo
    const renderModulePermissions = (moduleId)=>{
        const module = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$permissionConfig$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PERMISSIONS_CONFIG"][moduleId];
        if (!module) return null;
        // Se estiver filtrando, mostrar apenas as permissões que correspondem à busca
        const permissions = searchTerm ? module.permissions.filter((p)=>filteredPermissions.some((fp)=>fp.id === p.id)) : module.permissions;
        if (permissions.length === 0) return null;
        const allPermissionsSelected = permissions.every((p)=>selectedPermissions.includes(p.id));
        const somePermissionsSelected = permissions.some((p)=>selectedPermissions.includes(p.id));
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "mb-6 border rounded-lg overflow-hidden dark:border-gray-700",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between cursor-pointer border-b dark:border-gray-700",
                    onClick: ()=>toggleModuleExpansion(moduleId),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `p-2 rounded-full ${somePermissionsSelected ? allPermissionsSelected ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400" : "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400" : "bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400"}`,
                                    children: getModuleIcon(moduleId)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                    lineNumber: 209,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-medium text-neutral-800 dark:text-gray-200",
                                            children: module.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                            lineNumber: 221,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-neutral-500 dark:text-gray-400",
                                            children: somePermissionsSelected ? `${selectedPermissions.filter((p)=>module.permissions.some((mp)=>mp.id === p)).length} de ${permissions.length} permissões selecionadas` : "Nenhuma permissão selecionada"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                            lineNumber: 222,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                    lineNumber: 220,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                            lineNumber: 208,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: (e)=>{
                                        e.stopPropagation();
                                        toggleModulePermissions(moduleId);
                                    },
                                    className: `px-3 py-1 rounded text-sm font-medium ${allPermissionsSelected ? "bg-neutral-200 text-neutral-700 hover:bg-neutral-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600" : "bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700"}`,
                                    children: allPermissionsSelected ? "Desmarcar todas" : "Selecionar todas"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                    lineNumber: 235,
                                    columnNumber: 13
                                }, this),
                                expandedModules[moduleId] ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                    className: "text-neutral-600 dark:text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                    lineNumber: 251,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                    className: "text-neutral-600 dark:text-gray-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                    lineNumber: 253,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                            lineNumber: 234,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                    lineNumber: 204,
                    columnNumber: 9
                }, this),
                expandedModules[moduleId] && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-4 divide-y dark:divide-gray-700 dark:bg-gray-850",
                    children: permissions.map((permission)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-3 first:pt-0 last:pb-0",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-start gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-shrink-0 mt-0.5",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "checkbox",
                                            id: permission.id,
                                            checked: hasPermission(permission.id),
                                            onChange: ()=>togglePermission(permission.id),
                                            className: "h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                            lineNumber: 265,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                        lineNumber: 264,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: permission.id,
                                                className: "block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer",
                                                children: permission.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                                lineNumber: 275,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "mt-1 text-sm text-neutral-600 dark:text-gray-400",
                                                children: permission.description
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                                lineNumber: 281,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-1 text-xs text-neutral-500 dark:text-gray-500",
                                                children: [
                                                    "ID: ",
                                                    permission.id
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                                lineNumber: 284,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                        lineNumber: 274,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/permissions/PermissionsModal.js",
                                lineNumber: 263,
                                columnNumber: 17
                            }, this)
                        }, permission.id, false, {
                            fileName: "[project]/src/components/permissions/PermissionsModal.js",
                            lineNumber: 262,
                            columnNumber: 15
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/permissions/PermissionsModal.js",
                    lineNumber: 260,
                    columnNumber: 11
                }, this)
            ]
        }, moduleId, true, {
            fileName: "[project]/src/components/permissions/PermissionsModal.js",
            lineNumber: 202,
            columnNumber: 7
        }, this);
    };
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const __TURBOPACK__default__export__ = PermissionsModal;
}}),
"[project]/src/components/forms/UserProfileImageUpload.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$userService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/admin/services/userService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-ssr] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/trash.js [app-ssr] (ecmascript) <export default as Trash>");
'use client';
;
;
;
;
const UserProfileImageUpload = ({ userId, initialImageUrl, onImageUploaded, deferUpload = false, uploadRef = null, size = 'medium', disabled = false })=>{
    const [imageUrl, setImageUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialImageUrl || null);
    const [previewUrl, setPreviewUrl] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialImageUrl || null);
    const [selectedFile, setSelectedFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isUploading, setIsUploading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Tamanhos disponíveis
    const sizes = {
        small: {
            container: 'h-16 w-16',
            icon: 'h-4 w-4'
        },
        medium: {
            container: 'h-24 w-24',
            icon: 'h-6 w-6'
        },
        large: {
            container: 'h-32 w-32',
            icon: 'h-8 w-8'
        }
    };
    // Atualizar a URL da imagem quando a prop initialImageUrl mudar
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (initialImageUrl) {
            setImageUrl(initialImageUrl);
            setPreviewUrl(initialImageUrl);
        }
    }, [
        initialImageUrl
    ]);
    // Expor o método de upload para o componente pai
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (uploadRef) {
            uploadRef.current = {
                uploadSelectedImage: async ()=>{
                    console.log('Método uploadSelectedImage chamado');
                    console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');
                    console.log('ID do usuário:', userId);
                    if (selectedFile && userId) {
                        console.log('Iniciando upload do arquivo selecionado');
                        const result = await uploadImage(selectedFile);
                        console.log('Resultado do upload:', result);
                        return result;
                    }
                    console.log('Nenhum arquivo para upload ou ID do usuário ausente');
                    return null;
                },
                hasSelectedFile: ()=>{
                    const hasFile = !!selectedFile;
                    console.log('Verificando se há arquivo selecionado:', hasFile);
                    return hasFile;
                }
            };
        }
    }, [
        selectedFile,
        userId,
        uploadRef
    ]);
    const handleFileChange = (e)=>{
        const file = e.target.files[0];
        if (!file) return;
        // Verificar tipo de arquivo (apenas imagens)
        if (!file.type.startsWith('image/')) {
            setError('Por favor, selecione uma imagem válida');
            return;
        }
        // Limitar tamanho (2MB)
        if (file.size > 2 * 1024 * 1024) {
            setError('A imagem deve ter no máximo 2MB');
            return;
        }
        setError(null);
        setSelectedFile(file);
        console.log('Arquivo selecionado:', file.name, file.type, file.size);
        // Criar uma URL para pré-visualização
        const newPreviewUrl = URL.createObjectURL(file);
        setPreviewUrl(newPreviewUrl);
        // Se não estiver adiando o upload, fazer o upload imediatamente
        if (!deferUpload && userId) {
            uploadImage(file);
        } else {
            // Notificar o componente pai sobre a mudança de arquivo
            if (onImageUploaded) {
                onImageUploaded(null, file);
            }
        }
    };
    // Método para fazer o upload da imagem
    const uploadImage = async (file)=>{
        if (!file || !userId) {
            console.error('Upload cancelado: arquivo ou userId ausente', {
                file: !!file,
                userId
            });
            return null;
        }
        setIsUploading(true);
        console.log('Iniciando upload de imagem para usuário ID:', userId);
        console.log('Arquivo a ser enviado:', file.name, file.type, file.size);
        try {
            console.log('Chamando serviço de upload de imagem');
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$userService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["userService"].uploadProfileImage(userId, file);
            console.log('Upload de imagem concluído com sucesso');
            console.log('Resposta completa:', JSON.stringify(response));
            // Atualizar URL da imagem com timestamp para evitar cache
            const timestamp = new Date().getTime();
            // Usar a URL completa retornada pelo servidor
            const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;
            console.log('Nova URL da imagem:', newImageUrl);
            setImageUrl(newImageUrl);
            setSelectedFile(null); // Limpar o arquivo selecionado após o upload
            if (onImageUploaded) {
                onImageUploaded(newImageUrl);
            }
            return response;
        } catch (error) {
            console.error('Erro ao fazer upload da imagem:', error);
            setError(error.response?.data?.message || 'Erro ao fazer upload da imagem');
            return null;
        } finally{
            setIsUploading(false);
        }
    };
    const removeImage = ()=>{
        setImageUrl(null);
        setPreviewUrl(null);
        setSelectedFile(null);
        if (onImageUploaded) {
            onImageUploaded(null);
        }
    };
    const triggerFileInput = ()=>{
        if (!disabled && fileInputRef.current) {
            fileInputRef.current.click();
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col items-center",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative group",
                children: previewUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `relative ${sizes[size].container} overflow-hidden rounded-full border border-neutral-300 dark:border-gray-600`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                            src: previewUrl,
                            alt: "Imagem de perfil",
                            className: "h-full w-full object-cover"
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                            lineNumber: 162,
                            columnNumber: 13
                        }, this),
                        !disabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: removeImage,
                            className: "absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",
                            title: "Remover imagem",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                className: "h-3 w-3"
                            }, void 0, false, {
                                fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                                lineNumber: 174,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                            lineNumber: 168,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                    lineNumber: 161,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    onClick: triggerFileInput,
                    className: `${sizes[size].container} flex items-center justify-center rounded-full border-2 border-dashed border-neutral-300 dark:border-gray-600 ${!disabled ? 'cursor-pointer hover:border-primary-500 dark:hover:border-primary-400' : 'cursor-not-allowed opacity-70'}`,
                    children: isUploading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                        className: `${sizes[size].icon} text-neutral-400 dark:text-gray-500 animate-spin`
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                        lineNumber: 184,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                        className: `${sizes[size].icon} text-neutral-400 dark:text-gray-500`
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                        lineNumber: 186,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                    lineNumber: 179,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                lineNumber: 159,
                columnNumber: 7
            }, this),
            !disabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-2 flex flex-col items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: triggerFileInput,
                        disabled: isUploading,
                        className: "text-xs text-primary-600 dark:text-primary-400 hover:underline focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed",
                        children: previewUrl ? 'Alterar foto' : 'Adicionar foto'
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                        lineNumber: 194,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: fileInputRef,
                        type: "file",
                        accept: "image/*",
                        onChange: handleFileChange,
                        className: "hidden",
                        disabled: isUploading || disabled
                    }, void 0, false, {
                        fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                lineNumber: 193,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "mt-1 text-xs text-red-600 dark:text-red-400",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
                lineNumber: 214,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/forms/UserProfileImageUpload.js",
        lineNumber: 158,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = UserProfileImageUpload;
}}),
"[project]/src/components/common/MaskedInput.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
/**
 * Componente genérico para campos de entrada com máscara
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.type - Tipo de máscara: 'cpf', 'cnpj', 'phone', 'cep', ou 'custom'
 * @param {string} props.value - Valor atual do campo
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {string} props.name - Nome do campo
 * @param {string} props.id - ID do campo
 * @param {string} props.placeholder - Texto de placeholder
 * @param {string} props.className - Classes CSS adicionais
 * @param {boolean} props.disabled - Se o campo está desabilitado
 * @param {string} props.customMask - Máscara personalizada (quando type='custom')
 * @param {Object} props.inputProps - Propriedades adicionais para o input
 */ const MaskedInput = ({ type = 'text', value = '', onChange, name, id, placeholder, className = '', disabled = false, customMask, ...inputProps })=>{
    // Estado interno para controlar o valor formatado
    const [inputValue, setInputValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    // Atualiza o estado interno quando o valor externo muda
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (value !== undefined) {
            setInputValue(formatValue(value, type, customMask));
        }
    }, [
        value,
        type,
        customMask
    ]);
    // Função para aplicar a máscara ao valor
    const applyMask = (value, mask)=>{
        let maskedValue = '';
        let valueIndex = 0;
        for(let i = 0; i < mask.length && valueIndex < value.length; i++){
            const maskChar = mask[i];
            const valueChar = value[valueIndex];
            if (maskChar === '#') {
                // Apenas dígitos
                if (/\d/.test(valueChar)) {
                    maskedValue += valueChar;
                    valueIndex++;
                } else {
                    valueIndex++;
                    i--;
                }
            } else if (maskChar === 'A') {
                // Apenas letras
                if (/[a-zA-Z]/.test(valueChar)) {
                    maskedValue += valueChar;
                    valueIndex++;
                } else {
                    valueIndex++;
                    i--;
                }
            } else if (maskChar === 'S') {
                // Letras ou dígitos
                if (/[a-zA-Z0-9]/.test(valueChar)) {
                    maskedValue += valueChar;
                    valueIndex++;
                } else {
                    valueIndex++;
                    i--;
                }
            } else {
                // Caracteres especiais da máscara
                maskedValue += maskChar;
                // Se o caractere do valor for igual ao caractere da máscara, avança
                if (valueChar === maskChar) {
                    valueIndex++;
                }
            }
        }
        return maskedValue;
    };
    // Função para obter a máscara com base no tipo
    const getMask = (type)=>{
        switch(type){
            case 'cpf':
                return '###.###.###-##';
            case 'cnpj':
                return '##.###.###/####-##';
            case 'phone':
                return '(##) #####-####';
            case 'cep':
                return '#####-###';
            case 'custom':
                return customMask || '';
            default:
                return '';
        }
    };
    // Função para formatar o valor com base no tipo
    const formatValue = (value, type, customMask)=>{
        if (!value) return '';
        // Remove caracteres não numéricos para tipos numéricos
        let cleanValue = value;
        if ([
            'cpf',
            'cnpj',
            'phone',
            'cep'
        ].includes(type)) {
            cleanValue = value.replace(/\D/g, '');
        }
        const mask = getMask(type);
        return applyMask(cleanValue, mask);
    };
    // Função para remover a máscara e obter apenas os dígitos
    const unformatValue = (value)=>{
        if (!value) return '';
        if ([
            'cpf',
            'cnpj',
            'phone',
            'cep'
        ].includes(type)) {
            return value.replace(/\D/g, '');
        }
        return value;
    };
    // Manipulador de mudança de valor
    const handleChange = (e)=>{
        const newValue = e.target.value;
        const formattedValue = formatValue(newValue, type, customMask);
        setInputValue(formattedValue);
        if (onChange) {
            // Cria um evento sintético com o valor formatado
            const syntheticEvent = {
                ...e,
                target: {
                    ...e.target,
                    name: name,
                    value: formattedValue,
                    rawValue: unformatValue(formattedValue)
                }
            };
            onChange(syntheticEvent);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: "text",
        id: id,
        name: name,
        value: inputValue,
        onChange: handleChange,
        placeholder: placeholder,
        className: className,
        disabled: disabled,
        ...inputProps
    }, void 0, false, {
        fileName: "[project]/src/components/common/MaskedInput.js",
        lineNumber: 159,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = MaskedInput;
}}),
"[project]/src/components/common/AddressForm.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useCep.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$MaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/common/MaskedInput.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleMaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleMaskedInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleMaskedInput.js [app-ssr] (ecmascript) <export default as ModuleMaskedInput>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleInput.js [app-ssr] (ecmascript) <export default as ModuleInput>");
'use client';
;
;
;
;
;
;
/**
 * Componente reutilizável para formulários de endereço com busca automática por CEP
 *
 * @param {Object} props - Propriedades do componente
 * @param {Object} props.formData - Dados do formulário
 * @param {Function} props.setFormData - Função para atualizar os dados do formulário
 * @param {Object} props.errors - Erros de validação
 * @param {boolean} props.isLoading - Indica se o formulário está em carregamento
 * @param {Object} props.fieldMapping - Mapeamento dos campos do endereço para os campos do formulário
 * @param {string} props.prefix - Prefixo para campos aninhados (ex: "person.")
 * @param {Object} props.classes - Classes CSS personalizadas
 * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)
 */ const AddressForm = ({ formData, setFormData, errors = {}, isLoading = false, fieldMapping = {}, prefix = '', classes = {}, moduleColor = 'default' })=>{
    // Hook para busca de CEP
    const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useCep$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCep"])();
    // Classes CSS padrão
    const defaultClasses = {
        label: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
        input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white',
        error: 'text-sm text-red-600 dark:text-red-400 mt-1',
        iconContainer: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none',
        button: 'absolute inset-y-0 right-0 px-3 flex items-center bg-primary-500 hover:bg-primary-600 text-white rounded-r-md transition-colors'
    };
    // Mescla as classes padrão com as classes personalizadas
    const mergedClasses = {
        label: classes.label || defaultClasses.label,
        input: classes.input || defaultClasses.input,
        error: classes.error || defaultClasses.error,
        iconContainer: classes.iconContainer || defaultClasses.iconContainer,
        button: classes.button || defaultClasses.button
    };
    // Mapeamento padrão dos campos
    const defaultMapping = {
        cep: `${prefix}postalCode`,
        logradouro: `${prefix}address`,
        bairro: `${prefix}neighborhood`,
        localidade: `${prefix}city`,
        uf: `${prefix}state`
    };
    // Log para debug
    console.log('Prefix:', prefix);
    console.log('Default mapping:', defaultMapping);
    // Mescla o mapeamento padrão com o mapeamento personalizado
    const mergedMapping = {
        ...defaultMapping,
        ...fieldMapping
    };
    console.log('Mapeamento mesclado:', mergedMapping);
    // Função para lidar com a mudança de valores nos campos
    const handleChange = (e)=>{
        const { name, value } = e.target;
        console.log(`Alterando campo: ${name} para valor: ${value}`);
        // Suporte para campos aninhados (ex: "person.address")
        if (name.includes('.')) {
            const [parent, child] = name.split('.');
            console.log(`Campo aninhado: ${parent}.${child}. Estado atual:`, formData[parent]);
            const newFormData = {
                ...formData,
                [parent]: {
                    ...formData[parent],
                    [child]: value
                }
            };
            console.log('Novo estado do formulário:', newFormData);
            setFormData(newFormData);
        } else {
            const newFormData = {
                ...formData,
                [name]: value
            };
            console.log('Novo estado do formulário:', newFormData);
            setFormData(newFormData);
        }
    };
    // Função para buscar endereço pelo CEP
    const handleCepSearch = async ()=>{
        // Obtém o valor do CEP do campo correspondente
        const cepField = mergedMapping.cep;
        let cepValue;
        if (cepField.includes('.')) {
            const [parent, child] = cepField.split('.');
            cepValue = formData[parent]?.[child];
        } else {
            cepValue = formData[cepField];
        }
        console.log('Buscando endereço para o CEP:', cepValue);
        if (cepValue) {
            try {
                const result = await searchAddressByCep(cepValue, setFormData, mergedMapping);
                console.log('Resultado da busca de CEP:', result);
                // Forçar a atualização do formulário com os dados recebidos
                if (result) {
                    console.log('Dados de endereço recebidos com sucesso, atualizando formulário');
                    // Criar um objeto com os campos mapeados
                    const updatedFields = {};
                    // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário
                    Object.keys(result).forEach((apiField)=>{
                        const formField = mergedMapping[apiField];
                        if (formField && result[apiField]) {
                            updatedFields[formField] = result[apiField];
                        }
                    });
                    console.log('Campos a serem atualizados:', updatedFields);
                    // Atualizar o formulário com os dados do endereço
                    setFormData((prevData)=>({
                            ...prevData,
                            ...updatedFields
                        }));
                }
            } catch (error) {
                console.error('Erro ao buscar CEP:', error);
            }
        }
    };
    // Efeito para buscar endereço automaticamente quando o CEP for preenchido completamente
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const cepField = mergedMapping.cep;
        let cepValue;
        if (cepField.includes('.')) {
            const [parent, child] = cepField.split('.');
            cepValue = formData[parent]?.[child];
        } else {
            cepValue = formData[cepField];
        }
        // Formatar o CEP se necessário
        if (cepValue && !cepValue.includes('-') && cepValue.replace(/\D/g, '').length === 8) {
            // Formatar o CEP no formato 00000-000
            const cleanCep = cepValue.replace(/\D/g, '');
            const formattedCep = cleanCep.replace(/(\d{5})(\d{3})/, '$1-$2');
            // Atualizar o formData com o CEP formatado
            if (cepField.includes('.')) {
                const [parent, child] = cepField.split('.');
                setFormData((prev)=>({
                        ...prev,
                        [parent]: {
                            ...prev[parent],
                            [child]: formattedCep
                        }
                    }));
            } else {
                setFormData((prev)=>({
                        ...prev,
                        [cepField]: formattedCep
                    }));
            }
        }
        // Se o CEP tiver 8 dígitos (sem contar a máscara), busca o endereço
        if (cepValue && cepValue.replace(/\D/g, '').length === 8) {
            handleCepSearch();
        }
    }, [
        formData[mergedMapping.cep]
    ]);
    // Função para obter o valor de um campo, considerando campos aninhados
    const getFieldValue = (fieldName)=>{
        console.log(`Obtendo valor para o campo: ${fieldName}`);
        if (fieldName.includes('.')) {
            const [parent, child] = fieldName.split('.');
            console.log(`Campo aninhado: ${parent}.${child}, valor:`, formData[parent]?.[child]);
            return formData[parent]?.[child] || '';
        }
        console.log(`Campo simples: ${fieldName}, valor:`, formData[fieldName]);
        return formData[fieldName] || '';
    };
    // Função para verificar se um campo tem erro
    const hasError = (fieldName)=>{
        return errors[fieldName] ? true : false;
    };
    // Função para obter a mensagem de erro de um campo
    const getErrorMessage = (fieldName)=>{
        return errors[fieldName] || '';
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-1 md:grid-cols-2 gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: mergedClasses.label,
                            htmlFor: mergedMapping.cep,
                            children: "CEP"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: mergedClasses.iconContainer,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                        className: "h-5 w-5 text-gray-400 dark:text-gray-500"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/AddressForm.js",
                                        lineNumber: 227,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AddressForm.js",
                                    lineNumber: 226,
                                    columnNumber: 13
                                }, this),
                                moduleColor ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleMaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleMaskedInput$3e$__["ModuleMaskedInput"], {
                                        moduleColor: moduleColor,
                                        mask: "99999-999",
                                        replacement: {
                                            9: /[0-9]/
                                        },
                                        id: mergedMapping.cep,
                                        name: mergedMapping.cep,
                                        value: getFieldValue(mergedMapping.cep),
                                        onChange: handleChange,
                                        placeholder: "00000-000",
                                        className: "pl-10 pr-12",
                                        disabled: isLoading || isCepLoading,
                                        error: hasError(mergedMapping.cep)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/AddressForm.js",
                                        lineNumber: 231,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AddressForm.js",
                                    lineNumber: 230,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$MaskedInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    type: "cep",
                                    id: mergedMapping.cep,
                                    name: mergedMapping.cep,
                                    value: getFieldValue(mergedMapping.cep),
                                    onChange: handleChange,
                                    placeholder: "00000-000",
                                    className: `${mergedClasses.input} pl-10 pr-12 ${hasError(mergedMapping.cep) ? 'border-red-500 dark:border-red-700' : ''}`,
                                    disabled: isLoading || isCepLoading
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AddressForm.js",
                                    lineNumber: 246,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: handleCepSearch,
                                    className: mergedClasses.button,
                                    disabled: isLoading || isCepLoading,
                                    "aria-label": "Buscar CEP",
                                    children: isCepLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                        className: "h-5 w-5 animate-spin"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/AddressForm.js",
                                        lineNumber: 265,
                                        columnNumber: 17
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                        className: "h-5 w-5"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/AddressForm.js",
                                        lineNumber: 267,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/AddressForm.js",
                                    lineNumber: 257,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 225,
                            columnNumber: 11
                        }, this),
                        hasError(mergedMapping.cep) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: getErrorMessage(mergedMapping.cep)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 272,
                            columnNumber: 13
                        }, this),
                        cepError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: cepError
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 275,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/AddressForm.js",
                    lineNumber: 221,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: mergedClasses.label,
                            htmlFor: mergedMapping.uf,
                            children: "Estado"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 281,
                            columnNumber: 11
                        }, this),
                        moduleColor ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                            moduleColor: moduleColor,
                            id: mergedMapping.uf,
                            name: mergedMapping.uf,
                            type: "text",
                            value: getFieldValue(mergedMapping.uf),
                            onChange: handleChange,
                            placeholder: "UF",
                            disabled: isLoading || isCepLoading,
                            maxLength: 2,
                            error: hasError(mergedMapping.uf)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 285,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            id: mergedMapping.uf,
                            name: mergedMapping.uf,
                            type: "text",
                            value: getFieldValue(mergedMapping.uf),
                            onChange: handleChange,
                            placeholder: "UF",
                            className: `${mergedClasses.input} ${hasError(mergedMapping.uf) ? 'border-red-500 dark:border-red-700' : ''}`,
                            disabled: isLoading || isCepLoading,
                            maxLength: 2
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 298,
                            columnNumber: 13
                        }, this),
                        hasError(mergedMapping.uf) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: getErrorMessage(mergedMapping.uf)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 311,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/AddressForm.js",
                    lineNumber: 280,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: mergedClasses.label,
                            htmlFor: mergedMapping.localidade,
                            children: "Cidade"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 317,
                            columnNumber: 11
                        }, this),
                        moduleColor ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                            moduleColor: moduleColor,
                            id: mergedMapping.localidade,
                            name: mergedMapping.localidade,
                            type: "text",
                            value: getFieldValue(mergedMapping.localidade),
                            onChange: handleChange,
                            placeholder: "Cidade",
                            disabled: isLoading || isCepLoading,
                            error: hasError(mergedMapping.localidade)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 321,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            id: mergedMapping.localidade,
                            name: mergedMapping.localidade,
                            type: "text",
                            value: getFieldValue(mergedMapping.localidade),
                            onChange: handleChange,
                            placeholder: "Cidade",
                            className: `${mergedClasses.input} ${hasError(mergedMapping.localidade) ? 'border-red-500 dark:border-red-700' : ''}`,
                            disabled: isLoading || isCepLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 333,
                            columnNumber: 13
                        }, this),
                        hasError(mergedMapping.localidade) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: getErrorMessage(mergedMapping.localidade)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 345,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/AddressForm.js",
                    lineNumber: 316,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: mergedClasses.label,
                            htmlFor: mergedMapping.bairro,
                            children: "Bairro"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 351,
                            columnNumber: 11
                        }, this),
                        moduleColor ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                            moduleColor: moduleColor,
                            id: mergedMapping.bairro,
                            name: mergedMapping.bairro,
                            type: "text",
                            value: getFieldValue(mergedMapping.bairro),
                            onChange: handleChange,
                            placeholder: "Bairro",
                            disabled: isLoading || isCepLoading,
                            error: hasError(mergedMapping.bairro)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 355,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            id: mergedMapping.bairro,
                            name: mergedMapping.bairro,
                            type: "text",
                            value: getFieldValue(mergedMapping.bairro),
                            onChange: handleChange,
                            placeholder: "Bairro",
                            className: `${mergedClasses.input} ${hasError(mergedMapping.bairro) ? 'border-red-500 dark:border-red-700' : ''}`,
                            disabled: isLoading || isCepLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 367,
                            columnNumber: 13
                        }, this),
                        hasError(mergedMapping.bairro) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: getErrorMessage(mergedMapping.bairro)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 379,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/AddressForm.js",
                    lineNumber: 350,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:col-span-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: mergedClasses.label,
                            htmlFor: mergedMapping.logradouro,
                            children: "Logradouro"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 385,
                            columnNumber: 11
                        }, this),
                        moduleColor ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                            moduleColor: moduleColor,
                            id: mergedMapping.logradouro,
                            name: mergedMapping.logradouro,
                            type: "text",
                            value: getFieldValue(mergedMapping.logradouro),
                            onChange: handleChange,
                            placeholder: "Rua, Avenida, etc.",
                            disabled: isLoading || isCepLoading,
                            error: hasError(mergedMapping.logradouro)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 389,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            id: mergedMapping.logradouro,
                            name: mergedMapping.logradouro,
                            type: "text",
                            value: getFieldValue(mergedMapping.logradouro),
                            onChange: handleChange,
                            placeholder: "Rua, Avenida, etc.",
                            className: `${mergedClasses.input} ${hasError(mergedMapping.logradouro) ? 'border-red-500 dark:border-red-700' : ''}`,
                            disabled: isLoading || isCepLoading
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 401,
                            columnNumber: 13
                        }, this),
                        hasError(mergedMapping.logradouro) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: mergedClasses.error,
                            children: getErrorMessage(mergedMapping.logradouro)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/AddressForm.js",
                            lineNumber: 413,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/AddressForm.js",
                    lineNumber: 384,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/AddressForm.js",
            lineNumber: 219,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/AddressForm.js",
        lineNumber: 217,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = AddressForm;
}}),
"[project]/src/components/common/TimeInput.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
/**
 * Componente de input de horário personalizado
 * @param {Object} props - Propriedades do componente
 * @param {string} props.value - Valor inicial do input (formato HH:MM)
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.placeholder - Placeholder do input
 * @param {boolean} props.disabled - Se o input está desabilitado
 * @param {string} props.id - ID do input
 * @param {string} props.name - Nome do input
 * @param {string} props.error - Mensagem de erro
 * @param {string} props.errorClassName - Classes CSS para o erro
 */ const TimeInput = ({ value: initialValue = '', onChange, className = '', placeholder = '00:00', disabled = false, id, name, error, errorClassName = 'text-xs text-red-500 mt-1' })=>{
    // Referência para o input
    const inputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Estado interno para controlar o valor durante a edição
    // Usamos o valor inicial apenas na primeira renderização
    const [inputValue, setInputValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialValue || '');
    // Formata o valor para o formato HH:MM
    const formatTimeValue = (val)=>{
        // Remove caracteres não numéricos, exceto ':'
        let cleaned = val.replace(/[^\d:]/g, '');
        // Se não tiver ':', adiciona após os primeiros 2 dígitos
        if (cleaned.length > 2 && !cleaned.includes(':')) {
            cleaned = cleaned.substring(0, 2) + ':' + cleaned.substring(2);
        }
        // Limita o tamanho total a 5 caracteres (HH:MM)
        if (cleaned.length > 5) {
            cleaned = cleaned.substring(0, 5);
        }
        // Valida horas e minutos
        if (cleaned.includes(':')) {
            const [hours, minutes] = cleaned.split(':');
            // Valida horas (0-23)
            if (hours.length === 2 && parseInt(hours) > 23) {
                cleaned = '23' + cleaned.substring(2);
            }
            // Valida minutos (0-59)
            if (minutes && minutes.length === 2 && parseInt(minutes) > 59) {
                cleaned = cleaned.substring(0, 3) + '59';
            }
        }
        return cleaned;
    };
    // Manipulador de mudança do input
    const handleChange = (e)=>{
        console.log('TimeInput handleChange - valor original:', e.target.value);
        const newValue = formatTimeValue(e.target.value);
        console.log('TimeInput handleChange - valor formatado:', newValue);
        // Atualiza apenas o estado interno, sem notificar o componente pai ainda
        // Isso evita que o React recrie o componente e cause perda de foco
        setInputValue(newValue);
        console.log('TimeInput handleChange - estado interno atualizado');
    };
    // Verifica se o formato do horário é válido (HH:MM)
    const isValidTimeFormat = (val)=>{
        if (!val) return true; // Vazio é considerado válido
        // Verifica o formato HH:MM
        const regex = /^([0-1][0-9]|2[0-3]):([0-5][0-9])$/;
        return regex.test(val);
    };
    // Manipulador de teclas
    const handleKeyDown = (e)=>{
        // Adiciona ':' automaticamente após digitar 2 números
        if (e.target.value.length === 2 && !e.target.value.includes(':') && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'Tab' && e.key !== ':') {
            const newValue = e.target.value + ':';
            setInputValue(newValue);
            e.preventDefault();
            // Posiciona o cursor após o ':'
            setTimeout(()=>{
                if (inputRef.current) {
                    inputRef.current.selectionStart = 3;
                    inputRef.current.selectionEnd = 3;
                }
            }, 0);
        }
        // Permite teclas de navegação e edição
        if ([
            'ArrowLeft',
            'ArrowRight',
            'ArrowUp',
            'ArrowDown',
            'Home',
            'End',
            'Tab',
            'Backspace',
            'Delete'
        ].includes(e.key) || e.ctrlKey && [
            'a',
            'c',
            'v',
            'x'
        ].includes(e.key.toLowerCase())) {
            return;
        }
        // Permite apenas números e ':'
        if (!/[0-9:]/.test(e.key)) {
            e.preventDefault();
        }
    };
    // Manipulador de foco
    const handleFocus = (e)=>{
        // Seleciona todo o texto ao focar
        e.target.select();
    };
    // Manipulador de perda de foco
    const handleBlur = ()=>{
        console.log('TimeInput handleBlur - valor atual:', inputValue);
        // Formata o valor final ao perder o foco
        if (inputValue && !isValidTimeFormat(inputValue)) {
            // Se o valor não estiver completo, tenta completá-lo
            let formattedValue = inputValue;
            if (formattedValue.length === 1) {
                // Um dígito: assume como hora e adiciona minutos zerados
                formattedValue = '0' + formattedValue + ':00';
            } else if (formattedValue.length === 2) {
                // Dois dígitos: assume como hora e adiciona minutos zerados
                formattedValue = formattedValue + ':00';
            } else if (formattedValue.includes(':')) {
                const [hours, minutes] = formattedValue.split(':');
                // Completa horas se necessário
                let formattedHours = hours;
                if (hours.length === 1) {
                    formattedHours = '0' + hours;
                }
                // Completa minutos se necessário
                let formattedMinutes = minutes || '00';
                if (minutes && minutes.length === 1) {
                    formattedMinutes = minutes + '0';
                }
                formattedValue = formattedHours + ':' + formattedMinutes;
            }
            // Atualiza o valor interno
            if (isValidTimeFormat(formattedValue)) {
                console.log('TimeInput handleBlur - valor formatado:', formattedValue);
                setInputValue(formattedValue);
                // Notifica o componente pai com o valor formatado
                onChange(formattedValue);
            }
        } else if (inputValue) {
            // Se o valor já estiver no formato correto, notifica o componente pai
            console.log('TimeInput handleBlur - notificando componente pai com valor atual');
            onChange(inputValue);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: inputRef,
                type: "text",
                value: inputValue,
                onChange: handleChange,
                onKeyDown: handleKeyDown,
                onFocus: handleFocus,
                onBlur: handleBlur,
                placeholder: placeholder,
                disabled: disabled,
                id: id,
                name: name,
                className: className,
                maxLength: 5
            }, void 0, false, {
                fileName: "[project]/src/components/common/TimeInput.js",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: errorClassName,
                children: error
            }, void 0, false, {
                fileName: "[project]/src/components/common/TimeInput.js",
                lineNumber: 198,
                columnNumber: 17
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/TimeInput.js",
        lineNumber: 182,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TimeInput;
}}),
"[project]/src/components/workingHours/BranchWorkingHoursForm.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TimeInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/common/TimeInput.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-ssr] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/trash.js [app-ssr] (ecmascript) <export default as Trash>");
"use client";
;
;
;
;
const DAYS_OF_WEEK = [
    {
        value: '1',
        label: 'Segunda-feira'
    },
    {
        value: '2',
        label: 'Terça-feira'
    },
    {
        value: '3',
        label: 'Quarta-feira'
    },
    {
        value: '4',
        label: 'Quinta-feira'
    },
    {
        value: '5',
        label: 'Sexta-feira'
    },
    {
        value: '6',
        label: 'Sábado'
    },
    {
        value: '0',
        label: 'Domingo'
    }
];
const BranchWorkingHoursForm = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ defaultWorkingHours, onChange, isLoading, labelClasses, inputClasses, errorClasses, onValidationChange// Nova prop para informar o componente pai sobre o estado da validação
 }, ref)=>{
    const [workingHours, setWorkingHours] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    // Initialize working hours from props or with default values
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultWorkingHours) {
            // Converte os dados da API para o formato de exibição
            const displayHours = {};
            Object.keys(defaultWorkingHours).forEach((day)=>{
                displayHours[day] = defaultWorkingHours[day].map((slot)=>({
                        startTime: minutesToTime(slot.startTimeMinutes),
                        endTime: minutesToTime(slot.endTimeMinutes)
                    }));
            });
            console.log('useEffect - dados convertidos para exibição:', displayHours);
            setWorkingHours(displayHours);
        } else {
            // Default working hours: Monday to Friday, 9:00-18:00
            const defaultHours = {};
            // Add default hours for weekdays (1-5)
            for(let i = 1; i <= 5; i++){
                defaultHours[i] = [
                    {
                        startTime: '09:00',
                        endTime: '18:00'
                    }
                ];
            }
            console.log('useEffect - usando horários padrão:', defaultHours);
            setWorkingHours(defaultHours);
        }
    }, [
        defaultWorkingHours
    ]);
    // Convert minutes to time string (HH:MM)
    const minutesToTime = (minutes)=>{
        if (minutes === null || minutes === undefined) return '';
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
    };
    // Convert time string (HH:MM) to minutes
    const timeToMinutes = (timeStr)=>{
        if (!timeStr) return null;
        // Garantir que a string tem o formato correto
        if (!timeStr.includes(':')) {
            // Se não tiver ':', tenta formatar
            if (timeStr.length <= 2) {
                // Apenas horas
                return parseInt(timeStr) * 60;
            } else {
                // Assume que os dois primeiros dígitos são horas e o resto são minutos
                const hours = parseInt(timeStr.slice(0, 2));
                const minutes = parseInt(timeStr.slice(2));
                return hours * 60 + minutes;
            }
        }
        // Formato normal HH:MM
        const parts = timeStr.split(':');
        const hours = parseInt(parts[0] || '0');
        const minutes = parseInt(parts[1] || '0');
        // Validação básica
        if (isNaN(hours) || isNaN(minutes)) return null;
        return hours * 60 + minutes;
    };
    // Format working hours for display and editing
    const formatWorkingHoursForDisplay = (workingHoursData)=>{
        console.log('formatWorkingHoursForDisplay - dados recebidos:', workingHoursData);
        const formatted = {};
        Object.keys(workingHoursData).forEach((dayOfWeek)=>{
            console.log('formatWorkingHoursForDisplay - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);
            formatted[dayOfWeek] = workingHoursData[dayOfWeek].map((slot)=>{
                // Verifica se os campos _startTime e _endTime existem (campos preservados)
                if (slot._startTime !== undefined || slot._endTime !== undefined) {
                    console.log('formatWorkingHoursForDisplay - usando campos preservados');
                    return {
                        startTime: slot._startTime || '',
                        endTime: slot._endTime || ''
                    };
                }
                // Caso contrário, converte de minutos para string
                const startTime = minutesToTime(slot.startTimeMinutes);
                const endTime = minutesToTime(slot.endTimeMinutes);
                console.log('formatWorkingHoursForDisplay - convertendo slot:', 'startTimeMinutes:', slot.startTimeMinutes, '->', startTime, 'endTimeMinutes:', slot.endTimeMinutes, '->', endTime);
                return {
                    startTime,
                    endTime
                };
            });
        });
        console.log('formatWorkingHoursForDisplay - resultado formatado:', formatted);
        return formatted;
    };
    // Format working hours for API
    const formatWorkingHoursForAPI = (workingHoursData)=>{
        console.log('formatWorkingHoursForAPI - dados recebidos:', workingHoursData);
        const formatted = {};
        Object.keys(workingHoursData).forEach((dayOfWeek)=>{
            console.log('formatWorkingHoursForAPI - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);
            // Filtra slots vazios (sem horário de início e término)
            const validSlots = workingHoursData[dayOfWeek].filter((slot)=>{
                const hasStartTime = slot.startTime && slot.startTime.trim() !== '';
                const hasEndTime = slot.endTime && slot.endTime.trim() !== '';
                return hasStartTime || hasEndTime;
            });
            console.log('formatWorkingHoursForAPI - slots válidos:', validSlots);
            formatted[dayOfWeek] = validSlots.map((slot)=>{
                // Verifica se os valores existem antes de convertê-los
                const startTime = slot.startTime || '';
                const endTime = slot.endTime || '';
                const startTimeMinutes = timeToMinutes(startTime);
                const endTimeMinutes = timeToMinutes(endTime);
                console.log('formatWorkingHoursForAPI - convertendo slot:', 'startTime:', startTime, '->', startTimeMinutes, 'minutos', 'endTime:', endTime, '->', endTimeMinutes, 'minutos');
                return {
                    startTimeMinutes,
                    endTimeMinutes,
                    // Removidos os campos de intervalo
                    breakStartMinutes: null,
                    breakEndMinutes: null
                };
            });
            // Se não houver slots válidos, adiciona um slot vazio
            if (formatted[dayOfWeek].length === 0) {
                formatted[dayOfWeek] = [
                    {
                        startTimeMinutes: null,
                        endTimeMinutes: null,
                        breakStartMinutes: null,
                        breakEndMinutes: null
                    }
                ];
            }
        });
        console.log('formatWorkingHoursForAPI - resultado formatado:', formatted);
        return formatted;
    };
    // Handle changes to working hours
    const handleWorkingHoursChange = (dayOfWeek, index, field, value)=>{
        console.log('handleWorkingHoursChange - dia:', dayOfWeek, 'índice:', index, 'campo:', field, 'valor:', value);
        // Cria uma cópia profunda do estado atual para evitar problemas de referência
        const newWorkingHours = JSON.parse(JSON.stringify(workingHours));
        if (!newWorkingHours[dayOfWeek]) {
            newWorkingHours[dayOfWeek] = [];
            console.log('handleWorkingHoursChange - criando array para o dia', dayOfWeek);
        }
        if (!newWorkingHours[dayOfWeek][index]) {
            newWorkingHours[dayOfWeek][index] = {
                startTime: '',
                endTime: ''
            };
            console.log('handleWorkingHoursChange - criando objeto para o índice', index);
        }
        // Preserva os valores existentes e atualiza apenas o campo especificado
        console.log('handleWorkingHoursChange - valor anterior:', newWorkingHours[dayOfWeek][index][field]);
        newWorkingHours[dayOfWeek][index][field] = value;
        console.log('handleWorkingHoursChange - novo valor:', newWorkingHours[dayOfWeek][index][field]);
        console.log('handleWorkingHoursChange - slot completo:', newWorkingHours[dayOfWeek][index]);
        // Atualiza o estado local
        setWorkingHours(newWorkingHours);
        console.log('handleWorkingHoursChange - estado atualizado');
        console.log('handleWorkingHoursChange - estado completo:', newWorkingHours);
        // Validate time slots
        validateTimeSlot(dayOfWeek, index, newWorkingHours[dayOfWeek][index]);
        // Notifica o componente pai com uma cópia dos dados
        if (onChange) {
            console.log('handleWorkingHoursChange - notificando componente pai');
            // Converte os dados para o formato da API
            const apiData = formatWorkingHoursForAPI(newWorkingHours);
            console.log('handleWorkingHoursChange - dados formatados para API:', apiData);
            onChange(apiData);
        }
    };
    // Validate time slot - validação durante a edição (menos rigorosa)
    const validateTimeSlot = (dayOfWeek, index, slot)=>{
        const newErrors = {
            ...errors
        };
        if (!newErrors[dayOfWeek]) {
            newErrors[dayOfWeek] = [];
        }
        if (!newErrors[dayOfWeek][index]) {
            newErrors[dayOfWeek][index] = {};
        }
        // Clear previous errors
        newErrors[dayOfWeek][index] = {};
        // Só validamos campos obrigatórios quando ambos estiverem preenchidos ou quando o formulário for enviado
        // Isso permite que o usuário preencha um campo por vez
        // Validate that end time is after start time if both are provided
        if (slot.startTime && slot.endTime) {
            const startMinutes = timeToMinutes(slot.startTime);
            const endMinutes = timeToMinutes(slot.endTime);
            if (endMinutes <= startMinutes) {
                newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';
            }
        }
        setErrors(newErrors);
    };
    // Validate all time slots - validação completa para submissão do formulário
    const validateAllTimeSlots = ()=>{
        const newErrors = {};
        let isValid = true;
        // Verificar cada dia e cada slot
        Object.keys(workingHours).forEach((dayOfWeek)=>{
            if (!newErrors[dayOfWeek]) {
                newErrors[dayOfWeek] = [];
            }
            workingHours[dayOfWeek].forEach((slot, index)=>{
                if (!newErrors[dayOfWeek][index]) {
                    newErrors[dayOfWeek][index] = {};
                }
                // Validar horário inicial
                if (!slot.startTime) {
                    newErrors[dayOfWeek][index].startTime = 'Horário de início é obrigatório';
                    isValid = false;
                }
                // Validar horário final
                if (!slot.endTime) {
                    newErrors[dayOfWeek][index].endTime = 'Horário de término é obrigatório';
                    isValid = false;
                }
                // Validar que o horário final é após o inicial
                if (slot.startTime && slot.endTime) {
                    const startMinutes = timeToMinutes(slot.startTime);
                    const endMinutes = timeToMinutes(slot.endTime);
                    if (endMinutes <= startMinutes) {
                        newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';
                        isValid = false;
                    }
                }
            // Removida a validação de intervalos
            });
        });
        setErrors(newErrors);
        // Notificar o componente pai sobre o estado da validação
        if (onValidationChange) {
            onValidationChange(isValid);
        }
        return isValid;
    };
    // Add a new time slot for a day (apenas um horário por dia)
    const addTimeSlot = (dayOfWeek)=>{
        const newWorkingHours = {
            ...workingHours
        };
        // Se já existe um horário para este dia, não adiciona outro
        if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {
            return;
        }
        if (!newWorkingHours[dayOfWeek]) {
            newWorkingHours[dayOfWeek] = [];
        }
        newWorkingHours[dayOfWeek].push({
            startTime: '',
            endTime: ''
        });
        setWorkingHours(newWorkingHours);
        // Notify parent component
        if (onChange) {
            onChange(formatWorkingHoursForAPI(newWorkingHours));
        }
    };
    // Remove a time slot
    const removeTimeSlot = (dayOfWeek, index)=>{
        const newWorkingHours = {
            ...workingHours
        };
        if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {
            newWorkingHours[dayOfWeek].splice(index, 1);
            // If no more slots for this day, remove the day
            if (newWorkingHours[dayOfWeek].length === 0) {
                delete newWorkingHours[dayOfWeek];
            }
            setWorkingHours(newWorkingHours);
            // Remove errors for this slot
            const newErrors = {
                ...errors
            };
            if (newErrors[dayOfWeek] && newErrors[dayOfWeek].length > 0) {
                newErrors[dayOfWeek].splice(index, 1);
                if (newErrors[dayOfWeek].length === 0) {
                    delete newErrors[dayOfWeek];
                }
                setErrors(newErrors);
            }
            // Notify parent component
            if (onChange) {
                onChange(formatWorkingHoursForAPI(newWorkingHours));
            }
        }
    };
    // Copy working hours from one day to other days
    const copyWorkingHours = (fromDay, toDays)=>{
        console.log('copyWorkingHours - copiando de:', fromDay, 'para:', toDays);
        console.log('copyWorkingHours - horários atuais:', workingHours);
        if (!workingHours[fromDay] || workingHours[fromDay].length === 0) {
            console.log('copyWorkingHours - não há horários para copiar do dia', fromDay);
            return;
        }
        // Cria uma cópia profunda para evitar problemas de referência
        const newWorkingHours = JSON.parse(JSON.stringify(workingHours));
        console.log('copyWorkingHours - horários a serem copiados:', workingHours[fromDay]);
        toDays.forEach((toDay)=>{
            console.log('copyWorkingHours - copiando para o dia:', toDay);
            newWorkingHours[toDay] = JSON.parse(JSON.stringify(workingHours[fromDay]));
        });
        console.log('copyWorkingHours - novos horários:', newWorkingHours);
        setWorkingHours(newWorkingHours);
        console.log('copyWorkingHours - estado atualizado');
        // Notify parent component
        if (onChange) {
            console.log('copyWorkingHours - notificando componente pai');
            const formattedData = formatWorkingHoursForAPI(newWorkingHours);
            console.log('copyWorkingHours - dados formatados para API:', formattedData);
            onChange(formattedData);
        }
    };
    // Copy Monday to all weekdays
    const copyMondayToWeekdays = ()=>{
        console.log('copyMondayToWeekdays - chamado');
        console.log('copyMondayToWeekdays - estado atual:', workingHours);
        // Verifica se há horários válidos na segunda-feira
        if (!workingHours['1'] || workingHours['1'].length === 0) {
            console.log('copyMondayToWeekdays - não há horários na segunda-feira para copiar');
            alert('Não há horários na segunda-feira para copiar.');
            return;
        }
        // Verifica se os horários da segunda-feira têm valores válidos
        const mondaySlots = workingHours['1'];
        console.log('copyMondayToWeekdays - slots da segunda-feira:', mondaySlots);
        // Verifica se há pelo menos um slot com horário de início e término válidos
        const hasValidSlot = mondaySlots.some((slot)=>{
            return slot.startTime && slot.startTime.trim() !== '' && slot.endTime && slot.endTime.trim() !== '';
        });
        if (!hasValidSlot) {
            console.log('copyMondayToWeekdays - não há horários válidos na segunda-feira');
            alert('Não há horários válidos na segunda-feira. Certifique-se de que há pelo menos um horário com início e término preenchidos.');
            return;
        }
        // Filtra apenas os slots válidos
        const validMondaySlots = mondaySlots.filter((slot)=>{
            return slot.startTime && slot.startTime.trim() !== '' && slot.endTime && slot.endTime.trim() !== '';
        });
        console.log('copyMondayToWeekdays - slots válidos da segunda-feira:', validMondaySlots);
        // Cria uma cópia profunda do estado atual
        const newWorkingHours = JSON.parse(JSON.stringify(workingHours));
        // Copia os horários da segunda-feira para os outros dias da semana
        [
            '2',
            '3',
            '4',
            '5'
        ].forEach((day)=>{
            console.log(`copyMondayToWeekdays - copiando para o dia ${day}`);
            // Garante que o array para o dia existe
            if (!newWorkingHours[day]) {
                newWorkingHours[day] = [];
            }
            // Limpa os horários existentes e adiciona os novos
            newWorkingHours[day] = JSON.parse(JSON.stringify(validMondaySlots));
        });
        console.log('copyMondayToWeekdays - novos horários:', newWorkingHours);
        // Atualiza o estado
        setWorkingHours(newWorkingHours);
        console.log('copyMondayToWeekdays - estado atualizado');
        // Notifica o componente pai
        if (onChange) {
            console.log('copyMondayToWeekdays - notificando componente pai');
            // Converte os dados para o formato da API
            const apiData = formatWorkingHoursForAPI(newWorkingHours);
            console.log('copyMondayToWeekdays - dados formatados para API:', apiData);
            onChange(apiData);
        }
        alert('Horários da segunda-feira copiados com sucesso para os outros dias da semana!');
    };
    // Render time input field
    const renderTimeInput = (dayOfWeek, index, field, label, value)=>{
        const hasError = errors[dayOfWeek]?.[index]?.[field];
        // Função simplificada para lidar com mudanças no input de horário
        const handleTimeChange = (newValue)=>{
            // Só atualiza se o valor for válido
            if (newValue) {
                console.log('handleTimeChange - novo valor válido:', newValue);
                handleWorkingHoursChange(dayOfWeek, index, field, newValue);
            } else {
                console.log('handleTimeChange - ignorando valor vazio');
            }
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                    className: `${labelClasses} text-xs`,
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                    lineNumber: 498,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TimeInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    value: value || '',
                    onChange: handleTimeChange,
                    className: `${inputClasses} ${hasError ? 'border-red-500 dark:border-red-700' : ''}`,
                    disabled: isLoading,
                    error: hasError,
                    errorClassName: errorClasses
                }, void 0, false, {
                    fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                    lineNumber: 501,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
            lineNumber: 497,
            columnNumber: 7
        }, this);
    };
    // Expor a função de validação para o componente pai via useImperativeHandle
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useImperativeHandle(ref, ()=>({
            validateAllTimeSlots
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-base font-medium text-neutral-800 dark:text-gray-200 flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                className: "h-4 w-4 text-primary-500 dark:text-primary-400"
                            }, void 0, false, {
                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                lineNumber: 522,
                                columnNumber: 11
                            }, this),
                            "Horários de Trabalho Padrão"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                        lineNumber: 521,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>{
                            console.log('Botão "Copiar Segunda para dias úteis" clicado');
                            copyMondayToWeekdays();
                        },
                        className: "text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors",
                        children: "Copiar Segunda para dias úteis"
                    }, void 0, false, {
                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                        lineNumber: 526,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                lineNumber: 520,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-start gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
                            size: 16,
                            className: "text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0"
                        }, void 0, false, {
                            fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                            lineNumber: 540,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-600 dark:text-gray-300",
                            children: "Configure os horários de trabalho padrão para esta unidade. Estes horários serão aplicados automaticamente a todos os usuários associados a esta unidade."
                        }, void 0, false, {
                            fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                            lineNumber: 541,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                    lineNumber: 539,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                lineNumber: 538,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-8",
                children: DAYS_OF_WEEK.map((day)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-b border-gray-200 dark:border-gray-700 pb-6 last:border-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                        className: "font-medium text-gray-800 dark:text-gray-200",
                                        children: day.label
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                        lineNumber: 551,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>addTimeSlot(day.value),
                                        disabled: workingHours[day.value] && workingHours[day.value].length > 0,
                                        className: "text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                size: 12
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                lineNumber: 558,
                                                columnNumber: 17
                                            }, this),
                                            "Adicionar Horário"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                        lineNumber: 552,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                lineNumber: 550,
                                columnNumber: 13
                            }, this),
                            !workingHours[day.value] || workingHours[day.value].length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-500 dark:text-gray-400 italic",
                                children: "Nenhum horário configurado para este dia"
                            }, void 0, false, {
                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                lineNumber: 564,
                                columnNumber: 15
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: workingHours[day.value].map((slot, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-1 md:grid-cols-3 gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "md:col-span-1",
                                                children: renderTimeInput(day.value, index, 'startTime', 'Início', slot.startTime)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                lineNumber: 572,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "md:col-span-1",
                                                children: renderTimeInput(day.value, index, 'endTime', 'Término', slot.endTime)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                lineNumber: 577,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "md:col-span-1 flex items-end justify-end",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: ()=>removeTimeSlot(day.value, index),
                                                    className: "text-xs px-2 py-1 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors flex items-center gap-1",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                                            size: 12
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                            lineNumber: 588,
                                                            columnNumber: 25
                                                        }, this),
                                                        "Remover"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                    lineNumber: 583,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                                lineNumber: 582,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                        lineNumber: 570,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                                lineNumber: 568,
                                columnNumber: 15
                            }, this)
                        ]
                    }, day.value, true, {
                        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                        lineNumber: 549,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
                lineNumber: 547,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/workingHours/BranchWorkingHoursForm.js",
        lineNumber: 519,
        columnNumber: 5
    }, this);
});
const __TURBOPACK__default__export__ = BranchWorkingHoursForm;
}}),

};

//# sourceMappingURL=src_components_afbf8f._.js.map