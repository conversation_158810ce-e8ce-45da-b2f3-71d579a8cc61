// scripts/run-all-seeds-docker.js
const { execSync } = require('child_process');
const path = require('path');

// Função para executar um comando Docker
function runDockerCommand(command, description) {
  console.log(`\n=== Executando seed de ${description}... ===\n`);
  
  try {
    // Executar o comando dentro do container Docker
    execSync(`docker exec -it high-tide-systems-api ${command}`, { 
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });
    
    console.log(`\n✅ Seed de ${description} executado com sucesso!\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ Erro ao executar o seed de ${description}:`, error);
    return false;
  }
}

// Função principal para executar todos os seeds em sequência dentro do Docker
async function runAllSeedsDocker() {
  console.log('=== INICIANDO EXECUÇÃO DE TODOS OS SEEDS NO DOCKER ===\n');
  
  // Ordem de execução dos seeds
  const seeds = [
    { command: 'node prisma/seed.js', description: 'base (empresas e usuário admin)' },
    { command: 'node prisma/seed-professions.js', description: 'profissões' },
    { command: 'node prisma/seed-users.js', description: 'usuários' },
    { command: 'node prisma/seed-insurances.js', description: 'convênios' },
    { command: 'node prisma/seed-insurance-limits.js', description: 'limites de convênio' },
    { command: 'node prisma/seed-service-types.js', description: 'tipos de serviço' },
    { command: 'node prisma/seed-locations.js', description: 'localizações' },
    { command: 'node prisma/seed-clients.js', description: 'clientes' },
    { command: 'node prisma/seed-patients.js', description: 'pacientes' },
    { command: 'node prisma/seed-working-hours.js', description: 'horários de trabalho' }
  ];
  
  let successCount = 0;
  let failCount = 0;
  
  // Executar cada seed em sequência
  for (const seed of seeds) {
    const success = runDockerCommand(seed.command, seed.description);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  // Exibir resumo
  console.log('\n=== RESUMO DA EXECUÇÃO DOS SEEDS NO DOCKER ===');
  console.log(`✅ Seeds executados com sucesso: ${successCount}`);
  console.log(`❌ Seeds com falha: ${failCount}`);
  console.log('=== FIM DA EXECUÇÃO DOS SEEDS NO DOCKER ===\n');
  
  if (failCount > 0) {
    console.log('⚠️ Alguns seeds falharam. Verifique os erros acima e tente executar individualmente os seeds que falharam.');
    process.exit(1);
  } else {
    console.log('🎉 Todos os seeds foram executados com sucesso no Docker!');
  }
}

// Executar a função principal
runAllSeedsDocker();
