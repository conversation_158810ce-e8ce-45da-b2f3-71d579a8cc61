// scripts/run-clients-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de clientes...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-clients.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de clientes executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de clientes:', error);
  process.exit(1);
}
