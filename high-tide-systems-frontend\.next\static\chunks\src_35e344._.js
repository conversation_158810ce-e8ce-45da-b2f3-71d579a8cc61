(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_35e344._.js", {

"[project]/src/components/tutorial/TutorialTriggerButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/TutorialContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/circle-help.js [app-client] (ecmascript) <export default as HelpCircle>");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
/**
 * Botão para iniciar um tutorial
 * 
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.steps - Etapas do tutorial
 * @param {string} props.tutorialName - Nome único do tutorial
 * @param {boolean} props.showIfCompleted - Se deve mostrar o botão mesmo se o tutorial já foi concluído
 * @param {string} props.size - Tamanho do botão ('sm', 'md', 'lg')
 * @param {Object} props.className - Classes adicionais para o botão
 */ const TutorialTriggerButton = ({ steps, tutorialName, showIfCompleted = true, size = 'md', className = '', children })=>{
    _s();
    const { startTutorial, isTutorialCompleted } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTutorial"])();
    // Não renderizar o botão se o tutorial já foi concluído e showIfCompleted for falso
    if (!showIfCompleted && isTutorialCompleted(tutorialName)) {
        return null;
    }
    // Determinar tamanho do botão
    const sizeClasses = {
        sm: 'p-1.5 text-xs',
        md: 'p-2 text-sm',
        lg: 'p-2.5 text-base'
    };
    // Determinar tamanho do ícone
    const iconSize = {
        sm: 14,
        md: 16,
        lg: 20
    };
    const isCompleted = isTutorialCompleted(tutorialName);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: ()=>startTutorial(steps, tutorialName),
        className: `
        relative flex items-center gap-1.5 rounded-full 
        ${isCompleted ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : 'bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/40 dark:text-primary-300 dark:hover:bg-primary-800/60'}
        transition-colors ${sizeClasses[size]} ${className}
      `,
        "aria-label": `Abrir tutorial: ${tutorialName}`,
        title: isCompleted ? "Ver tutorial novamente" : "Ver tutorial",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__["HelpCircle"], {
                size: iconSize[size]
            }, void 0, false, {
                fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            children,
            !isCompleted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-primary-500 dark:bg-primary-400"
            }, void 0, false, {
                fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
        lineNumber: 49,
        columnNumber: 5
    }, this);
};
_s(TutorialTriggerButton, "45cQxarC9/Ea/taXKiFcQegbpvE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTutorial"]
    ];
});
_c = TutorialTriggerButton;
const __TURBOPACK__default__export__ = TutorialTriggerButton;
var _c;
__turbopack_refresh__.register(_c, "TutorialTriggerButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/modules/people/services/insurancesService.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// app/modules/people/services/insurancesService.js
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "insurancesService": (()=>insurancesService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/apiResponseAdapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/services/exportService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-client] (ecmascript)");
;
;
;
;
;
const insurancesService = {
    // Obter lista de convênios (com suporte a filtragem)
    getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {})=>{
        try {
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (companyId) params.append('companyId', companyId);
            if (page) params.append('page', page);
            if (limit) params.append('limit', limit);
            // Adicionar insuranceIds como parâmetros separados com notação de array
            if (insuranceIds && insuranceIds.length > 0) {
                // Garantir que insuranceIds seja um array
                const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [
                    insuranceIds
                ];
                // Adicionar cada ID como um parâmetro separado
                insuranceIdsArray.forEach((id, index)=>{
                    // Usar a notação de array para compatibilidade com a API
                    params.append(`insuranceIds[${index}]`, id);
                });
                console.log("Filtrando por múltiplos IDs de convênios:", insuranceIdsArray);
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/insurances?${params.toString()}`);
            // Usar o adaptador para extrair os dados de forma consistente
            // Primeiro tentamos extrair usando o formato padrão
            const extracted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractData"])(response.data, 'insurances', [
                'data'
            ]);
            // Se não houver insurances no formato padrão, processamos o array diretamente
            if (extracted.insurances && extracted.insurances.length > 0) {
                return extracted;
            } else {
                // Processar o array diretamente se a API retornar apenas um array
                const insurances = Array.isArray(response.data) ? response.data : [];
                return {
                    insurances,
                    total: insurances.length,
                    pages: Math.ceil(insurances.length / limit)
                };
            }
        } catch (error) {
            console.error("Erro ao buscar convênios:", error);
            throw error;
        }
    },
    // Obter um convênio específico
    getInsurance: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/insurances/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar convênio ${id}:`, error);
            throw error;
        }
    },
    // Obter um convênio pelo ID (versão simplificada que não lança erro)
    getInsuranceById: async (id)=>{
        try {
            console.log(`Buscando convênio com ID: ${id}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/insurances/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar convênio com ID ${id}:`, error);
            return null;
        }
    },
    // Criar um novo convênio
    createInsurance: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post('/insurances', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao criar convênio:", error);
            throw error;
        }
    },
    // Atualizar um convênio existente
    updateInsurance: async (id, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].put(`/insurances/${id}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar convênio ${id}:`, error);
            throw error;
        }
    },
    // Excluir um convênio
    deleteInsurance: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].delete(`/insurances/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir convênio ${id}:`, error);
            throw error;
        }
    },
    // Adicionar um convênio a uma pessoa
    addPersonInsurance: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post('/insurances/person', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao adicionar convênio à pessoa:", error);
            throw error;
        }
    },
    // Remover um convênio de uma pessoa
    removePersonInsurance: async (personId, insuranceId)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].delete(`/insurances/person/${personId}/${insuranceId}`);
            return true;
        } catch (error) {
            console.error("Erro ao remover convênio da pessoa:", error);
            throw error;
        }
    },
    // Listar convênios de uma pessoa - VERSÃO MELHORADA
    listPersonInsurances: async (personId)=>{
        try {
            console.log(`Buscando convênios para a pessoa ID: ${personId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/insurances/person/${personId}`);
            // Detecta a estrutura retornada e normaliza
            const data = response.data;
            // Log para debug da estrutura de dados
            console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);
            // Se for array, retorna diretamente
            if (Array.isArray(data)) {
                console.log(`Encontrados ${data.length} convênios no formato de array`);
                return data;
            } else if (data && typeof data === 'object') {
                // Tenta encontrar um array dentro do objeto
                const possibleArrayProps = [
                    'personInsurances',
                    'insurances',
                    'data',
                    'items',
                    'results'
                ];
                // Primeiro procura nas propriedades comuns
                for (const prop of possibleArrayProps){
                    if (Array.isArray(data[prop])) {
                        console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
                        return data[prop];
                    }
                }
                // Se não encontrou nas propriedades comuns, verifica todas as propriedades
                for (const prop of Object.keys(data)){
                    if (Array.isArray(data[prop])) {
                        console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
                        return data[prop];
                    }
                }
                // Tenta extrair informações de convênios mesmo que não estejam em um array direto
                if (data.insurance || data.insuranceId) {
                    console.log(`Encontrado um único convênio em formato não-array`);
                    const insurance = {
                        id: data.insuranceId || data.insurance?.id,
                        name: data.insuranceName || data.insurance?.name,
                        policyNumber: data.policyNumber,
                        validUntil: data.validUntil
                    };
                    return [
                        insurance
                    ];
                }
            }
            // Se não conseguir encontrar nenhum array, retorna array vazio
            console.warn("Estrutura de resposta inesperada:", data);
            return [];
        } catch (error) {
            console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);
            // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX
            return [];
        }
    },
    /**
   * Exporta a lista de convênios com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */ exportInsurances: async (filters, exportFormat = "xlsx")=>{
        try {
            // Obter os dados filtrados da API
            const response = await insurancesService.getInsurances({
                ...filters,
                limit: 1000 // Aumentamos o limite para exportar mais dados
            });
            // Extrair os dados dos convênios
            const insurancesArray = response.insurances || [];
            // Timestamp atual para o subtítulo
            const timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "dd/MM/yyyy 'às' HH:mm", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ptBR"]
            });
            // Definição das colunas com formatação
            const columns = [
                {
                    key: "name",
                    header: "Nome"
                },
                {
                    key: "companyName",
                    header: "Empresa"
                },
                {
                    key: "createdAt",
                    header: "Data de Cadastro",
                    type: "date"
                }
            ];
            // Preparar os dados para exportação
            const preparedData = insurancesArray.map((insurance)=>{
                return {
                    name: insurance.name || "",
                    companyName: insurance.company && insurance.company.name ? insurance.company.name : "",
                    createdAt: insurance.createdAt || ""
                };
            });
            // Filtros aplicados para subtítulo
            let subtitleParts = [];
            if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
            if (filters.insuranceIds && filters.insuranceIds.length > 0) {
                subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);
            }
            if (filters.companyId) {
                // Tentar encontrar o nome da empresa nos dados
                const companyName = insurancesArray.find((i)=>i.company && i.company.id === filters.companyId)?.company?.name;
                subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);
            }
            // Construir o subtítulo
            let subtitle = `Exportado em: ${timestamp}`;
            if (subtitleParts.length > 0) {
                subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
            }
            // Exportar os dados
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["exportService"].exportData(preparedData, {
                format: exportFormat,
                filename: "convenios",
                columns,
                title: "Lista de Convênios",
                subtitle
            });
        } catch (error) {
            console.error("Erro ao exportar convênios:", error);
            return false;
        }
    },
    updatePersonInsurance: async (personId, insuranceId, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].put(`/insurances/person/${personId}/${insuranceId}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);
            throw error;
        }
    },
    // Obter detalhes de um convênio específico de uma pessoa
    getPersonInsurance: async (personId, insuranceId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/insurances/person/${personId}/${insuranceId}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = insurancesService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/modules/admin/services/companyService.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "companyService": (()=>companyService),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-client] (ecmascript)");
;
const companyService = {
    // Obter a empresa atual do usuário autenticado
    getCurrentCompany: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get('/companies/current');
            return response.data;
        } catch (error) {
            console.error("Erro ao buscar empresa atual:", error);
            throw error;
        }
    },
    // Listar empresas com suporte a paginação e filtros
    getCompanies: async ({ page = 1, limit = 10, search, active } = {})=>{
        try {
            const params = new URLSearchParams();
            if (page) params.append('page', page);
            if (limit) params.append('limit', limit);
            if (search) params.append('search', search);
            if (active !== undefined) params.append('active', active);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/companies?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error("Erro ao buscar empresas:", error);
            throw error;
        }
    },
    // Obter uma empresa específica
    getCompany: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/companies/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar empresa ${id}:`, error);
            throw error;
        }
    },
    // Obter lista de empresas para formulários de seleção
    getCompaniesForSelect: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get("/companies/select");
            return response.data.companies || [];
        } catch (error) {
            console.error("Erro ao buscar empresas para seleção:", error);
            // Retorna array vazio em caso de erro para facilitar o manuseio no frontend
            return [];
        }
    },
    // Criar uma nova empresa
    createCompany: async (formData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].post('/companies', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Erro ao criar empresa:", error);
            throw error;
        }
    },
    // Atualizar uma empresa existente
    updateCompany: async (id, formData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].put(`/companies/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar empresa ${id}:`, error);
            throw error;
        }
    },
    // Alternar o status de uma empresa (ativo/inativo)
    toggleCompanyStatus: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].patch(`/companies/${id}/status`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao alterar status da empresa ${id}:`, error);
            throw error;
        }
    },
    // Excluir uma empresa
    deleteCompany: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].delete(`/companies/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir empresa ${id}:`, error);
            throw error;
        }
    },
    // Obter o logo de uma empresa
    getCompanyLogo: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get(`/companies/${id}/logo`, {
                responseType: 'blob'
            });
            return URL.createObjectURL(response.data);
        } catch (error) {
            console.error(`Erro ao obter logo da empresa ${id}:`, error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = companyService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/ModalButton.js [app-client] (ecmascript) <export default as ModalButton>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModalButton": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModalButton.js [app-client] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleFormGroup.js [app-client] (ecmascript) <export default as ModuleFormGroup>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleFormGroup": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleFormGroup.js [app-client] (ecmascript)");
}}),
"[project]/src/components/people/InsurancesFormModal.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insurancesService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/admin/services/companyService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__ = __turbopack_import__("[project]/src/components/ui/ModalButton.js [app-client] (ecmascript) <export default as ModalButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleModal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleModal$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleModal.js [app-client] (ecmascript) <export default as ModuleModal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleFormGroup.js [app-client] (ecmascript) <export default as ModuleFormGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleInput.js [app-client] (ecmascript) <export default as ModuleInput>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/building.js [app-client] (ecmascript) <export default as Building>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleSelect.js [app-client] (ecmascript) <export default as ModuleSelect>");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
;
const InsuranceFormModal = ({ isOpen, onClose, insurance = null, onSuccess })=>{
    _s();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        name: "",
        companyId: ""
    });
    const [companies, setCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingCompanies, setIsLoadingCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, isSystemAdmin } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // Se não for system admin, deve usar a empresa do usuário
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InsuranceFormModal.useEffect": ()=>{
            if (!isSystemAdmin && user?.companyId) {
                setFormData({
                    "InsuranceFormModal.useEffect": (prev)=>({
                            ...prev,
                            companyId: user.companyId
                        })
                }["InsuranceFormModal.useEffect"]);
            }
        }
    }["InsuranceFormModal.useEffect"], [
        isSystemAdmin,
        user?.companyId
    ]);
    // Carregar empresas (se você tiver esse serviço)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InsuranceFormModal.useEffect": ()=>{
            const loadCompanies = {
                "InsuranceFormModal.useEffect.loadCompanies": async ()=>{
                    // Se não for system admin, não precisa carregar empresas
                    if (!isSystemAdmin) return;
                    setIsLoadingCompanies(true);
                    try {
                        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["companyService"].getCompaniesForSelect();
                        setCompanies(response || []);
                    } catch (error) {
                        console.error("Erro ao carregar empresas:", error);
                        setError("Não foi possível carregar a lista de empresas.");
                    } finally{
                        setIsLoadingCompanies(false);
                    }
                }
            }["InsuranceFormModal.useEffect.loadCompanies"];
            if (isOpen) {
                loadCompanies();
            }
        }
    }["InsuranceFormModal.useEffect"], [
        isOpen,
        isSystemAdmin
    ]);
    // Preencher o formulário se estivermos editando
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InsuranceFormModal.useEffect": ()=>{
            if (insurance) {
                // Ao editar, carregar os dados do convênio
                setFormData({
                    name: insurance.name || "",
                    // Se não for system admin, forçar a empresa do usuário
                    companyId: isSystemAdmin ? insurance.companyId || "" : user?.companyId || ""
                });
            } else {
                // Reset do formulário para novo convênio
                setFormData({
                    name: "",
                    // Se não for system admin, definir a empresa do usuário
                    companyId: isSystemAdmin ? "" : user?.companyId || ""
                });
            }
        }
    }["InsuranceFormModal.useEffect"], [
        insurance,
        isSystemAdmin,
        user?.companyId
    ]);
    const handleChange = (e)=>{
        const { name, value } = e.target;
        setFormData((prev)=>({
                ...prev,
                [name]: value
            }));
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        setIsSubmitting(true);
        setError(null);
        try {
            // Garantir que companyId esteja definido para não-admin
            const dataToSubmit = {
                ...formData,
                // Se não for system admin, garantir que use a empresa do usuário
                companyId: isSystemAdmin ? formData.companyId : user?.companyId
            };
            if (insurance) {
                // Editar convênio existente
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].updateInsurance(insurance.id, dataToSubmit);
            } else {
                // Criar novo convênio
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].createInsurance(dataToSubmit);
            }
            onSuccess();
            onClose();
        } catch (err) {
            console.error("Erro ao salvar convênio:", err);
            setError(err.response?.data?.message || "Ocorreu um erro ao salvar o convênio.");
        } finally{
            setIsSubmitting(false);
        }
    };
    // Componente de rodapé com botões
    const modalFooter = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex justify-end gap-3",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__["ModalButton"], {
                variant: "secondary",
                moduleColor: "people",
                onClick: onClose,
                disabled: isSubmitting,
                children: "Cancelar"
            }, void 0, false, {
                fileName: "[project]/src/components/people/InsurancesFormModal.js",
                lineNumber: 118,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__["ModalButton"], {
                variant: "primary",
                moduleColor: "people",
                type: "submit",
                form: "insurance-form",
                isLoading: isSubmitting,
                children: insurance ? "Atualizar" : "Salvar"
            }, void 0, false, {
                fileName: "[project]/src/components/people/InsurancesFormModal.js",
                lineNumber: 127,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/people/InsurancesFormModal.js",
        lineNumber: 117,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleModal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleModal$3e$__["ModuleModal"], {
        isOpen: isOpen,
        onClose: onClose,
        title: insurance ? "Editar Convênio" : "Novo Convênio",
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
            size: 22
        }, void 0, false, {
            fileName: "[project]/src/components/people/InsurancesFormModal.js",
            lineNumber: 144,
            columnNumber: 13
        }, void 0),
        moduleColor: "people",
        size: "md",
        animateExit: true,
        footer: modalFooter,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            id: "insurance-form",
            onSubmit: handleSubmit,
            className: "overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6",
            children: [
                error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                            size: 16
                        }, void 0, false, {
                            fileName: "[project]/src/components/people/InsurancesFormModal.js",
                            lineNumber: 153,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/components/people/InsurancesFormModal.js",
                            lineNumber: 154,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/people/InsurancesFormModal.js",
                    lineNumber: 152,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Nome do Convênio",
                    htmlFor: "name",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 162,
                        columnNumber: 19
                    }, void 0),
                    required: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                        moduleColor: "people",
                        type: "text",
                        id: "name",
                        name: "name",
                        value: formData.name,
                        onChange: handleChange,
                        required: true,
                        placeholder: "Digite o nome do convênio"
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 165,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsurancesFormModal.js",
                    lineNumber: 158,
                    columnNumber: 11
                }, this),
                isSystemAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Empresa (opcional)",
                    htmlFor: "companyId",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 183,
                        columnNumber: 21
                    }, void 0),
                    helpText: "Se não selecionado, o convênio estará disponível para toda a plataforma.",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                        moduleColor: "people",
                        id: "companyId",
                        name: "companyId",
                        value: formData.companyId,
                        onChange: handleChange,
                        placeholder: "Selecione uma empresa (Opcional)",
                        children: isLoadingCompanies ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                            disabled: true,
                            children: "Carregando empresas..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/people/InsurancesFormModal.js",
                            lineNumber: 195,
                            columnNumber: 19
                        }, this) : companies.map((company)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: company.id,
                                children: company.name
                            }, company.id, false, {
                                fileName: "[project]/src/components/people/InsurancesFormModal.js",
                                lineNumber: 198,
                                columnNumber: 21
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 186,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsurancesFormModal.js",
                    lineNumber: 179,
                    columnNumber: 13
                }, this),
                !isSystemAdmin && user?.companyId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Empresa",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 212,
                        columnNumber: 21
                    }, void 0),
                    helpText: "O convênio será associado automaticamente à sua empresa.",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300",
                        children: user.companyName || "Sua empresa"
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsurancesFormModal.js",
                        lineNumber: 215,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsurancesFormModal.js",
                    lineNumber: 209,
                    columnNumber: 13
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/people/InsurancesFormModal.js",
            lineNumber: 150,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/people/InsurancesFormModal.js",
        lineNumber: 140,
        columnNumber: 5
    }, this);
};
_s(InsuranceFormModal, "IRaouIhVUq7GpwNjfZNpfTSPIwA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = InsuranceFormModal;
const __TURBOPACK__default__export__ = InsuranceFormModal;
var _c;
__turbopack_refresh__.register(_c, "InsuranceFormModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/ExportMenu.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-client] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js [app-client] (ecmascript) <export default as FileSpreadsheet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-client] (ecmascript) <export default as ConstructionButton>");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
const ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' })=>{
    _s();
    const [dropdownOpen, setDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dropdownPosition, setDropdownPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        top: 0,
        right: 0,
        width: 0
    });
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Montar o componente apenas no cliente
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ExportMenu.useEffect": ()=>{
            setMounted(true);
            return ({
                "ExportMenu.useEffect": ()=>setMounted(false)
            })["ExportMenu.useEffect"];
        }
    }["ExportMenu.useEffect"], []);
    // Calcular a posição do dropdown quando aberto
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ExportMenu.useEffect": ()=>{
            if (dropdownOpen && buttonRef.current) {
                const rect = buttonRef.current.getBoundingClientRect();
                setDropdownPosition({
                    top: rect.bottom + window.scrollY,
                    right: window.innerWidth - rect.right,
                    width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)
                });
            }
        }
    }["ExportMenu.useEffect"], [
        dropdownOpen
    ]);
    // Fecha o dropdown ao clicar fora dele
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ExportMenu.useEffect": ()=>{
            const handleClickOutside = {
                "ExportMenu.useEffect.handleClickOutside": (event)=>{
                    if (buttonRef.current && !buttonRef.current.contains(event.target) && dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                        setDropdownOpen(false);
                    }
                }
            }["ExportMenu.useEffect.handleClickOutside"];
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "ExportMenu.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["ExportMenu.useEffect"];
        }
    }["ExportMenu.useEffect"], []);
    const handleExport = (format)=>{
        onExport(format);
        setDropdownOpen(false);
    };
    // Se estiver em construção, mostrar o botão de construção
    if (underConstruction) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
            className: "flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors",
            title: "Exportação em Construção",
            content: "A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.",
            icon: "FileText",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                    size: 16
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: "Exportar"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                    size: 14
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 69,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/ExportMenu.js",
            lineNumber: 61,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                ref: buttonRef,
                onClick: ()=>setDropdownOpen(!dropdownOpen),
                className: `flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`,
                disabled: isExporting || disabled && !underConstruction,
                title: disabled ? "Não há dados para exportar" : "Exportar dados",
                children: [
                    isExporting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                        size: 16,
                        className: "animate-spin"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 84,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: isExporting ? "Exportando..." : "Exportar"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                        size: 14,
                        className: `transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 76,
                columnNumber: 7
            }, this),
            dropdownOpen && mounted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropdownRef,
                className: "fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700",
                style: {
                    top: `${dropdownPosition.top}px`,
                    right: `${dropdownPosition.right}px`,
                    width: `${dropdownPosition.width}px`
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-sm font-medium text-neutral-700 dark:text-gray-200",
                            children: "Formato de exportação"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ExportMenu.js",
                            lineNumber: 104,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 103,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('image'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                        size: 16,
                                        className: "text-blue-500 dark:text-blue-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Imagem (PNG)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('pdf'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                        size: 16,
                                        className: "text-red-500 dark:text-red-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "PDF"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 119,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('xlsx'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__["FileSpreadsheet"], {
                                        size: 16,
                                        className: "text-green-500 dark:text-green-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Excel (XLSX)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 126,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 94,
                columnNumber: 9
            }, this), document.body)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/ExportMenu.js",
        lineNumber: 75,
        columnNumber: 5
    }, this);
};
_s(ExportMenu, "UU62FXCzf0eNcO/Uq8JGsrTJ704=");
_c = ExportMenu;
const __TURBOPACK__default__export__ = ExportMenu;
var _c;
__turbopack_refresh__.register(_c, "ExportMenu");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/ModuleTable.js [app-client] (ecmascript) <export default as ModuleTable>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleTable": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleTable.js [app-client] (ecmascript)");
}}),
"[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialManager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialTriggerButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialTriggerButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleHeader.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/multi-select.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insurancesService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/admin/services/companyService.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ConfirmationDialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ConfirmationDialog.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$people$2f$InsurancesFormModal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/people/InsurancesFormModal.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ExportMenu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ExportMenu.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/building.js [app-client] (ecmascript) <export default as Building>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/filter.js [app-client] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleInput.js [app-client] (ecmascript) <export default as ModuleInput>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleSelect.js [app-client] (ecmascript) <export default as ModuleSelect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-client] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTable$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleTable.js [app-client] (ecmascript) <export default as ModuleTable>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-client] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/trash.js [app-client] (ecmascript) <export default as Trash>");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Tutorial steps para a página de convênios
const insurancesTutorialSteps = [
    {
        title: "Convênios",
        content: "Esta tela permite gerenciar os convênios disponíveis no sistema.",
        selector: "h1",
        position: "bottom"
    },
    {
        title: "Adicionar Novo Convênio",
        content: "Clique aqui para adicionar um novo convênio.",
        selector: "button:has(span:contains('Novo Convênio'))",
        position: "left"
    },
    {
        title: "Filtrar Convênios",
        content: "Use esta barra de pesquisa para encontrar convênios específicos pelo nome.",
        selector: "input[placeholder*='Buscar']",
        position: "bottom"
    },
    {
        title: "Filtrar por Múltiplos Convênios",
        content: "Selecione um ou mais convênios pelo nome para filtrar a lista.",
        selector: "div:has(> label:contains('Filtrar por Convênios'))",
        position: "bottom"
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de convênios em diferentes formatos usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Gerenciar Convênios",
        content: "Edite ou exclua convênios existentes usando os botões de ação na tabela.",
        selector: "table",
        position: "top"
    }
];
const InsurancesPage = ()=>{
    _s();
    const { user: currentUser } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const isAdmin = currentUser?.modules?.includes("ADMIN") || currentUser?.modules?.includes("RH");
    const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
    const [insurances, setInsurances] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [search, setSearch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [insurancesFilter, setInsurancesFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [insuranceOptions, setInsuranceOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoadingInsuranceOptions, setIsLoadingInsuranceOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedInsurance, setSelectedInsurance] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [insuranceFormOpen, setInsuranceFormOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isExporting, setIsExporting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Estados para paginação
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [totalPages, setTotalPages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(1);
    const [totalItems, setTotalItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // Estados para filtro de empresa (apenas para system_admin)
    const [companies, setCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [companyFilter, setCompanyFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [isLoadingCompanies, setIsLoadingCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Constante para itens por página
    const ITEMS_PER_PAGE = 10;
    // Função para carregar opções de convênios para o multi-select
    const loadInsuranceOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "InsurancesPage.useCallback[loadInsuranceOptions]": async ()=>{
            setIsLoadingInsuranceOptions(true);
            try {
                // Carregar todos os convênios para o multi-select (com limite maior)
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].getInsurances({
                    limit: 100 // Limite maior para ter mais opções
                });
                const options = response?.insurances?.map({
                    "InsurancesPage.useCallback[loadInsuranceOptions]": (insurance)=>({
                            value: insurance.id,
                            label: insurance.name,
                            // Guardar o nome para ordenação
                            sortName: insurance.name.toLowerCase()
                        })
                }["InsurancesPage.useCallback[loadInsuranceOptions]"]) || [];
                // Ordenar as opções alfabeticamente pelo nome
                const sortedOptions = options.sort({
                    "InsurancesPage.useCallback[loadInsuranceOptions].sortedOptions": (a, b)=>a.sortName.localeCompare(b.sortName)
                }["InsurancesPage.useCallback[loadInsuranceOptions].sortedOptions"]);
                setInsuranceOptions(sortedOptions);
            } catch (error) {
                console.error("Erro ao carregar opções de convênios:", error);
                setInsuranceOptions([]);
            } finally{
                setIsLoadingInsuranceOptions(false);
            }
        }
    }["InsurancesPage.useCallback[loadInsuranceOptions]"], []);
    // Função para carregar empresas (apenas para system_admin)
    const loadCompanies = async ()=>{
        if (!isSystemAdmin) return;
        setIsLoadingCompanies(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["companyService"].getCompaniesForSelect();
            setCompanies(response);
        } catch (error) {
            console.error("Erro ao carregar empresas:", error);
        } finally{
            setIsLoadingCompanies(false);
        }
    };
    const loadInsurances = async (page = currentPage, searchQuery = search, insuranceIds = insurancesFilter, company = companyFilter)=>{
        setIsLoading(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].getInsurances({
                search: searchQuery || undefined,
                insuranceIds: insuranceIds.length > 0 ? insuranceIds : undefined,
                companyId: company || undefined,
                page,
                limit: ITEMS_PER_PAGE
            });
            console.log('Dados de convênios recebidos:', response);
            // Verificar se temos os dados no formato esperado
            if (response && typeof response === 'object') {
                // Extrair os convênios, total e páginas
                const insurancesArray = response.insurances || [];
                const total = response.total || insurancesArray.length;
                const pages = response.pages || Math.ceil(total / ITEMS_PER_PAGE);
                console.log('Array de convênios extraído:', insurancesArray);
                console.log('Total de itens:', total);
                console.log('Total de páginas:', pages);
                // Atualizar o estado
                setInsurances(insurancesArray);
                setTotalItems(total);
                setTotalPages(pages);
                setCurrentPage(page);
            } else {
                console.error('Dados de convênios inválidos:', response);
                setInsurances([]);
                setTotalItems(0);
                setTotalPages(1);
            }
        } catch (error) {
            console.error("Erro ao carregar convênios:", error);
            setInsurances([]);
            setTotalItems(0);
            setTotalPages(1);
        } finally{
            setIsLoading(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InsurancesPage.useEffect": ()=>{
            loadInsurances();
            loadInsuranceOptions();
            // Carregar empresas se o usuário for system_admin
            if (isSystemAdmin) {
                loadCompanies();
            }
        }
    }["InsurancesPage.useEffect"], [
        loadInsuranceOptions,
        isSystemAdmin
    ]);
    const handleSearch = (e)=>{
        e.preventDefault();
        loadInsurances(1, search, insurancesFilter, companyFilter);
    };
    const handleInsurancesFilterChange = (value)=>{
        setInsurancesFilter(value);
        loadInsurances(1, search, value, companyFilter);
    };
    const handleCompanyFilterChange = (value)=>{
        setCompanyFilter(value);
        loadInsurances(1, search, insurancesFilter, value);
    };
    const handleResetFilters = ()=>{
        setSearch("");
        setInsurancesFilter([]);
        setCompanyFilter("");
        loadInsurances(1, "", [], "");
    };
    // Função para lidar com a mudança de página
    const handlePageChange = (page)=>{
        loadInsurances(page, search, insurancesFilter, companyFilter);
    };
    const handleEditInsurance = (insurance)=>{
        setSelectedInsurance(insurance);
        setInsuranceFormOpen(true);
    };
    const handleDeleteInsurance = (insurance)=>{
        setSelectedInsurance(insurance);
        setConfirmationDialogOpen(true);
    };
    const handleExport = async (format)=>{
        setIsExporting(true);
        try {
            // Exportar usando os mesmos filtros da tabela atual
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].exportInsurances({
                search: search || undefined,
                insuranceIds: insurancesFilter.length > 0 ? insurancesFilter : undefined,
                companyId: companyFilter || undefined
            }, format);
        } catch (error) {
            console.error("Erro ao exportar convênios:", error);
        // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
        } finally{
            setIsExporting(false);
        }
    };
    const confirmDeleteInsurance = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["insurancesService"].deleteInsurance(selectedInsurance.id);
            loadInsurances();
        } catch (error) {
            console.error("Erro ao excluir convênio:", error);
        }
        setConfirmationDialogOpen(false);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-slate-800 dark:text-white flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"], {
                                size: 24,
                                className: "mr-2 text-orange-600 dark:text-orange-400"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 267,
                                columnNumber: 11
                            }, this),
                            "Convênios"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                        lineNumber: 266,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ExportMenu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                onExport: handleExport,
                                isExporting: isExporting,
                                disabled: isLoading || insurances.length === 0,
                                className: "text-orange-700 dark:text-orange-300"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 273,
                                columnNumber: 11
                            }, this),
                            isAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>{
                                    setSelectedInsurance(null);
                                    setInsuranceFormOpen(true);
                                },
                                className: "flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        size: 18
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                        lineNumber: 289,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: "Novo Convênio"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                        lineNumber: 290,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 282,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                        lineNumber: 271,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 265,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                title: "Filtros",
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                    size: 22,
                    className: "text-module-people-icon dark:text-module-people-icon-dark"
                }, void 0, false, {
                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                    lineNumber: 299,
                    columnNumber: 15
                }, void 0),
                description: "Gerencie os convênios disponíveis no sistema. Utilize o filtro abaixo para encontrar convênios específicos.",
                tutorialSteps: insurancesTutorialSteps,
                tutorialName: "insurances-overview",
                moduleColor: "people",
                filters: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSearch,
                    className: "flex flex-col gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col md:flex-row gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                            className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5 z-10"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 311,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                                            moduleColor: "people",
                                            type: "text",
                                            placeholder: "Buscar por nome do convênio...",
                                            value: search,
                                            onChange: (e)=>setSearch(e.target.value),
                                            className: "w-full pl-10"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 312,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 310,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col sm:flex-row gap-2",
                                    children: [
                                        isSystemAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                                            moduleColor: "people",
                                            value: companyFilter,
                                            onChange: (e)=>handleCompanyFilterChange(e.target.value),
                                            placeholder: "Empresa",
                                            disabled: isLoadingCompanies,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "",
                                                    children: "Todas as empresas"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                    lineNumber: 332,
                                                    columnNumber: 21
                                                }, void 0),
                                                companies.map((company)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: company.id,
                                                        children: company.name
                                                    }, company.id, false, {
                                                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                        lineNumber: 334,
                                                        columnNumber: 23
                                                    }, void 0))
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 325,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterButton"], {
                                            type: "submit",
                                            moduleColor: "people",
                                            variant: "primary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                                    size: 16,
                                                    className: "sm:hidden"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                    lineNumber: 342,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "hidden sm:inline",
                                                    children: "Filtrar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                    lineNumber: 343,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 341,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FilterButton"], {
                                            type: "button",
                                            onClick: handleResetFilters,
                                            moduleColor: "people",
                                            variant: "secondary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                    size: 16,
                                                    className: "sm:hidden"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                    lineNumber: 352,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "hidden sm:inline",
                                                    children: "Limpar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                    lineNumber: 353,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 346,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 322,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                            lineNumber: 309,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                label: "Filtrar por Convênios",
                                value: insurancesFilter,
                                onChange: handleInsurancesFilterChange,
                                options: insuranceOptions,
                                placeholder: "Selecione um ou mais convênios pelo nome...",
                                loading: isLoadingInsuranceOptions,
                                moduleOverride: "people"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 360,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                            lineNumber: 359,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                    lineNumber: 305,
                    columnNumber: 11
                }, void 0)
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 297,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTable$3e$__["ModuleTable"], {
                moduleColor: "people",
                columns: [
                    {
                        header: 'Nome',
                        field: 'name',
                        width: '40%'
                    },
                    {
                        header: 'Empresa',
                        field: 'company',
                        width: '40%'
                    },
                    {
                        header: 'Ações',
                        field: 'actions',
                        className: 'text-right',
                        width: '20%',
                        sortable: false
                    }
                ],
                data: insurances,
                isLoading: isLoading,
                emptyMessage: "Nenhum convênio encontrado",
                emptyIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"], {
                    size: 24
                }, void 0, false, {
                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                    lineNumber: 385,
                    columnNumber: 20
                }, void 0),
                tableId: "people-insurances-table",
                enableColumnToggle: true,
                defaultSortField: "name",
                defaultSortDirection: "asc",
                currentPage: currentPage,
                totalPages: totalPages,
                totalItems: totalItems,
                onPageChange: handlePageChange,
                showPagination: totalPages > 1,
                renderRow: (insurance, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                        className: moduleColors.hoverBg,
                        children: [
                            visibleColumns.includes('name') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building$3e$__["Building"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                lineNumber: 401,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 400,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-sm font-medium text-neutral-900 dark:text-neutral-100",
                                                children: insurance.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                lineNumber: 404,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 403,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 399,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 398,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('company') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: insurance.company ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                            className: "h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 416,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-neutral-700 dark:text-neutral-300",
                                            children: insurance.company.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 417,
                                            columnNumber: 21
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 415,
                                    columnNumber: 19
                                }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-neutral-400 dark:text-neutral-500 text-sm",
                                    children: "Convênio geral"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 420,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 413,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('actions') && isAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4 text-right",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-end gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleEditInsurance(insurance),
                                            className: "p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors",
                                            title: "Editar",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                lineNumber: 435,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 430,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleDeleteInsurance(insurance),
                                            className: "p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors",
                                            title: "Excluir",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                                lineNumber: 442,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                            lineNumber: 437,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                    lineNumber: 429,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                                lineNumber: 428,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, insurance.id, true, {
                        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                        lineNumber: 396,
                        columnNumber: 11
                    }, void 0)
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 375,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ConfirmationDialog$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: confirmationDialogOpen,
                onClose: ()=>setConfirmationDialogOpen(false),
                onConfirm: confirmDeleteInsurance,
                title: "Excluir Convênio",
                message: `Tem certeza que deseja excluir o convênio "${selectedInsurance?.name}"? Esta ação não pode ser desfeita.`,
                variant: "danger",
                confirmText: "Excluir",
                cancelText: "Cancelar"
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 452,
                columnNumber: 7
            }, this),
            insuranceFormOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$people$2f$InsurancesFormModal$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: insuranceFormOpen,
                onClose: ()=>setInsuranceFormOpen(false),
                insurance: selectedInsurance,
                onSuccess: ()=>{
                    setInsuranceFormOpen(false);
                    loadInsurances();
                }
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 465,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
                lineNumber: 477,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js",
        lineNumber: 263,
        columnNumber: 5
    }, this);
};
_s(InsurancesPage, "8u03UUclkMpPaRVoVmjJZapZGE0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = InsurancesPage;
const __TURBOPACK__default__export__ = InsurancesPage;
var _c;
__turbopack_refresh__.register(_c, "InsurancesPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/people/insurances/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>InsurancesRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$InsurancesPage$2f$InsurancesPage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/InsurancesPage/InsurancesPage.js [app-client] (ecmascript)");
"use client";
;
;
function InsurancesRoute() {
    // Se o componente não existir, você precisará criá-lo
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$InsurancesPage$2f$InsurancesPage$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/app/dashboard/people/insurances/page.js",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
_c = InsurancesRoute;
var _c;
__turbopack_refresh__.register(_c, "InsurancesRoute");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/people/insurances/page.js [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_35e344._.js.map