{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ui/ExportMenu.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, Construction, Image } from \"lucide-react\";\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\n\r\nconst ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' }) => {\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n  const buttonRef = useRef(null);\r\n  const dropdownRef = useRef(null);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Fecha o dropdown ao clicar fora dele\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleExport = (format) => {\r\n    onExport(format);\r\n    setDropdownOpen(false);\r\n  };\r\n\r\n  // Se estiver em construção, mostrar o botão de construção\r\n  if (underConstruction) {\r\n    return (\r\n      <ConstructionButton\r\n        className=\"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors\"\r\n        title=\"Exportação em Construção\"\r\n        content=\"A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.\"\r\n        icon=\"FileText\"\r\n      >\r\n        <Download size={16} />\r\n        <span>Exportar</span>\r\n        <ChevronDown size={14} />\r\n      </ConstructionButton>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        ref={buttonRef}\r\n        onClick={() => setDropdownOpen(!dropdownOpen)}\r\n        className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`}\r\n        disabled={isExporting || (disabled && !underConstruction)}\r\n        title={disabled ? \"Não há dados para exportar\" : \"Exportar dados\"}\r\n      >\r\n        {isExporting ? (\r\n          <Loader2 size={16} className=\"animate-spin\" />\r\n        ) : (\r\n          <Download size={16} />\r\n        )}\r\n        <span>{isExporting ? \"Exportando...\" : \"Exportar\"}</span>\r\n        <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n      {dropdownOpen && mounted && createPortal(\r\n        <div\r\n          ref={dropdownRef}\r\n          className=\"fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n          style={{\r\n            top: `${dropdownPosition.top}px`,\r\n            right: `${dropdownPosition.right}px`,\r\n            width: `${dropdownPosition.width}px`,\r\n          }}\r\n        >\r\n          <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n            <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Formato de exportação</h4>\r\n          </div>\r\n          <div className=\"p-1\">\r\n            <button\r\n              onClick={() => handleExport('image')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <Image size={16} className=\"text-blue-500 dark:text-blue-400\" />\r\n              <span>Imagem (PNG)</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('pdf')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileText size={16} className=\"text-red-500 dark:text-red-400\" />\r\n              <span>PDF</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('xlsx')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileSpreadsheet size={16} className=\"text-green-500 dark:text-green-400\" />\r\n              <span>Excel (XLSX)</span>\r\n            </button>\r\n          </div>\r\n        </div>,\r\n        document.body\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportMenu;"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;;AAQA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,oBAAoB,KAAK,EAAE,YAAY,EAAE,EAAE;IAChH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IACtF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,gBAAgB;IAClB;IAEA,0DAA0D;IAC1D,IAAI,mBAAmB;QACrB,qBACE,8OAAC,wMAAA,CAAA,qBAAkB;YACjB,WAAU;YACV,OAAM;YACN,SAAQ;YACR,MAAK;;8BAEL,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;8BAChB,8OAAC;8BAAK;;;;;;8BACN,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAW,CAAC,+GAA+G,EAAE,UAAU,QAAQ,CAAC,gBAAgB,6CAA6C,4FAA4F,CAAC,EAAE,WAAW;gBACvT,UAAU,eAAgB,YAAY,CAAC;gBACvC,OAAO,WAAW,+BAA+B;;oBAEhD,4BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;kCAElB,8OAAC;kCAAM,cAAc,kBAAkB;;;;;;kCACvC,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;YAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oBACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBACtC;;kCAEA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAE1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;sBAIZ,SAAS,IAAI;;;;;;;AAIrB;uCAEe"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ui/Modal.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { createPortal } from 'react-dom';\r\nimport { X } from 'lucide-react';\r\n\r\nconst Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Efeito para prevenir scroll quando o modal estiver aberto\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = '';\r\n    }\r\n    return () => {\r\n      document.body.style.overflow = '';\r\n    };\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen || !mounted) return null;\r\n\r\n  // Determinar a largura do modal com base no tamanho\r\n  const sizeClasses = {\r\n    sm: 'max-w-md',\r\n    md: 'max-w-2xl',\r\n    lg: 'max-w-4xl',\r\n    xl: 'max-w-6xl',\r\n    full: 'max-w-full mx-4'\r\n  };\r\n\r\n  // Usar createPortal para renderizar o modal no nível mais alto do DOM\r\n  const modalContent = (\r\n    <div className=\"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\">\r\n      {/* Overlay de fundo escuro */}\r\n      <div className=\"fixed inset-0 bg-black/50\" onClick={onClose}></div>\r\n\r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 ${sizeClasses[size]} w-full max-h-[90vh] flex flex-col z-[11050]`}>\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">\r\n            {title}\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Conteúdo */}\r\n        <div className=\"overflow-y-auto flex-1\">\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Renderizar o modal usando um portal para garantir que ele fique acima de tudo\r\n  return createPortal(modalContent, document.body);\r\n};\r\n\r\nexport default Modal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QACA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,oDAAoD;IACpD,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,sEAAsE;IACtE,MAAM,6BACJ,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;gBAA4B,SAAS;;;;;;0BAEpD,8OAAC;gBAAI,WAAW,CAAC,6EAA6E,EAAE,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC;;kCAE7J,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,gFAAgF;IAChF,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;uCAEe"}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/permissions/Protected.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { usePermissions } from '@/hooks/usePermissions';\r\n\r\n// Componente que renderiza condicionalmente baseado em permissões\r\nexport function Protected({ \r\n  permission, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { can, canAll, canAny } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (!permission) return true;\r\n    \r\n    if (Array.isArray(permission)) {\r\n      return requireAll ? canAll(permission) : canAny(permission);\r\n    }\r\n    \r\n    return can(permission);\r\n  }, [permission, requireAll, can, canAll, canAny]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza condicionalmente baseado em módulos\r\nexport function ProtectedModule({ \r\n  module, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { hasModule, isAdmin } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (isAdmin()) return true;\r\n    \r\n    if (!module) return true;\r\n    \r\n    if (Array.isArray(module)) {\r\n      return requireAll \r\n        ? module.every(m => hasModule(m))\r\n        : module.some(m => hasModule(m));\r\n    }\r\n    \r\n    return hasModule(module);\r\n  }, [module, requireAll, hasModule, isAdmin]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza apenas para administradores\r\nexport function AdminOnly({ fallback = null, children }) {\r\n  const { isAdmin } = usePermissions();\r\n  \r\n  return isAdmin() ? children : fallback;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAMO,SAAS,UAAU,EACxB,UAAU,EACV,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE7C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,MAAM,OAAO,CAAC,aAAa;YAC7B,OAAO,aAAa,OAAO,cAAc,OAAO;QAClD;QAEA,OAAO,IAAI;IACb,GAAG;QAAC;QAAY;QAAY;QAAK;QAAQ;KAAO;IAEhD,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,gBAAgB,EAC9B,MAAM,EACN,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE5C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,WAAW,OAAO;QAEtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,OAAO,aACH,OAAO,KAAK,CAAC,CAAA,IAAK,UAAU,MAC5B,OAAO,IAAI,CAAC,CAAA,IAAK,UAAU;QACjC;QAEA,OAAO,UAAU;IACnB,GAAG;QAAC;QAAQ;QAAY;QAAW;KAAQ;IAE3C,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,UAAU,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACrD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,YAAY,WAAW;AAChC"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/permissions/PermissionsModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  X,\r\n  Loader2,\r\n  Shield,\r\n  AlertCircle,\r\n  Settings,\r\n  Users,\r\n  DollarSign,\r\n  Calendar,\r\n  CheckSquare,\r\n  ChevronDown,\r\n  ChevronRight,\r\n  Info,\r\n  Search,\r\n} from \"lucide-react\";\r\nimport { api } from \"@/utils/api\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { userService } from \"@/app/modules/admin/services/userService\";\r\nimport {\r\n  PERMISSIONS_CONFIG,\r\n  getAllPermissions,\r\n} from \"@/utils/permissionConfig\";\r\n\r\nconst PermissionsModal = ({ isOpen, onClose, user, onSuccess }) => {\r\n  const { user: currentUser } = useAuth();\r\n  const [selectedPermissions, setSelectedPermissions] = useState([]);\r\n  const [expandedModules, setExpandedModules] = useState({});\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isSaving, setIsSaving] = useState(false);\r\n  const [error, setError] = useState(\"\");\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [filteredPermissions, setFilteredPermissions] = useState([]);\r\n\r\n  // Carregar permissões do usuário ao abrir o modal\r\n  useEffect(() => {\r\n    if (user && isOpen) {\r\n      setIsLoading(true);\r\n\r\n      // Se usuário já tem módulos/permissões, configuramos o estado inicial\r\n      if (user.permissions) {\r\n        setSelectedPermissions(user.permissions);\r\n      } else {\r\n        // Se não tiver permissões, inicializamos com array vazio\r\n        setSelectedPermissions([]);\r\n      }\r\n\r\n      // Inicialmente, expandimos apenas os módulos que o usuário tem acesso\r\n      const initialExpandedState = {};\r\n      user.modules?.forEach((moduleId) => {\r\n        initialExpandedState[moduleId] = true;\r\n      });\r\n      setExpandedModules(initialExpandedState);\r\n\r\n      setIsLoading(false);\r\n    }\r\n  }, [user, isOpen]);\r\n\r\n  // Filtragem de permissões baseada na busca\r\n  useEffect(() => {\r\n    if (!searchTerm.trim()) {\r\n      setFilteredPermissions(getAllPermissions());\r\n      return;\r\n    }\r\n\r\n    const lowerSearch = searchTerm.toLowerCase();\r\n    const filtered = getAllPermissions().filter(\r\n      (permission) =>\r\n        permission.name.toLowerCase().includes(lowerSearch) ||\r\n        permission.description.toLowerCase().includes(lowerSearch) ||\r\n        permission.id.toLowerCase().includes(lowerSearch) ||\r\n        PERMISSIONS_CONFIG[permission.moduleId].name\r\n          .toLowerCase()\r\n          .includes(lowerSearch)\r\n    );\r\n\r\n    setFilteredPermissions(filtered);\r\n\r\n    // Expande automaticamente os módulos que têm permissões que correspondem à busca\r\n    const modulesToExpand = {};\r\n    filtered.forEach((permission) => {\r\n      modulesToExpand[permission.moduleId] = true;\r\n    });\r\n\r\n    setExpandedModules((prev) => ({\r\n      ...prev,\r\n      ...modulesToExpand,\r\n    }));\r\n  }, [searchTerm]);\r\n\r\n  const isAdmin = currentUser?.modules?.includes(\"ADMIN\");\r\n\r\n  // Verificar se o usuário tem uma permissão específica\r\n  const hasPermission = (permissionId) => {\r\n    return selectedPermissions.includes(permissionId);\r\n  };\r\n\r\n  // Alternar uma permissão específica\r\n  const togglePermission = (permissionId) => {\r\n    setSelectedPermissions((prev) => {\r\n      if (prev.includes(permissionId)) {\r\n        return prev.filter((id) => id !== permissionId);\r\n      } else {\r\n        return [...prev, permissionId];\r\n      }\r\n    });\r\n  };\r\n\r\n  // Alternar todas as permissões de um módulo\r\n  const toggleModulePermissions = (moduleId) => {\r\n    const modulePermissions = PERMISSIONS_CONFIG[moduleId].permissions.map(\r\n      (p) => p.id\r\n    );\r\n\r\n    // Verificar se todas as permissões do módulo já estão selecionadas\r\n    const allSelected = modulePermissions.every((p) =>\r\n      selectedPermissions.includes(p)\r\n    );\r\n\r\n    if (allSelected) {\r\n      // Remover todas as permissões do módulo\r\n      setSelectedPermissions((prev) =>\r\n        prev.filter((p) => !modulePermissions.includes(p))\r\n      );\r\n    } else {\r\n      // Adicionar todas as permissões do módulo\r\n      setSelectedPermissions((prev) => {\r\n        const newPermissions = [...prev];\r\n        modulePermissions.forEach((p) => {\r\n          if (!newPermissions.includes(p)) {\r\n            newPermissions.push(p);\r\n          }\r\n        });\r\n        return newPermissions;\r\n      });\r\n    }\r\n  };\r\n\r\n  // Alternar a expansão de um módulo\r\n  const toggleModuleExpansion = (moduleId) => {\r\n    setExpandedModules((prev) => ({\r\n      ...prev,\r\n      [moduleId]: !prev[moduleId],\r\n    }));\r\n  };\r\n\r\n  // Salvar permissões\r\n  const handleSave = async () => {\r\n    setIsSaving(true);\r\n    setError(\"\");\r\n\r\n    try {\r\n      await userService.updatePermissions(user.id, selectedPermissions);\r\n\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error(\"Erro ao atualizar permissões:\", error);\r\n      setError(error.response?.data?.message || \"Erro ao atualizar permissões\");\r\n    } finally {\r\n      setIsSaving(false);\r\n    }\r\n  };\r\n\r\n  // Obter o ícone do módulo\r\n  const getModuleIcon = (moduleId) => {\r\n    const icons = {\r\n      ADMIN: <Settings className=\"h-5 w-5\" />,\r\n      RH: <Users className=\"h-5 w-5\" />,\r\n      FINANCIAL: <DollarSign className=\"h-5 w-5\" />,\r\n      SCHEDULING: <Calendar className=\"h-5 w-5\" />,\r\n      BASIC: <CheckSquare className=\"h-5 w-5\" />,\r\n    };\r\n\r\n    return icons[moduleId] || <Shield className=\"h-5 w-5\" />;\r\n  };\r\n\r\n  // Renderizar as permissões de um módulo\r\n  const renderModulePermissions = (moduleId) => {\r\n    const module = PERMISSIONS_CONFIG[moduleId];\r\n    if (!module) return null;\r\n\r\n    // Se estiver filtrando, mostrar apenas as permissões que correspondem à busca\r\n    const permissions = searchTerm\r\n      ? module.permissions.filter((p) =>\r\n          filteredPermissions.some((fp) => fp.id === p.id)\r\n        )\r\n      : module.permissions;\r\n\r\n    if (permissions.length === 0) return null;\r\n\r\n    const allPermissionsSelected = permissions.every((p) =>\r\n      selectedPermissions.includes(p.id)\r\n    );\r\n\r\n    const somePermissionsSelected = permissions.some((p) =>\r\n      selectedPermissions.includes(p.id)\r\n    );\r\n\r\n    return (\r\n      <div key={moduleId} className=\"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\">\r\n        {/* Cabeçalho do módulo */}\r\n        <div\r\n          className=\"bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between cursor-pointer border-b dark:border-gray-700\"\r\n          onClick={() => toggleModuleExpansion(moduleId)}\r\n        >\r\n          <div className=\"flex items-center gap-3\">\r\n            <div\r\n              className={`p-2 rounded-full ${\r\n                somePermissionsSelected\r\n                  ? allPermissionsSelected\r\n                    ? \"bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400\"\r\n                  : \"bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\"\r\n              }`}\r\n            >\r\n              {getModuleIcon(moduleId)}\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-medium text-neutral-800 dark:text-gray-200\">{module.name}</h3>\r\n              <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                {somePermissionsSelected\r\n                  ? `${\r\n                      selectedPermissions.filter((p) =>\r\n                        module.permissions.some((mp) => mp.id === p)\r\n                      ).length\r\n                    } de ${permissions.length} permissões selecionadas`\r\n                  : \"Nenhuma permissão selecionada\"}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                toggleModulePermissions(moduleId);\r\n              }}\r\n              className={`px-3 py-1 rounded text-sm font-medium ${\r\n                allPermissionsSelected\r\n                  ? \"bg-neutral-200 text-neutral-700 hover:bg-neutral-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600\"\r\n                  : \"bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700\"\r\n              }`}\r\n            >\r\n              {allPermissionsSelected ? \"Desmarcar todas\" : \"Selecionar todas\"}\r\n            </button>\r\n\r\n            {expandedModules[moduleId] ? (\r\n              <ChevronDown className=\"text-neutral-600 dark:text-gray-400\" />\r\n            ) : (\r\n              <ChevronRight className=\"text-neutral-600 dark:text-gray-400\" />\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Lista de permissões do módulo */}\r\n        {expandedModules[moduleId] && (\r\n          <div className=\"p-4 divide-y dark:divide-gray-700 dark:bg-gray-850\">\r\n            {permissions.map((permission) => (\r\n              <div key={permission.id} className=\"py-3 first:pt-0 last:pb-0\">\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"flex-shrink-0 mt-0.5\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id={permission.id}\r\n                      checked={hasPermission(permission.id)}\r\n                      onChange={() => togglePermission(permission.id)}\r\n                      className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"flex-1\">\r\n                    <label\r\n                      htmlFor={permission.id}\r\n                      className=\"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\"\r\n                    >\r\n                      {permission.name}\r\n                    </label>\r\n                    <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-400\">\r\n                      {permission.description}\r\n                    </p>\r\n                    <div className=\"mt-1 text-xs text-neutral-500 dark:text-gray-500\">\r\n                      ID: {permission.id}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (typeof window === \"undefined\") return null;\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n      {/* Overlay de fundo escuro */}\r\n      <div className=\"fixed inset-0 bg-black/50 dark:bg-black/70\" onClick={onClose}></div>\r\n\r\n      <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-4xl w-full max-h-[90vh] flex flex-col z-[55]\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Shield className=\"h-5 w-5 text-primary-500 dark:text-primary-400\" />\r\n            <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-gray-100\">\r\n              Gerenciar Permissões\r\n            </h3>\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-neutral-500 hover:text-neutral-700 dark:text-gray-400 dark:hover:text-gray-300\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Conteúdo */}\r\n        <div className=\"overflow-y-auto p-6 dark:bg-gray-800\">\r\n          {error && (\r\n            <div className=\"mb-6 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-center gap-2 dark:bg-red-900/20 dark:border-red-800/50 dark:text-red-400\">\r\n              <AlertCircle size={18} />\r\n              <span>{error}</span>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"mb-6\">\r\n            <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\">\r\n              {user?.fullName}\r\n            </h4>\r\n            <p className=\"text-sm text-neutral-600 dark:text-gray-400 mb-4\">\r\n              Configure as permissões específicas que este usuário terá acesso\r\n              dentro de cada módulo:\r\n            </p>\r\n\r\n            <div className=\"bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50\">\r\n              <div className=\"flex-shrink-0 mt-1\">\r\n                <Info className=\"h-5 w-5 text-amber-500 dark:text-amber-400\" />\r\n              </div>\r\n              <div>\r\n                <h5 className=\"font-medium text-amber-800 dark:text-amber-300\">Importante</h5>\r\n                <p className=\"text-sm text-amber-700 dark:text-amber-400\">\r\n                  As permissões só serão aplicadas se o usuário também tiver\r\n                  acesso ao módulo correspondente. Certifique-se de que o\r\n                  usuário tenha os módulos necessários atribuídos.\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Barra de pesquisa */}\r\n            <div className=\"relative mb-6\">\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <Search className=\"h-5 w-5 text-neutral-400 dark:text-gray-500\" />\r\n              </div>\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Buscar permissões...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 dark:placeholder-gray-400\"\r\n              />\r\n              {searchTerm && (\r\n                <button\r\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                  onClick={() => setSearchTerm(\"\")}\r\n                >\r\n                  <X className=\"h-5 w-5 text-neutral-400 hover:text-neutral-600 dark:text-gray-500 dark:hover:text-gray-400\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {isLoading ? (\r\n            <div className=\"flex justify-center items-center py-12\">\r\n              <Loader2 className=\"h-8 w-8 animate-spin text-primary-500 dark:text-primary-400\" />\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-6\">\r\n              {/* Seção Módulos Atribuídos */}\r\n              <div className=\"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700\">\r\n                <h4 className=\"font-medium text-neutral-700 dark:text-gray-300 mb-2\">\r\n                  Módulos Atribuídos\r\n                </h4>\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {user?.modules?.map((moduleId) => (\r\n                    <div\r\n                      key={moduleId}\r\n                      className=\"px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2\"\r\n                    >\r\n                      {getModuleIcon(moduleId)}\r\n                      <span className=\"text-sm dark:text-gray-300\">\r\n                        {PERMISSIONS_CONFIG[moduleId]?.name || moduleId}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                  {(!user?.modules || user.modules.length === 0) && (\r\n                    <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                      Nenhum módulo atribuído\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Lista de permissões por módulo */}\r\n              {searchTerm ? (\r\n                // Se estiver pesquisando, mostra os módulos que têm permissões correspondentes\r\n                Object.keys(PERMISSIONS_CONFIG)\r\n                  .filter((moduleId) =>\r\n                    PERMISSIONS_CONFIG[moduleId].permissions.some((p) =>\r\n                      filteredPermissions.some((fp) => fp.id === p.id)\r\n                    )\r\n                  )\r\n                  .map((moduleId) => renderModulePermissions(moduleId))\r\n              ) : (\r\n                // Caso contrário, mostra primeiro os módulos que o usuário tem acesso\r\n                <>\r\n                  {user?.modules?.map((moduleId) =>\r\n                    renderModulePermissions(moduleId)\r\n                  )}\r\n                  {Object.keys(PERMISSIONS_CONFIG)\r\n                    .filter((moduleId) => !user?.modules?.includes(moduleId))\r\n                    .map((moduleId) => renderModulePermissions(moduleId))}\r\n                </>\r\n              )}\r\n\r\n              {/* Mensagem quando não há resultados na busca */}\r\n              {searchTerm && filteredPermissions.length === 0 && (\r\n                <div className=\"text-center py-8\">\r\n                  <p className=\"text-neutral-500 dark:text-gray-400\">\r\n                    Nenhuma permissão encontrada para \"{searchTerm}\"\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800\">\r\n          <div className=\"text-sm text-neutral-600 dark:text-gray-400\">\r\n            <span className=\"font-medium\">{selectedPermissions.length}</span>{\" \"}\r\n            permissões selecionadas\r\n          </div>\r\n\r\n          <div className=\"flex gap-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\"\r\n              disabled={isSaving}\r\n            >\r\n              Cancelar\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleSave}\r\n              className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\"\r\n              disabled={isSaving}\r\n            >\r\n              {isSaving ? (\r\n                <>\r\n                  <Loader2 size={16} className=\"animate-spin\" />\r\n                  <span>Salvando...</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Shield size={16} />\r\n                  <span>Salvar Permissões</span>\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PermissionsModal;"], "names": [], "mappings": ";;;;AAEA;AAgBA;AACA;AACA;AACA;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;AA0BA,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;IAC5D,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEjE,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,QAAQ;YAClB,aAAa;YAEb,sEAAsE;YACtE,IAAI,KAAK,WAAW,EAAE;gBACpB,uBAAuB,KAAK,WAAW;YACzC,OAAO;gBACL,yDAAyD;gBACzD,uBAAuB,EAAE;YAC3B;YAEA,sEAAsE;YACtE,MAAM,uBAAuB,CAAC;YAC9B,KAAK,OAAO,EAAE,QAAQ,CAAC;gBACrB,oBAAoB,CAAC,SAAS,GAAG;YACnC;YACA,mBAAmB;YAEnB,aAAa;QACf;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,uBAAuB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;YACvC;QACF;QAEA,MAAM,cAAc,WAAW,WAAW;QAC1C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,IAAI,MAAM,CACzC,CAAC,aACC,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACvC,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC9C,WAAW,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,gIAAA,CAAA,qBAAkB,CAAC,WAAW,QAAQ,CAAC,CAAC,IAAI,CACzC,WAAW,GACX,QAAQ,CAAC;QAGhB,uBAAuB;QAEvB,iFAAiF;QACjF,MAAM,kBAAkB,CAAC;QACzB,SAAS,OAAO,CAAC,CAAC;YAChB,eAAe,CAAC,WAAW,QAAQ,CAAC,GAAG;QACzC;QAEA,mBAAmB,CAAC,OAAS,CAAC;gBAC5B,GAAG,IAAI;gBACP,GAAG,eAAe;YACpB,CAAC;IACH,GAAG;QAAC;KAAW;IAEf,MAAM,UAAU,aAAa,SAAS,SAAS;IAE/C,sDAAsD;IACtD,MAAM,gBAAgB,CAAC;QACrB,OAAO,oBAAoB,QAAQ,CAAC;IACtC;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,CAAC;QACxB,uBAAuB,CAAC;YACtB,IAAI,KAAK,QAAQ,CAAC,eAAe;gBAC/B,OAAO,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO;YACpC,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAa;YAChC;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,0BAA0B,CAAC;QAC/B,MAAM,oBAAoB,gIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CACpE,CAAC,IAAM,EAAE,EAAE;QAGb,mEAAmE;QACnE,MAAM,cAAc,kBAAkB,KAAK,CAAC,CAAC,IAC3C,oBAAoB,QAAQ,CAAC;QAG/B,IAAI,aAAa;YACf,wCAAwC;YACxC,uBAAuB,CAAC,OACtB,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,kBAAkB,QAAQ,CAAC;QAEnD,OAAO;YACL,0CAA0C;YAC1C,uBAAuB,CAAC;gBACtB,MAAM,iBAAiB;uBAAI;iBAAK;gBAChC,kBAAkB,OAAO,CAAC,CAAC;oBACzB,IAAI,CAAC,eAAe,QAAQ,CAAC,IAAI;wBAC/B,eAAe,IAAI,CAAC;oBACtB;gBACF;gBACA,OAAO;YACT;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAC,OAAS,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAC7B,CAAC;IACH;IAEA,oBAAoB;IACpB,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;QAET,IAAI;YACF,MAAM,yJAAA,CAAA,cAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE;YAE7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC5C,SAAU;YACR,YAAY;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;YACZ,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC3B,kBAAI,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACrB,yBAAW,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YACjC,0BAAY,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAChC,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,OAAO,KAAK,CAAC,SAAS,kBAAI,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC9C;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,SAAS,gIAAA,CAAA,qBAAkB,CAAC,SAAS;QAC3C,IAAI,CAAC,QAAQ,OAAO;QAEpB,8EAA8E;QAC9E,MAAM,cAAc,aAChB,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,IACzB,oBAAoB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,EAAE,EAAE,KAEjD,OAAO,WAAW;QAEtB,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;QAErC,MAAM,yBAAyB,YAAY,KAAK,CAAC,CAAC,IAChD,oBAAoB,QAAQ,CAAC,EAAE,EAAE;QAGnC,MAAM,0BAA0B,YAAY,IAAI,CAAC,CAAC,IAChD,oBAAoB,QAAQ,CAAC,EAAE,EAAE;QAGnC,qBACE,8OAAC;YAAmB,WAAU;;8BAE5B,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,sBAAsB;;sCAErC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,iBAAiB,EAC3B,0BACI,yBACE,yEACA,yEACF,uEACJ;8CAED,cAAc;;;;;;8CAEjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmD,OAAO,IAAI;;;;;;sDAC5E,8OAAC;4CAAE,WAAU;sDACV,0BACG,GACE,oBAAoB,MAAM,CAAC,CAAC,IAC1B,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,IAC1C,MAAM,CACT,IAAI,EAAE,YAAY,MAAM,CAAC,wBAAwB,CAAC,GACnD;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,wBAAwB;oCAC1B;oCACA,WAAW,CAAC,sCAAsC,EAChD,yBACI,oHACA,gGACJ;8CAED,yBAAyB,oBAAoB;;;;;;gCAG/C,eAAe,CAAC,SAAS,iBACxB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;gBAM7B,eAAe,CAAC,SAAS,kBACxB,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;4BAAwB,WAAU;sCACjC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,IAAI,WAAW,EAAE;4CACjB,SAAS,cAAc,WAAW,EAAE;4CACpC,UAAU,IAAM,iBAAiB,WAAW,EAAE;4CAC9C,WAAU;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,WAAW,EAAE;gDACtB,WAAU;0DAET,WAAW,IAAI;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DACV,WAAW,WAAW;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;;oDAAmD;oDAC3D,WAAW,EAAE;;;;;;;;;;;;;;;;;;;2BAvBhB,WAAW,EAAE;;;;;;;;;;;WA5DrB;;;;;IA6Fd;IAEA,wCAAmC,OAAO;;AAwL5C;uCAEe"}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/forms/UserProfileImageUpload.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { Upload, Trash, Loader2 } from 'lucide-react';\r\nimport { userService } from '@/app/modules/admin/services/userService';\r\n\r\nconst UserProfileImageUpload = ({\r\n  userId,\r\n  initialImageUrl,\r\n  onImageUploaded,\r\n  deferUpload = false,\r\n  uploadRef = null,\r\n  size = 'medium',\r\n  disabled = false\r\n}) => {\r\n  const [imageUrl, setImageUrl] = useState(initialImageUrl || null);\r\n  const [previewUrl, setPreviewUrl] = useState(initialImageUrl || null);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Tamanhos disponíveis\r\n  const sizes = {\r\n    small: { container: 'h-16 w-16', icon: 'h-4 w-4' },\r\n    medium: { container: 'h-24 w-24', icon: 'h-6 w-6' },\r\n    large: { container: 'h-32 w-32', icon: 'h-8 w-8' }\r\n  };\r\n\r\n  // Atualizar a URL da imagem quando a prop initialImageUrl mudar\r\n  useEffect(() => {\r\n    if (initialImageUrl) {\r\n      setImageUrl(initialImageUrl);\r\n      setPreviewUrl(initialImageUrl);\r\n    }\r\n  }, [initialImageUrl]);\r\n\r\n  // Expor o método de upload para o componente pai\r\n  useEffect(() => {\r\n    if (uploadRef) {\r\n      uploadRef.current = {\r\n        uploadSelectedImage: async () => {\r\n          console.log('Método uploadSelectedImage chamado');\r\n          console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');\r\n          console.log('ID do usuário:', userId);\r\n\r\n          if (selectedFile && userId) {\r\n            console.log('Iniciando upload do arquivo selecionado');\r\n            const result = await uploadImage(selectedFile);\r\n            console.log('Resultado do upload:', result);\r\n            return result;\r\n          }\r\n          console.log('Nenhum arquivo para upload ou ID do usuário ausente');\r\n          return null;\r\n        },\r\n        hasSelectedFile: () => {\r\n          const hasFile = !!selectedFile;\r\n          console.log('Verificando se há arquivo selecionado:', hasFile);\r\n          return hasFile;\r\n        }\r\n      };\r\n    }\r\n  }, [selectedFile, userId, uploadRef]);\r\n\r\n  const handleFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (!file) return;\r\n\r\n    // Verificar tipo de arquivo (apenas imagens)\r\n    if (!file.type.startsWith('image/')) {\r\n      setError('Por favor, selecione uma imagem válida');\r\n      return;\r\n    }\r\n\r\n    // Limitar tamanho (2MB)\r\n    if (file.size > 2 * 1024 * 1024) {\r\n      setError('A imagem deve ter no máximo 2MB');\r\n      return;\r\n    }\r\n\r\n    setError(null);\r\n    setSelectedFile(file);\r\n    console.log('Arquivo selecionado:', file.name, file.type, file.size);\r\n\r\n    // Criar uma URL para pré-visualização\r\n    const newPreviewUrl = URL.createObjectURL(file);\r\n    setPreviewUrl(newPreviewUrl);\r\n\r\n    // Se não estiver adiando o upload, fazer o upload imediatamente\r\n    if (!deferUpload && userId) {\r\n      uploadImage(file);\r\n    } else {\r\n      // Notificar o componente pai sobre a mudança de arquivo\r\n      if (onImageUploaded) {\r\n        onImageUploaded(null, file);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Método para fazer o upload da imagem\r\n  const uploadImage = async (file) => {\r\n    if (!file || !userId) {\r\n      console.error('Upload cancelado: arquivo ou userId ausente', { file: !!file, userId });\r\n      return null;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    console.log('Iniciando upload de imagem para usuário ID:', userId);\r\n    console.log('Arquivo a ser enviado:', file.name, file.type, file.size);\r\n\r\n    try {\r\n      console.log('Chamando serviço de upload de imagem');\r\n      const response = await userService.uploadProfileImage(userId, file);\r\n      console.log('Upload de imagem concluído com sucesso');\r\n      console.log('Resposta completa:', JSON.stringify(response));\r\n\r\n      // Atualizar URL da imagem com timestamp para evitar cache\r\n      const timestamp = new Date().getTime();\r\n\r\n      // Usar a URL completa retornada pelo servidor\r\n      const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;\r\n      console.log('Nova URL da imagem:', newImageUrl);\r\n\r\n      setImageUrl(newImageUrl);\r\n      setSelectedFile(null); // Limpar o arquivo selecionado após o upload\r\n\r\n      if (onImageUploaded) {\r\n        onImageUploaded(newImageUrl);\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Erro ao fazer upload da imagem:', error);\r\n      setError(error.response?.data?.message || 'Erro ao fazer upload da imagem');\r\n      return null;\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const removeImage = () => {\r\n    setImageUrl(null);\r\n    setPreviewUrl(null);\r\n    setSelectedFile(null);\r\n    \r\n    if (onImageUploaded) {\r\n      onImageUploaded(null);\r\n    }\r\n  };\r\n\r\n  const triggerFileInput = () => {\r\n    if (!disabled && fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"relative group\">\r\n        {previewUrl ? (\r\n          <div className={`relative ${sizes[size].container} overflow-hidden rounded-full border border-neutral-300 dark:border-gray-600`}>\r\n            <img\r\n              src={previewUrl}\r\n              alt=\"Imagem de perfil\"\r\n              className=\"h-full w-full object-cover\"\r\n            />\r\n            {!disabled && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={removeImage}\r\n                className=\"absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                title=\"Remover imagem\"\r\n              >\r\n                <Trash className=\"h-3 w-3\" />\r\n              </button>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <div\r\n            onClick={triggerFileInput}\r\n            className={`${sizes[size].container} flex items-center justify-center rounded-full border-2 border-dashed border-neutral-300 dark:border-gray-600 ${!disabled ? 'cursor-pointer hover:border-primary-500 dark:hover:border-primary-400' : 'cursor-not-allowed opacity-70'}`}\r\n          >\r\n            {isUploading ? (\r\n              <Loader2 className={`${sizes[size].icon} text-neutral-400 dark:text-gray-500 animate-spin`} />\r\n            ) : (\r\n              <Upload className={`${sizes[size].icon} text-neutral-400 dark:text-gray-500`} />\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {!disabled && (\r\n        <div className=\"mt-2 flex flex-col items-center\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={triggerFileInput}\r\n            disabled={isUploading}\r\n            className=\"text-xs text-primary-600 dark:text-primary-400 hover:underline focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {previewUrl ? 'Alterar foto' : 'Adicionar foto'}\r\n          </button>\r\n          <input\r\n            ref={fileInputRef}\r\n            type=\"file\"\r\n            accept=\"image/*\"\r\n            onChange={handleFileChange}\r\n            className=\"hidden\"\r\n            disabled={isUploading || disabled}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {error && (\r\n        <p className=\"mt-1 text-xs text-red-600 dark:text-red-400\">{error}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserProfileImageUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAAA;AAAA;AAHA;;;;;AAMA,MAAM,yBAAyB,CAAC,EAC9B,MAAM,EACN,eAAe,EACf,eAAe,EACf,cAAc,KAAK,EACnB,YAAY,IAAI,EAChB,OAAO,QAAQ,EACf,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,uBAAuB;IACvB,MAAM,QAAQ;QACZ,OAAO;YAAE,WAAW;YAAa,MAAM;QAAU;QACjD,QAAQ;YAAE,WAAW;YAAa,MAAM;QAAU;QAClD,OAAO;YAAE,WAAW;YAAa,MAAM;QAAU;IACnD;IAEA,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,YAAY;YACZ,cAAc;QAChB;IACF,GAAG;QAAC;KAAgB;IAEpB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,UAAU,OAAO,GAAG;gBAClB,qBAAqB;oBACnB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,wBAAwB,eAAe,aAAa,IAAI,GAAG;oBACvE,QAAQ,GAAG,CAAC,kBAAkB;oBAE9B,IAAI,gBAAgB,QAAQ;wBAC1B,QAAQ,GAAG,CAAC;wBACZ,MAAM,SAAS,MAAM,YAAY;wBACjC,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;gBACA,iBAAiB;oBACf,MAAM,UAAU,CAAC,CAAC;oBAClB,QAAQ,GAAG,CAAC,0CAA0C;oBACtD,OAAO;gBACT;YACF;QACF;IACF,GAAG;QAAC;QAAc;QAAQ;KAAU;IAEpC,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;QAEX,6CAA6C;QAC7C,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,SAAS;YACT;QACF;QAEA,wBAAwB;QACxB,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,SAAS;YACT;QACF;QAEA,SAAS;QACT,gBAAgB;QAChB,QAAQ,GAAG,CAAC,wBAAwB,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAEnE,sCAAsC;QACtC,MAAM,gBAAgB,IAAI,eAAe,CAAC;QAC1C,cAAc;QAEd,gEAAgE;QAChE,IAAI,CAAC,eAAe,QAAQ;YAC1B,YAAY;QACd,OAAO;YACL,wDAAwD;YACxD,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;QACF;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACpB,QAAQ,KAAK,CAAC,+CAA+C;gBAAE,MAAM,CAAC,CAAC;gBAAM;YAAO;YACpF,OAAO;QACT;QAEA,eAAe;QACf,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,QAAQ,GAAG,CAAC,0BAA0B,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAErE,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,yJAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC,QAAQ;YAC9D,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAEjD,0DAA0D;YAC1D,MAAM,YAAY,IAAI,OAAO,OAAO;YAEpC,8CAA8C;YAC9C,MAAM,cAAc,SAAS,YAAY,GAAG,GAAG,SAAS,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG;YACxF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,YAAY;YACZ,gBAAgB,OAAO,6CAA6C;YAEpE,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;YAC1C,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ,cAAc;QACd,gBAAgB;QAEhB,IAAI,iBAAiB;YACnB,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,aAAa,OAAO,EAAE;YACrC,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,2BACC,8OAAC;oBAAI,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,4EAA4E,CAAC;;sCAC7H,8OAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;wBAEX,CAAC,0BACA,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;yCAKvB,8OAAC;oBACC,SAAS;oBACT,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,8GAA8G,EAAE,CAAC,WAAW,0EAA0E,iCAAiC;8BAE1Q,4BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,iDAAiD,CAAC;;;;;6CAE1F,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC;;;;;;;;;;;;;;;;YAMnF,CAAC,0BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,aAAa,iBAAiB;;;;;;kCAEjC,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAO;wBACP,UAAU;wBACV,WAAU;wBACV,UAAU,eAAe;;;;;;;;;;;;YAK9B,uBACC,8OAAC;gBAAE,WAAU;0BAA+C;;;;;;;;;;;;AAIpE;uCAEe"}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/common/MaskedInput.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\n/**\n * Componente genérico para campos de entrada com máscara\n * \n * @param {Object} props - Propriedades do componente\n * @param {string} props.type - Tipo de máscara: 'cpf', 'cnpj', 'phone', 'cep', ou 'custom'\n * @param {string} props.value - Valor atual do campo\n * @param {Function} props.onChange - Função chamada quando o valor muda\n * @param {string} props.name - Nome do campo\n * @param {string} props.id - ID do campo\n * @param {string} props.placeholder - Texto de placeholder\n * @param {string} props.className - Classes CSS adicionais\n * @param {boolean} props.disabled - Se o campo está desabilitado\n * @param {string} props.customMask - Máscara personalizada (quando type='custom')\n * @param {Object} props.inputProps - Propriedades adicionais para o input\n */\nconst MaskedInput = ({\n  type = 'text',\n  value = '',\n  onChange,\n  name,\n  id,\n  placeholder,\n  className = '',\n  disabled = false,\n  customMask,\n  ...inputProps\n}) => {\n  // Estado interno para controlar o valor formatado\n  const [inputValue, setInputValue] = useState('');\n\n  // Atualiza o estado interno quando o valor externo muda\n  useEffect(() => {\n    if (value !== undefined) {\n      setInputValue(formatValue(value, type, customMask));\n    }\n  }, [value, type, customMask]);\n\n  // Função para aplicar a máscara ao valor\n  const applyMask = (value, mask) => {\n    let maskedValue = '';\n    let valueIndex = 0;\n\n    for (let i = 0; i < mask.length && valueIndex < value.length; i++) {\n      const maskChar = mask[i];\n      const valueChar = value[valueIndex];\n\n      if (maskChar === '#') {\n        // Apenas dígitos\n        if (/\\d/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else if (maskChar === 'A') {\n        // Apenas letras\n        if (/[a-zA-Z]/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else if (maskChar === 'S') {\n        // Letras ou dígitos\n        if (/[a-zA-Z0-9]/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else {\n        // Caracteres especiais da máscara\n        maskedValue += maskChar;\n        \n        // Se o caractere do valor for igual ao caractere da máscara, avança\n        if (valueChar === maskChar) {\n          valueIndex++;\n        }\n      }\n    }\n\n    return maskedValue;\n  };\n\n  // Função para obter a máscara com base no tipo\n  const getMask = (type) => {\n    switch (type) {\n      case 'cpf':\n        return '###.###.###-##';\n      case 'cnpj':\n        return '##.###.###/####-##';\n      case 'phone':\n        return '(##) #####-####';\n      case 'cep':\n        return '#####-###';\n      case 'custom':\n        return customMask || '';\n      default:\n        return '';\n    }\n  };\n\n  // Função para formatar o valor com base no tipo\n  const formatValue = (value, type, customMask) => {\n    if (!value) return '';\n    \n    // Remove caracteres não numéricos para tipos numéricos\n    let cleanValue = value;\n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\n      cleanValue = value.replace(/\\D/g, '');\n    }\n    \n    const mask = getMask(type);\n    return applyMask(cleanValue, mask);\n  };\n\n  // Função para remover a máscara e obter apenas os dígitos\n  const unformatValue = (value) => {\n    if (!value) return '';\n    \n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\n      return value.replace(/\\D/g, '');\n    }\n    \n    return value;\n  };\n\n  // Manipulador de mudança de valor\n  const handleChange = (e) => {\n    const newValue = e.target.value;\n    const formattedValue = formatValue(newValue, type, customMask);\n    \n    setInputValue(formattedValue);\n    \n    if (onChange) {\n      // Cria um evento sintético com o valor formatado\n      const syntheticEvent = {\n        ...e,\n        target: {\n          ...e.target,\n          name: name,\n          value: formattedValue,\n          rawValue: unformatValue(formattedValue)\n        }\n      };\n      \n      onChange(syntheticEvent);\n    }\n  };\n\n  return (\n    <input\n      type=\"text\"\n      id={id}\n      name={name}\n      value={inputValue}\n      onChange={handleChange}\n      placeholder={placeholder}\n      className={className}\n      disabled={disabled}\n      {...inputProps}\n    />\n  );\n};\n\nexport default MaskedInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA;;;;;;;;;;;;;;CAcC,GACD,MAAM,cAAc,CAAC,EACnB,OAAO,MAAM,EACb,QAAQ,EAAE,EACV,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,WAAW,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,EACV,GAAG,YACJ;IACC,kDAAkD;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,cAAc,YAAY,OAAO,MAAM;QACzC;IACF,GAAG;QAAC;QAAO;QAAM;KAAW;IAE5B,yCAAyC;IACzC,MAAM,YAAY,CAAC,OAAO;QACxB,IAAI,cAAc;QAClB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,aAAa,MAAM,MAAM,EAAE,IAAK;YACjE,MAAM,WAAW,IAAI,CAAC,EAAE;YACxB,MAAM,YAAY,KAAK,CAAC,WAAW;YAEnC,IAAI,aAAa,KAAK;gBACpB,iBAAiB;gBACjB,IAAI,KAAK,IAAI,CAAC,YAAY;oBACxB,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,gBAAgB;gBAChB,IAAI,WAAW,IAAI,CAAC,YAAY;oBAC9B,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,oBAAoB;gBACpB,IAAI,cAAc,IAAI,CAAC,YAAY;oBACjC,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO;gBACL,kCAAkC;gBAClC,eAAe;gBAEf,oEAAoE;gBACpE,IAAI,cAAc,UAAU;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,cAAc;YACvB;gBACE,OAAO;QACX;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc,CAAC,OAAO,MAAM;QAChC,IAAI,CAAC,OAAO,OAAO;QAEnB,uDAAuD;QACvD,IAAI,aAAa;QACjB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,aAAa,MAAM,OAAO,CAAC,OAAO;QACpC;QAEA,MAAM,OAAO,QAAQ;QACrB,OAAO,UAAU,YAAY;IAC/B;IAEA,0DAA0D;IAC1D,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,OAAO,MAAM,OAAO,CAAC,OAAO;QAC9B;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,MAAM,iBAAiB,YAAY,UAAU,MAAM;QAEnD,cAAc;QAEd,IAAI,UAAU;YACZ,iDAAiD;YACjD,MAAM,iBAAiB;gBACrB,GAAG,CAAC;gBACJ,QAAQ;oBACN,GAAG,EAAE,MAAM;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU,cAAc;gBAC1B;YACF;YAEA,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACT,GAAG,UAAU;;;;;;AAGpB;uCAEe"}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/common/AddressForm.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { MapPin, Search, Loader2 } from 'lucide-react';\r\nimport { useCep } from '@/hooks/useCep';\r\nimport MaskedInput from './MaskedInput';\r\nimport { ModuleInput, ModuleMaskedInput } from '@/components/ui';\r\n\r\n/**\r\n * Componente reutilizável para formulários de endereço com busca automática por CEP\r\n *\r\n * @param {Object} props - Propriedades do componente\r\n * @param {Object} props.formData - Dados do formulário\r\n * @param {Function} props.setFormData - Função para atualizar os dados do formulário\r\n * @param {Object} props.errors - Erros de validação\r\n * @param {boolean} props.isLoading - Indica se o formulário está em carregamento\r\n * @param {Object} props.fieldMapping - Mapeamento dos campos do endereço para os campos do formulário\r\n * @param {string} props.prefix - Prefixo para campos aninhados (ex: \"person.\")\r\n * @param {Object} props.classes - Classes CSS personalizadas\r\n * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)\r\n */\r\nconst AddressForm = ({\r\n  formData,\r\n  setFormData,\r\n  errors = {},\r\n  isLoading = false,\r\n  fieldMapping = {},\r\n  prefix = '',\r\n  classes = {},\r\n  moduleColor = 'default'\r\n}) => {\r\n  // Hook para busca de CEP\r\n  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();\r\n\r\n  // Classes CSS padrão\r\n  const defaultClasses = {\r\n    label: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',\r\n    input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white',\r\n    error: 'text-sm text-red-600 dark:text-red-400 mt-1',\r\n    iconContainer: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none',\r\n    button: 'absolute inset-y-0 right-0 px-3 flex items-center bg-primary-500 hover:bg-primary-600 text-white rounded-r-md transition-colors'\r\n  };\r\n\r\n  // Mescla as classes padrão com as classes personalizadas\r\n  const mergedClasses = {\r\n    label: classes.label || defaultClasses.label,\r\n    input: classes.input || defaultClasses.input,\r\n    error: classes.error || defaultClasses.error,\r\n    iconContainer: classes.iconContainer || defaultClasses.iconContainer,\r\n    button: classes.button || defaultClasses.button\r\n  };\r\n\r\n  // Mapeamento padrão dos campos\r\n  const defaultMapping = {\r\n    cep: `${prefix}postalCode`,\r\n    logradouro: `${prefix}address`,\r\n    bairro: `${prefix}neighborhood`,\r\n    localidade: `${prefix}city`,\r\n    uf: `${prefix}state`\r\n  };\r\n\r\n  // Log para debug\r\n  console.log('Prefix:', prefix);\r\n  console.log('Default mapping:', defaultMapping);\r\n\r\n  // Mescla o mapeamento padrão com o mapeamento personalizado\r\n  const mergedMapping = { ...defaultMapping, ...fieldMapping };\r\n  console.log('Mapeamento mesclado:', mergedMapping);\r\n\r\n  // Função para lidar com a mudança de valores nos campos\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log(`Alterando campo: ${name} para valor: ${value}`);\r\n\r\n    // Suporte para campos aninhados (ex: \"person.address\")\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}. Estado atual:`, formData[parent]);\r\n\r\n      const newFormData = {\r\n        ...formData,\r\n        [parent]: {\r\n          ...formData[parent],\r\n          [child]: value\r\n        }\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    } else {\r\n      const newFormData = {\r\n        ...formData,\r\n        [name]: value\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    }\r\n  };\r\n\r\n  // Função para buscar endereço pelo CEP\r\n  const handleCepSearch = async () => {\r\n    // Obtém o valor do CEP do campo correspondente\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    console.log('Buscando endereço para o CEP:', cepValue);\r\n\r\n    if (cepValue) {\r\n      try {\r\n        const result = await searchAddressByCep(cepValue, setFormData, mergedMapping);\r\n        console.log('Resultado da busca de CEP:', result);\r\n\r\n        // Forçar a atualização do formulário com os dados recebidos\r\n        if (result) {\r\n          console.log('Dados de endereço recebidos com sucesso, atualizando formulário');\r\n\r\n          // Criar um objeto com os campos mapeados\r\n          const updatedFields = {};\r\n\r\n          // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário\r\n          Object.keys(result).forEach(apiField => {\r\n            const formField = mergedMapping[apiField];\r\n            if (formField && result[apiField]) {\r\n              updatedFields[formField] = result[apiField];\r\n            }\r\n          });\r\n\r\n          console.log('Campos a serem atualizados:', updatedFields);\r\n\r\n          // Atualizar o formulário com os dados do endereço\r\n          setFormData(prevData => ({\r\n            ...prevData,\r\n            ...updatedFields\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro ao buscar CEP:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Efeito para buscar endereço automaticamente quando o CEP for preenchido completamente\r\n  useEffect(() => {\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    // Formatar o CEP se necessário\r\n    if (cepValue && !cepValue.includes('-') && cepValue.replace(/\\D/g, '').length === 8) {\r\n      // Formatar o CEP no formato 00000-000\r\n      const cleanCep = cepValue.replace(/\\D/g, '');\r\n      const formattedCep = cleanCep.replace(/(\\d{5})(\\d{3})/, '$1-$2');\r\n\r\n      // Atualizar o formData com o CEP formatado\r\n      if (cepField.includes('.')) {\r\n        const [parent, child] = cepField.split('.');\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [parent]: {\r\n            ...prev[parent],\r\n            [child]: formattedCep\r\n          }\r\n        }));\r\n      } else {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [cepField]: formattedCep\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Se o CEP tiver 8 dígitos (sem contar a máscara), busca o endereço\r\n    if (cepValue && cepValue.replace(/\\D/g, '').length === 8) {\r\n      handleCepSearch();\r\n    }\r\n  }, [formData[mergedMapping.cep]]);\r\n\r\n  // Função para obter o valor de um campo, considerando campos aninhados\r\n  const getFieldValue = (fieldName) => {\r\n    console.log(`Obtendo valor para o campo: ${fieldName}`);\r\n\r\n    if (fieldName.includes('.')) {\r\n      const [parent, child] = fieldName.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}, valor:`, formData[parent]?.[child]);\r\n      return formData[parent]?.[child] || '';\r\n    }\r\n\r\n    console.log(`Campo simples: ${fieldName}, valor:`, formData[fieldName]);\r\n    return formData[fieldName] || '';\r\n  };\r\n\r\n  // Função para verificar se um campo tem erro\r\n  const hasError = (fieldName) => {\r\n    return errors[fieldName] ? true : false;\r\n  };\r\n\r\n  // Função para obter a mensagem de erro de um campo\r\n  const getErrorMessage = (fieldName) => {\r\n    return errors[fieldName] || '';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Seção de Endereço */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        {/* CEP com botão de busca */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.cep}>\r\n            CEP\r\n          </label>\r\n          <div className=\"relative\">\r\n            <div className={mergedClasses.iconContainer}>\r\n              <MapPin className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n            </div>\r\n            {moduleColor ? (\r\n              <div className=\"relative\">\r\n                <ModuleMaskedInput\r\n                  moduleColor={moduleColor}\r\n                  mask=\"99999-999\"\r\n                  replacement={{ 9: /[0-9]/ }}\r\n                  id={mergedMapping.cep}\r\n                  name={mergedMapping.cep}\r\n                  value={getFieldValue(mergedMapping.cep)}\r\n                  onChange={handleChange}\r\n                  placeholder=\"00000-000\"\r\n                  className=\"pl-10 pr-12\"\r\n                  disabled={isLoading || isCepLoading}\r\n                  error={hasError(mergedMapping.cep)}\r\n                />\r\n              </div>\r\n            ) : (\r\n              <MaskedInput\r\n                type=\"cep\"\r\n                id={mergedMapping.cep}\r\n                name={mergedMapping.cep}\r\n                value={getFieldValue(mergedMapping.cep)}\r\n                onChange={handleChange}\r\n                placeholder=\"00000-000\"\r\n                className={`${mergedClasses.input} pl-10 pr-12 ${hasError(mergedMapping.cep) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n                disabled={isLoading || isCepLoading}\r\n              />\r\n            )}\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCepSearch}\r\n              className={mergedClasses.button}\r\n              disabled={isLoading || isCepLoading}\r\n              aria-label=\"Buscar CEP\"\r\n            >\r\n              {isCepLoading ? (\r\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n              ) : (\r\n                <Search className=\"h-5 w-5\" />\r\n              )}\r\n            </button>\r\n          </div>\r\n          {hasError(mergedMapping.cep) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.cep)}</p>\r\n          )}\r\n          {cepError && (\r\n            <p className={mergedClasses.error}>{cepError}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Estado */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.uf}>\r\n            Estado\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n              error={hasError(mergedMapping.uf)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.uf) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.uf) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.uf)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Cidade */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.localidade}>\r\n            Cidade\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.localidade)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.localidade) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.localidade) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.localidade)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Bairro */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.bairro}>\r\n            Bairro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.bairro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.bairro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.bairro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.bairro)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Endereço (logradouro) */}\r\n        <div className=\"md:col-span-2\">\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.logradouro}>\r\n            Logradouro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.logradouro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.logradouro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.logradouro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.logradouro)}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddressForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;AAGA;AAHA;AAAA;AAGA;AANA;;;;;;;AAQA;;;;;;;;;;;;CAYC,GACD,MAAM,cAAc,CAAC,EACnB,QAAQ,EACR,WAAW,EACX,SAAS,CAAC,CAAC,EACX,YAAY,KAAK,EACjB,eAAe,CAAC,CAAC,EACjB,SAAS,EAAE,EACX,UAAU,CAAC,CAAC,EACZ,cAAc,SAAS,EACxB;IACC,yBAAyB;IACzB,MAAM,EAAE,kBAAkB,EAAE,WAAW,YAAY,EAAE,OAAO,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD;IAE9E,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,OAAO;QACP,OAAO;QACP,OAAO;QACP,eAAe;QACf,QAAQ;IACV;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QACpB,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,eAAe,QAAQ,aAAa,IAAI,eAAe,aAAa;QACpE,QAAQ,QAAQ,MAAM,IAAI,eAAe,MAAM;IACjD;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,KAAK,GAAG,OAAO,UAAU,CAAC;QAC1B,YAAY,GAAG,OAAO,OAAO,CAAC;QAC9B,QAAQ,GAAG,OAAO,YAAY,CAAC;QAC/B,YAAY,GAAG,OAAO,IAAI,CAAC;QAC3B,IAAI,GAAG,OAAO,KAAK,CAAC;IACtB;IAEA,iBAAiB;IACjB,QAAQ,GAAG,CAAC,WAAW;IACvB,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,4DAA4D;IAC5D,MAAM,gBAAgB;QAAE,GAAG,cAAc;QAAE,GAAG,YAAY;IAAC;IAC3D,QAAQ,GAAG,CAAC,wBAAwB;IAEpC,wDAAwD;IACxD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,aAAa,EAAE,OAAO;QAE3D,uDAAuD;QACvD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC;YACnC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,eAAe,CAAC,EAAE,QAAQ,CAAC,OAAO;YAEjF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,OAAO,EAAE;oBACR,GAAG,QAAQ,CAAC,OAAO;oBACnB,CAAC,MAAM,EAAE;gBACX;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd,OAAO;YACL,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB;QACtB,+CAA+C;QAC/C,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,IAAI,UAAU;YACZ,IAAI;gBACF,MAAM,SAAS,MAAM,mBAAmB,UAAU,aAAa;gBAC/D,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,4DAA4D;gBAC5D,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC;oBAEZ,yCAAyC;oBACzC,MAAM,gBAAgB,CAAC;oBAEvB,uFAAuF;oBACvF,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;wBAC1B,MAAM,YAAY,aAAa,CAAC,SAAS;wBACzC,IAAI,aAAa,MAAM,CAAC,SAAS,EAAE;4BACjC,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS;wBAC7C;oBACF;oBAEA,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,kDAAkD;oBAClD,YAAY,CAAA,WAAY,CAAC;4BACvB,GAAG,QAAQ;4BACX,GAAG,aAAa;wBAClB,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACvC;QACF;IACF;IAEA,wFAAwF;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,+BAA+B;QAC/B,IAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,QAAQ,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACnF,sCAAsC;YACtC,MAAM,WAAW,SAAS,OAAO,CAAC,OAAO;YACzC,MAAM,eAAe,SAAS,OAAO,CAAC,kBAAkB;YAExD,2CAA2C;YAC3C,IAAI,SAAS,QAAQ,CAAC,MAAM;gBAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;gBACvC,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,GAAG,IAAI,CAAC,OAAO;4BACf,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YACH,OAAO;gBACL,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,SAAS,EAAE;oBACd,CAAC;YACH;QACF;QAEA,oEAAoE;QACpE,IAAI,YAAY,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACxD;QACF;IACF,GAAG;QAAC,QAAQ,CAAC,cAAc,GAAG,CAAC;KAAC;IAEhC,uEAAuE;IACvE,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW;QAEtD,IAAI,UAAU,QAAQ,CAAC,MAAM;YAC3B,MAAM,CAAC,QAAQ,MAAM,GAAG,UAAU,KAAK,CAAC;YACxC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;YACnF,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,IAAI;QACtC;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,QAAQ,CAAC,EAAE,QAAQ,CAAC,UAAU;QACtE,OAAO,QAAQ,CAAC,UAAU,IAAI;IAChC;IAEA,6CAA6C;IAC7C,MAAM,WAAW,CAAC;QAChB,OAAO,MAAM,CAAC,UAAU,GAAG,OAAO;IACpC;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,CAAC,UAAU,IAAI;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,GAAG;sCAAE;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,cAAc,aAAa;8CACzC,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;gCAEnB,4BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4LAAA,CAAA,oBAAiB;wCAChB,aAAa;wCACb,MAAK;wCACL,aAAa;4CAAE,GAAG;wCAAQ;wCAC1B,IAAI,cAAc,GAAG;wCACrB,MAAM,cAAc,GAAG;wCACvB,OAAO,cAAc,cAAc,GAAG;wCACtC,UAAU;wCACV,aAAY;wCACZ,WAAU;wCACV,UAAU,aAAa;wCACvB,OAAO,SAAS,cAAc,GAAG;;;;;;;;;;yDAIrC,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,IAAI,cAAc,GAAG;oCACrB,MAAM,cAAc,GAAG;oCACvB,OAAO,cAAc,cAAc,GAAG;oCACtC,UAAU;oCACV,aAAY;oCACZ,WAAW,GAAG,cAAc,KAAK,CAAC,aAAa,EAAE,SAAS,cAAc,GAAG,IAAI,uCAAuC,IAAI;oCAC1H,UAAU,aAAa;;;;;;8CAG3B,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,cAAc,MAAM;oCAC/B,UAAU,aAAa;oCACvB,cAAW;8CAEV,6BACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIvB,SAAS,cAAc,GAAG,mBACzB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,GAAG;;;;;;wBAEtE,0BACC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG;;;;;;;;;;;;8BAKxC,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,EAAE;sCAAE;;;;;;wBAGjE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,WAAW;4BACX,OAAO,SAAS,cAAc,EAAE;;;;;iDAGlC,8OAAC;4BACC,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,EAAE,IAAI,uCAAuC,IAAI;4BAC7G,UAAU,aAAa;4BACvB,WAAW;;;;;;wBAGd,SAAS,cAAc,EAAE,mBACxB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,EAAE;;;;;;;;;;;;8BAKxE,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;8BAKhF,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,MAAM;sCAAE;;;;;;wBAGrE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,MAAM;;;;;iDAGtC,8OAAC;4BACC,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,MAAM,IAAI,uCAAuC,IAAI;4BACjH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,MAAM,mBAC5B,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,MAAM;;;;;;;;;;;;8BAK5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMxF;uCAEe"}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1903, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/common/TimeInput.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef } from 'react';\r\n\r\n/**\r\n * Componente de input de horário personalizado\r\n * @param {Object} props - Propriedades do componente\r\n * @param {string} props.value - Valor inicial do input (formato HH:MM)\r\n * @param {Function} props.onChange - Função chamada quando o valor muda\r\n * @param {string} props.className - Classes CSS adicionais\r\n * @param {string} props.placeholder - Placeholder do input\r\n * @param {boolean} props.disabled - Se o input está desabilitado\r\n * @param {string} props.id - ID do input\r\n * @param {string} props.name - Nome do input\r\n * @param {string} props.error - Mensagem de erro\r\n * @param {string} props.errorClassName - Classes CSS para o erro\r\n */\r\nconst TimeInput = ({\r\n  value: initialValue = '',\r\n  onChange,\r\n  className = '',\r\n  placeholder = '00:00',\r\n  disabled = false,\r\n  id,\r\n  name,\r\n  error,\r\n  errorClassName = 'text-xs text-red-500 mt-1',\r\n}) => {\r\n  // Referência para o input\r\n  const inputRef = useRef(null);\r\n\r\n  // Estado interno para controlar o valor durante a edição\r\n  // Usamos o valor inicial apenas na primeira renderização\r\n  const [inputValue, setInputValue] = useState(initialValue || '');\r\n\r\n  // Formata o valor para o formato HH:MM\r\n  const formatTimeValue = (val) => {\r\n    // Remove caracteres não numéricos, exceto ':'\r\n    let cleaned = val.replace(/[^\\d:]/g, '');\r\n\r\n    // Se não tiver ':', adiciona após os primeiros 2 dígitos\r\n    if (cleaned.length > 2 && !cleaned.includes(':')) {\r\n      cleaned = cleaned.substring(0, 2) + ':' + cleaned.substring(2);\r\n    }\r\n\r\n    // Limita o tamanho total a 5 caracteres (HH:MM)\r\n    if (cleaned.length > 5) {\r\n      cleaned = cleaned.substring(0, 5);\r\n    }\r\n\r\n    // Valida horas e minutos\r\n    if (cleaned.includes(':')) {\r\n      const [hours, minutes] = cleaned.split(':');\r\n\r\n      // Valida horas (0-23)\r\n      if (hours.length === 2 && parseInt(hours) > 23) {\r\n        cleaned = '23' + cleaned.substring(2);\r\n      }\r\n\r\n      // Valida minutos (0-59)\r\n      if (minutes && minutes.length === 2 && parseInt(minutes) > 59) {\r\n        cleaned = cleaned.substring(0, 3) + '59';\r\n      }\r\n    }\r\n\r\n    return cleaned;\r\n  };\r\n\r\n  // Manipulador de mudança do input\r\n  const handleChange = (e) => {\r\n    console.log('TimeInput handleChange - valor original:', e.target.value);\r\n    const newValue = formatTimeValue(e.target.value);\r\n    console.log('TimeInput handleChange - valor formatado:', newValue);\r\n\r\n    // Atualiza apenas o estado interno, sem notificar o componente pai ainda\r\n    // Isso evita que o React recrie o componente e cause perda de foco\r\n    setInputValue(newValue);\r\n    console.log('TimeInput handleChange - estado interno atualizado');\r\n  };\r\n\r\n  // Verifica se o formato do horário é válido (HH:MM)\r\n  const isValidTimeFormat = (val) => {\r\n    if (!val) return true; // Vazio é considerado válido\r\n\r\n    // Verifica o formato HH:MM\r\n    const regex = /^([0-1][0-9]|2[0-3]):([0-5][0-9])$/;\r\n    return regex.test(val);\r\n  };\r\n\r\n  // Manipulador de teclas\r\n  const handleKeyDown = (e) => {\r\n    // Adiciona ':' automaticamente após digitar 2 números\r\n    if (\r\n      e.target.value.length === 2 &&\r\n      !e.target.value.includes(':') &&\r\n      e.key !== 'Backspace' &&\r\n      e.key !== 'Delete' &&\r\n      e.key !== 'Tab' &&\r\n      e.key !== ':'\r\n    ) {\r\n      const newValue = e.target.value + ':';\r\n      setInputValue(newValue);\r\n      e.preventDefault();\r\n\r\n      // Posiciona o cursor após o ':'\r\n      setTimeout(() => {\r\n        if (inputRef.current) {\r\n          inputRef.current.selectionStart = 3;\r\n          inputRef.current.selectionEnd = 3;\r\n        }\r\n      }, 0);\r\n    }\r\n\r\n    // Permite teclas de navegação e edição\r\n    if (\r\n      ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End', 'Tab', 'Backspace', 'Delete'].includes(e.key) ||\r\n      (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase()))\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // Permite apenas números e ':'\r\n    if (!/[0-9:]/.test(e.key)) {\r\n      e.preventDefault();\r\n    }\r\n  };\r\n\r\n  // Manipulador de foco\r\n  const handleFocus = (e) => {\r\n    // Seleciona todo o texto ao focar\r\n    e.target.select();\r\n  };\r\n\r\n  // Manipulador de perda de foco\r\n  const handleBlur = () => {\r\n    console.log('TimeInput handleBlur - valor atual:', inputValue);\r\n\r\n    // Formata o valor final ao perder o foco\r\n    if (inputValue && !isValidTimeFormat(inputValue)) {\r\n      // Se o valor não estiver completo, tenta completá-lo\r\n      let formattedValue = inputValue;\r\n\r\n      if (formattedValue.length === 1) {\r\n        // Um dígito: assume como hora e adiciona minutos zerados\r\n        formattedValue = '0' + formattedValue + ':00';\r\n      } else if (formattedValue.length === 2) {\r\n        // Dois dígitos: assume como hora e adiciona minutos zerados\r\n        formattedValue = formattedValue + ':00';\r\n      } else if (formattedValue.includes(':')) {\r\n        const [hours, minutes] = formattedValue.split(':');\r\n\r\n        // Completa horas se necessário\r\n        let formattedHours = hours;\r\n        if (hours.length === 1) {\r\n          formattedHours = '0' + hours;\r\n        }\r\n\r\n        // Completa minutos se necessário\r\n        let formattedMinutes = minutes || '00';\r\n        if (minutes && minutes.length === 1) {\r\n          formattedMinutes = minutes + '0';\r\n        }\r\n\r\n        formattedValue = formattedHours + ':' + formattedMinutes;\r\n      }\r\n\r\n      // Atualiza o valor interno\r\n      if (isValidTimeFormat(formattedValue)) {\r\n        console.log('TimeInput handleBlur - valor formatado:', formattedValue);\r\n        setInputValue(formattedValue);\r\n        // Notifica o componente pai com o valor formatado\r\n        onChange(formattedValue);\r\n      }\r\n    } else if (inputValue) { // Só notifica se houver um valor válido\r\n      // Se o valor já estiver no formato correto, notifica o componente pai\r\n      console.log('TimeInput handleBlur - notificando componente pai com valor atual');\r\n      onChange(inputValue);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col\">\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        value={inputValue}\r\n        onChange={handleChange}\r\n        onKeyDown={handleKeyDown}\r\n        onFocus={handleFocus}\r\n        onBlur={handleBlur}\r\n        placeholder={placeholder}\r\n        disabled={disabled}\r\n        id={id}\r\n        name={name}\r\n        className={className}\r\n        maxLength={5}\r\n      />\r\n      {error && <p className={errorClassName}>{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TimeInput;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA;;;;;;;;;;;;CAYC,GACD,MAAM,YAAY,CAAC,EACjB,OAAO,eAAe,EAAE,EACxB,QAAQ,EACR,YAAY,EAAE,EACd,cAAc,OAAO,EACrB,WAAW,KAAK,EAChB,EAAE,EACF,IAAI,EACJ,KAAK,EACL,iBAAiB,2BAA2B,EAC7C;IACC,0BAA0B;IAC1B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,yDAAyD;IACzD,yDAAyD;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;IAE7D,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,8CAA8C;QAC9C,IAAI,UAAU,IAAI,OAAO,CAAC,WAAW;QAErC,yDAAyD;QACzD,IAAI,QAAQ,MAAM,GAAG,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM;YAChD,UAAU,QAAQ,SAAS,CAAC,GAAG,KAAK,MAAM,QAAQ,SAAS,CAAC;QAC9D;QAEA,gDAAgD;QAChD,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,UAAU,QAAQ,SAAS,CAAC,GAAG;QACjC;QAEA,yBAAyB;QACzB,IAAI,QAAQ,QAAQ,CAAC,MAAM;YACzB,MAAM,CAAC,OAAO,QAAQ,GAAG,QAAQ,KAAK,CAAC;YAEvC,sBAAsB;YACtB,IAAI,MAAM,MAAM,KAAK,KAAK,SAAS,SAAS,IAAI;gBAC9C,UAAU,OAAO,QAAQ,SAAS,CAAC;YACrC;YAEA,wBAAwB;YACxB,IAAI,WAAW,QAAQ,MAAM,KAAK,KAAK,SAAS,WAAW,IAAI;gBAC7D,UAAU,QAAQ,SAAS,CAAC,GAAG,KAAK;YACtC;QACF;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,eAAe,CAAC;QACpB,QAAQ,GAAG,CAAC,4CAA4C,EAAE,MAAM,CAAC,KAAK;QACtE,MAAM,WAAW,gBAAgB,EAAE,MAAM,CAAC,KAAK;QAC/C,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,yEAAyE;QACzE,mEAAmE;QACnE,cAAc;QACd,QAAQ,GAAG,CAAC;IACd;IAEA,oDAAoD;IACpD,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,KAAK,OAAO,MAAM,6BAA6B;QAEpD,2BAA2B;QAC3B,MAAM,QAAQ;QACd,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,CAAC;QACrB,sDAAsD;QACtD,IACE,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,KAC1B,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QACzB,EAAE,GAAG,KAAK,eACV,EAAE,GAAG,KAAK,YACV,EAAE,GAAG,KAAK,SACV,EAAE,GAAG,KAAK,KACV;YACA,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,GAAG;YAClC,cAAc;YACd,EAAE,cAAc;YAEhB,gCAAgC;YAChC,WAAW;gBACT,IAAI,SAAS,OAAO,EAAE;oBACpB,SAAS,OAAO,CAAC,cAAc,GAAG;oBAClC,SAAS,OAAO,CAAC,YAAY,GAAG;gBAClC;YACF,GAAG;QACL;QAEA,uCAAuC;QACvC,IACE;YAAC;YAAa;YAAc;YAAW;YAAa;YAAQ;YAAO;YAAO;YAAa;SAAS,CAAC,QAAQ,CAAC,EAAE,GAAG,KAC9G,EAAE,OAAO,IAAI;YAAC;YAAK;YAAK;YAAK;SAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC,WAAW,KAC7D;YACA;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAC,SAAS,IAAI,CAAC,EAAE,GAAG,GAAG;YACzB,EAAE,cAAc;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc,CAAC;QACnB,kCAAkC;QAClC,EAAE,MAAM,CAAC,MAAM;IACjB;IAEA,+BAA+B;IAC/B,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,yCAAyC;QACzC,IAAI,cAAc,CAAC,kBAAkB,aAAa;YAChD,qDAAqD;YACrD,IAAI,iBAAiB;YAErB,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,yDAAyD;gBACzD,iBAAiB,MAAM,iBAAiB;YAC1C,OAAO,IAAI,eAAe,MAAM,KAAK,GAAG;gBACtC,4DAA4D;gBAC5D,iBAAiB,iBAAiB;YACpC,OAAO,IAAI,eAAe,QAAQ,CAAC,MAAM;gBACvC,MAAM,CAAC,OAAO,QAAQ,GAAG,eAAe,KAAK,CAAC;gBAE9C,+BAA+B;gBAC/B,IAAI,iBAAiB;gBACrB,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,iBAAiB,MAAM;gBACzB;gBAEA,iCAAiC;gBACjC,IAAI,mBAAmB,WAAW;gBAClC,IAAI,WAAW,QAAQ,MAAM,KAAK,GAAG;oBACnC,mBAAmB,UAAU;gBAC/B;gBAEA,iBAAiB,iBAAiB,MAAM;YAC1C;YAEA,2BAA2B;YAC3B,IAAI,kBAAkB,iBAAiB;gBACrC,QAAQ,GAAG,CAAC,2CAA2C;gBACvD,cAAc;gBACd,kDAAkD;gBAClD,SAAS;YACX;QACF,OAAO,IAAI,YAAY;YACrB,sEAAsE;YACtE,QAAQ,GAAG,CAAC;YACZ,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,UAAU;gBACV,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,WAAW;;;;;;YAEZ,uBAAS,8OAAC;gBAAE,WAAW;0BAAiB;;;;;;;;;;;;AAG/C;uCAEe"}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/workingHours/BranchWorkingHoursForm.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\r\nimport { Clock, Plus, Trash, AlertCircle, Info } from 'lucide-react';\r\nimport TimeInput from '../common/TimeInput';\r\n\r\nconst DAYS_OF_WEEK = [\r\n  { value: '1', label: 'Segunda-feira' },\r\n  { value: '2', label: 'Terça-feira' },\r\n  { value: '3', label: 'Quarta-feira' },\r\n  { value: '4', label: 'Quinta-feira' },\r\n  { value: '5', label: '<PERSON>ta-feira' },\r\n  { value: '6', label: 'Sábado' },\r\n  { value: '0', label: 'Domingo' },\r\n];\r\n\r\nconst BranchWorkingHoursForm = forwardRef(({\r\n  defaultWorkingHours,\r\n  onChange,\r\n  isLoading,\r\n  labelClasses,\r\n  inputClasses,\r\n  errorClasses,\r\n  onValidationChange // Nova prop para informar o componente pai sobre o estado da validação\r\n}, ref) => {\r\n  const [workingHours, setWorkingHours] = useState({});\r\n  const [errors, setErrors] = useState({});\r\n\r\n  // Initialize working hours from props or with default values\r\n  useEffect(() => {\r\n    if (defaultWorkingHours) {\r\n      // Converte os dados da API para o formato de exibição\r\n      const displayHours = {};\r\n\r\n      Object.keys(defaultWorkingHours).forEach(day => {\r\n        displayHours[day] = defaultWorkingHours[day].map(slot => ({\r\n          startTime: minutesToTime(slot.startTimeMinutes),\r\n          endTime: minutesToTime(slot.endTimeMinutes)\r\n        }));\r\n      });\r\n\r\n      console.log('useEffect - dados convertidos para exibição:', displayHours);\r\n      setWorkingHours(displayHours);\r\n    } else {\r\n      // Default working hours: Monday to Friday, 9:00-18:00\r\n      const defaultHours = {};\r\n\r\n      // Add default hours for weekdays (1-5)\r\n      for (let i = 1; i <= 5; i++) {\r\n        defaultHours[i] = [\r\n          { startTime: '09:00', endTime: '18:00' }\r\n        ];\r\n      }\r\n\r\n      console.log('useEffect - usando horários padrão:', defaultHours);\r\n      setWorkingHours(defaultHours);\r\n    }\r\n  }, [defaultWorkingHours]);\r\n\r\n  // Convert minutes to time string (HH:MM)\r\n  const minutesToTime = (minutes) => {\r\n    if (minutes === null || minutes === undefined) return '';\r\n    const hours = Math.floor(minutes / 60);\r\n    const mins = minutes % 60;\r\n    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;\r\n  };\r\n\r\n  // Convert time string (HH:MM) to minutes\r\n  const timeToMinutes = (timeStr) => {\r\n    if (!timeStr) return null;\r\n\r\n    // Garantir que a string tem o formato correto\r\n    if (!timeStr.includes(':')) {\r\n      // Se não tiver ':', tenta formatar\r\n      if (timeStr.length <= 2) {\r\n        // Apenas horas\r\n        return parseInt(timeStr) * 60;\r\n      } else {\r\n        // Assume que os dois primeiros dígitos são horas e o resto são minutos\r\n        const hours = parseInt(timeStr.slice(0, 2));\r\n        const minutes = parseInt(timeStr.slice(2));\r\n        return hours * 60 + minutes;\r\n      }\r\n    }\r\n\r\n    // Formato normal HH:MM\r\n    const parts = timeStr.split(':');\r\n    const hours = parseInt(parts[0] || '0');\r\n    const minutes = parseInt(parts[1] || '0');\r\n\r\n    // Validação básica\r\n    if (isNaN(hours) || isNaN(minutes)) return null;\r\n\r\n    return hours * 60 + minutes;\r\n  };\r\n\r\n  // Format working hours for display and editing\r\n  const formatWorkingHoursForDisplay = (workingHoursData) => {\r\n    console.log('formatWorkingHoursForDisplay - dados recebidos:', workingHoursData);\r\n    const formatted = {};\r\n\r\n    Object.keys(workingHoursData).forEach(dayOfWeek => {\r\n      console.log('formatWorkingHoursForDisplay - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);\r\n\r\n      formatted[dayOfWeek] = workingHoursData[dayOfWeek].map(slot => {\r\n        // Verifica se os campos _startTime e _endTime existem (campos preservados)\r\n        if (slot._startTime !== undefined || slot._endTime !== undefined) {\r\n          console.log('formatWorkingHoursForDisplay - usando campos preservados');\r\n          return {\r\n            startTime: slot._startTime || '',\r\n            endTime: slot._endTime || ''\r\n          };\r\n        }\r\n\r\n        // Caso contrário, converte de minutos para string\r\n        const startTime = minutesToTime(slot.startTimeMinutes);\r\n        const endTime = minutesToTime(slot.endTimeMinutes);\r\n\r\n        console.log('formatWorkingHoursForDisplay - convertendo slot:',\r\n          'startTimeMinutes:', slot.startTimeMinutes, '->', startTime,\r\n          'endTimeMinutes:', slot.endTimeMinutes, '->', endTime);\r\n\r\n        return {\r\n          startTime,\r\n          endTime\r\n          // Removidos os campos de intervalo\r\n        };\r\n      });\r\n    });\r\n\r\n    console.log('formatWorkingHoursForDisplay - resultado formatado:', formatted);\r\n    return formatted;\r\n  };\r\n\r\n  // Format working hours for API\r\n  const formatWorkingHoursForAPI = (workingHoursData) => {\r\n    console.log('formatWorkingHoursForAPI - dados recebidos:', workingHoursData);\r\n    const formatted = {};\r\n\r\n    Object.keys(workingHoursData).forEach(dayOfWeek => {\r\n      console.log('formatWorkingHoursForAPI - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);\r\n\r\n      // Filtra slots vazios (sem horário de início e término)\r\n      const validSlots = workingHoursData[dayOfWeek].filter(slot => {\r\n        const hasStartTime = slot.startTime && slot.startTime.trim() !== '';\r\n        const hasEndTime = slot.endTime && slot.endTime.trim() !== '';\r\n        return hasStartTime || hasEndTime;\r\n      });\r\n\r\n      console.log('formatWorkingHoursForAPI - slots válidos:', validSlots);\r\n\r\n      formatted[dayOfWeek] = validSlots.map(slot => {\r\n        // Verifica se os valores existem antes de convertê-los\r\n        const startTime = slot.startTime || '';\r\n        const endTime = slot.endTime || '';\r\n\r\n        const startTimeMinutes = timeToMinutes(startTime);\r\n        const endTimeMinutes = timeToMinutes(endTime);\r\n\r\n        console.log('formatWorkingHoursForAPI - convertendo slot:',\r\n          'startTime:', startTime, '->', startTimeMinutes, 'minutos',\r\n          'endTime:', endTime, '->', endTimeMinutes, 'minutos');\r\n\r\n        return {\r\n          startTimeMinutes,\r\n          endTimeMinutes,\r\n          // Removidos os campos de intervalo\r\n          breakStartMinutes: null,\r\n          breakEndMinutes: null\r\n        };\r\n      });\r\n\r\n      // Se não houver slots válidos, adiciona um slot vazio\r\n      if (formatted[dayOfWeek].length === 0) {\r\n        formatted[dayOfWeek] = [{\r\n          startTimeMinutes: null,\r\n          endTimeMinutes: null,\r\n          breakStartMinutes: null,\r\n          breakEndMinutes: null\r\n        }];\r\n      }\r\n    });\r\n\r\n    console.log('formatWorkingHoursForAPI - resultado formatado:', formatted);\r\n    return formatted;\r\n  };\r\n\r\n  // Handle changes to working hours\r\n  const handleWorkingHoursChange = (dayOfWeek, index, field, value) => {\r\n    console.log('handleWorkingHoursChange - dia:', dayOfWeek, 'índice:', index, 'campo:', field, 'valor:', value);\r\n\r\n    // Cria uma cópia profunda do estado atual para evitar problemas de referência\r\n    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));\r\n\r\n    if (!newWorkingHours[dayOfWeek]) {\r\n      newWorkingHours[dayOfWeek] = [];\r\n      console.log('handleWorkingHoursChange - criando array para o dia', dayOfWeek);\r\n    }\r\n\r\n    if (!newWorkingHours[dayOfWeek][index]) {\r\n      newWorkingHours[dayOfWeek][index] = {\r\n        startTime: '',\r\n        endTime: ''\r\n      };\r\n      console.log('handleWorkingHoursChange - criando objeto para o índice', index);\r\n    }\r\n\r\n    // Preserva os valores existentes e atualiza apenas o campo especificado\r\n    console.log('handleWorkingHoursChange - valor anterior:', newWorkingHours[dayOfWeek][index][field]);\r\n    newWorkingHours[dayOfWeek][index][field] = value;\r\n    console.log('handleWorkingHoursChange - novo valor:', newWorkingHours[dayOfWeek][index][field]);\r\n    console.log('handleWorkingHoursChange - slot completo:', newWorkingHours[dayOfWeek][index]);\r\n\r\n    // Atualiza o estado local\r\n    setWorkingHours(newWorkingHours);\r\n    console.log('handleWorkingHoursChange - estado atualizado');\r\n    console.log('handleWorkingHoursChange - estado completo:', newWorkingHours);\r\n\r\n    // Validate time slots\r\n    validateTimeSlot(dayOfWeek, index, newWorkingHours[dayOfWeek][index]);\r\n\r\n    // Notifica o componente pai com uma cópia dos dados\r\n    if (onChange) {\r\n      console.log('handleWorkingHoursChange - notificando componente pai');\r\n\r\n      // Converte os dados para o formato da API\r\n      const apiData = formatWorkingHoursForAPI(newWorkingHours);\r\n      console.log('handleWorkingHoursChange - dados formatados para API:', apiData);\r\n      onChange(apiData);\r\n    }\r\n  };\r\n\r\n  // Validate time slot - validação durante a edição (menos rigorosa)\r\n  const validateTimeSlot = (dayOfWeek, index, slot) => {\r\n    const newErrors = { ...errors };\r\n\r\n    if (!newErrors[dayOfWeek]) {\r\n      newErrors[dayOfWeek] = [];\r\n    }\r\n\r\n    if (!newErrors[dayOfWeek][index]) {\r\n      newErrors[dayOfWeek][index] = {};\r\n    }\r\n\r\n    // Clear previous errors\r\n    newErrors[dayOfWeek][index] = {};\r\n\r\n    // Só validamos campos obrigatórios quando ambos estiverem preenchidos ou quando o formulário for enviado\r\n    // Isso permite que o usuário preencha um campo por vez\r\n\r\n    // Validate that end time is after start time if both are provided\r\n    if (slot.startTime && slot.endTime) {\r\n      const startMinutes = timeToMinutes(slot.startTime);\r\n      const endMinutes = timeToMinutes(slot.endTime);\r\n\r\n      if (endMinutes <= startMinutes) {\r\n        newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n  };\r\n\r\n  // Validate all time slots - validação completa para submissão do formulário\r\n  const validateAllTimeSlots = () => {\r\n    const newErrors = {};\r\n    let isValid = true;\r\n\r\n    // Verificar cada dia e cada slot\r\n    Object.keys(workingHours).forEach(dayOfWeek => {\r\n      if (!newErrors[dayOfWeek]) {\r\n        newErrors[dayOfWeek] = [];\r\n      }\r\n\r\n      workingHours[dayOfWeek].forEach((slot, index) => {\r\n        if (!newErrors[dayOfWeek][index]) {\r\n          newErrors[dayOfWeek][index] = {};\r\n        }\r\n\r\n        // Validar horário inicial\r\n        if (!slot.startTime) {\r\n          newErrors[dayOfWeek][index].startTime = 'Horário de início é obrigatório';\r\n          isValid = false;\r\n        }\r\n\r\n        // Validar horário final\r\n        if (!slot.endTime) {\r\n          newErrors[dayOfWeek][index].endTime = 'Horário de término é obrigatório';\r\n          isValid = false;\r\n        }\r\n\r\n        // Validar que o horário final é após o inicial\r\n        if (slot.startTime && slot.endTime) {\r\n          const startMinutes = timeToMinutes(slot.startTime);\r\n          const endMinutes = timeToMinutes(slot.endTime);\r\n\r\n          if (endMinutes <= startMinutes) {\r\n            newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';\r\n            isValid = false;\r\n          }\r\n        }\r\n\r\n        // Removida a validação de intervalos\r\n      });\r\n    });\r\n\r\n    setErrors(newErrors);\r\n\r\n    // Notificar o componente pai sobre o estado da validação\r\n    if (onValidationChange) {\r\n      onValidationChange(isValid);\r\n    }\r\n\r\n    return isValid;\r\n  };\r\n\r\n  // Add a new time slot for a day (apenas um horário por dia)\r\n  const addTimeSlot = (dayOfWeek) => {\r\n    const newWorkingHours = { ...workingHours };\r\n\r\n    // Se já existe um horário para este dia, não adiciona outro\r\n    if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {\r\n      return;\r\n    }\r\n\r\n    if (!newWorkingHours[dayOfWeek]) {\r\n      newWorkingHours[dayOfWeek] = [];\r\n    }\r\n\r\n    newWorkingHours[dayOfWeek].push({\r\n      startTime: '',\r\n      endTime: ''\r\n    });\r\n\r\n    setWorkingHours(newWorkingHours);\r\n\r\n    // Notify parent component\r\n    if (onChange) {\r\n      onChange(formatWorkingHoursForAPI(newWorkingHours));\r\n    }\r\n  };\r\n\r\n  // Remove a time slot\r\n  const removeTimeSlot = (dayOfWeek, index) => {\r\n    const newWorkingHours = { ...workingHours };\r\n\r\n    if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {\r\n      newWorkingHours[dayOfWeek].splice(index, 1);\r\n\r\n      // If no more slots for this day, remove the day\r\n      if (newWorkingHours[dayOfWeek].length === 0) {\r\n        delete newWorkingHours[dayOfWeek];\r\n      }\r\n\r\n      setWorkingHours(newWorkingHours);\r\n\r\n      // Remove errors for this slot\r\n      const newErrors = { ...errors };\r\n      if (newErrors[dayOfWeek] && newErrors[dayOfWeek].length > 0) {\r\n        newErrors[dayOfWeek].splice(index, 1);\r\n\r\n        if (newErrors[dayOfWeek].length === 0) {\r\n          delete newErrors[dayOfWeek];\r\n        }\r\n\r\n        setErrors(newErrors);\r\n      }\r\n\r\n      // Notify parent component\r\n      if (onChange) {\r\n        onChange(formatWorkingHoursForAPI(newWorkingHours));\r\n      }\r\n    }\r\n  };\r\n\r\n  // Copy working hours from one day to other days\r\n  const copyWorkingHours = (fromDay, toDays) => {\r\n    console.log('copyWorkingHours - copiando de:', fromDay, 'para:', toDays);\r\n    console.log('copyWorkingHours - horários atuais:', workingHours);\r\n\r\n    if (!workingHours[fromDay] || workingHours[fromDay].length === 0) {\r\n      console.log('copyWorkingHours - não há horários para copiar do dia', fromDay);\r\n      return;\r\n    }\r\n\r\n    // Cria uma cópia profunda para evitar problemas de referência\r\n    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));\r\n\r\n    console.log('copyWorkingHours - horários a serem copiados:', workingHours[fromDay]);\r\n\r\n    toDays.forEach(toDay => {\r\n      console.log('copyWorkingHours - copiando para o dia:', toDay);\r\n      newWorkingHours[toDay] = JSON.parse(JSON.stringify(workingHours[fromDay]));\r\n    });\r\n\r\n    console.log('copyWorkingHours - novos horários:', newWorkingHours);\r\n\r\n    setWorkingHours(newWorkingHours);\r\n    console.log('copyWorkingHours - estado atualizado');\r\n\r\n    // Notify parent component\r\n    if (onChange) {\r\n      console.log('copyWorkingHours - notificando componente pai');\r\n      const formattedData = formatWorkingHoursForAPI(newWorkingHours);\r\n      console.log('copyWorkingHours - dados formatados para API:', formattedData);\r\n      onChange(formattedData);\r\n    }\r\n  };\r\n\r\n  // Copy Monday to all weekdays\r\n  const copyMondayToWeekdays = () => {\r\n    console.log('copyMondayToWeekdays - chamado');\r\n    console.log('copyMondayToWeekdays - estado atual:', workingHours);\r\n\r\n    // Verifica se há horários válidos na segunda-feira\r\n    if (!workingHours['1'] || workingHours['1'].length === 0) {\r\n      console.log('copyMondayToWeekdays - não há horários na segunda-feira para copiar');\r\n      alert('Não há horários na segunda-feira para copiar.');\r\n      return;\r\n    }\r\n\r\n    // Verifica se os horários da segunda-feira têm valores válidos\r\n    const mondaySlots = workingHours['1'];\r\n    console.log('copyMondayToWeekdays - slots da segunda-feira:', mondaySlots);\r\n\r\n    // Verifica se há pelo menos um slot com horário de início e término válidos\r\n    const hasValidSlot = mondaySlots.some(slot => {\r\n      return slot.startTime && slot.startTime.trim() !== '' &&\r\n             slot.endTime && slot.endTime.trim() !== '';\r\n    });\r\n\r\n    if (!hasValidSlot) {\r\n      console.log('copyMondayToWeekdays - não há horários válidos na segunda-feira');\r\n      alert('Não há horários válidos na segunda-feira. Certifique-se de que há pelo menos um horário com início e término preenchidos.');\r\n      return;\r\n    }\r\n\r\n    // Filtra apenas os slots válidos\r\n    const validMondaySlots = mondaySlots.filter(slot => {\r\n      return slot.startTime && slot.startTime.trim() !== '' &&\r\n             slot.endTime && slot.endTime.trim() !== '';\r\n    });\r\n\r\n    console.log('copyMondayToWeekdays - slots válidos da segunda-feira:', validMondaySlots);\r\n\r\n    // Cria uma cópia profunda do estado atual\r\n    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));\r\n\r\n    // Copia os horários da segunda-feira para os outros dias da semana\r\n    ['2', '3', '4', '5'].forEach(day => {\r\n      console.log(`copyMondayToWeekdays - copiando para o dia ${day}`);\r\n\r\n      // Garante que o array para o dia existe\r\n      if (!newWorkingHours[day]) {\r\n        newWorkingHours[day] = [];\r\n      }\r\n\r\n      // Limpa os horários existentes e adiciona os novos\r\n      newWorkingHours[day] = JSON.parse(JSON.stringify(validMondaySlots));\r\n    });\r\n\r\n    console.log('copyMondayToWeekdays - novos horários:', newWorkingHours);\r\n\r\n    // Atualiza o estado\r\n    setWorkingHours(newWorkingHours);\r\n    console.log('copyMondayToWeekdays - estado atualizado');\r\n\r\n    // Notifica o componente pai\r\n    if (onChange) {\r\n      console.log('copyMondayToWeekdays - notificando componente pai');\r\n\r\n      // Converte os dados para o formato da API\r\n      const apiData = formatWorkingHoursForAPI(newWorkingHours);\r\n      console.log('copyMondayToWeekdays - dados formatados para API:', apiData);\r\n      onChange(apiData);\r\n    }\r\n\r\n    alert('Horários da segunda-feira copiados com sucesso para os outros dias da semana!');\r\n  };\r\n\r\n  // Render time input field\r\n  const renderTimeInput = (dayOfWeek, index, field, label, value) => {\r\n    const hasError = errors[dayOfWeek]?.[index]?.[field];\r\n\r\n    // Função simplificada para lidar com mudanças no input de horário\r\n    const handleTimeChange = (newValue) => {\r\n      // Só atualiza se o valor for válido\r\n      if (newValue) {\r\n        console.log('handleTimeChange - novo valor válido:', newValue);\r\n        handleWorkingHoursChange(dayOfWeek, index, field, newValue);\r\n      } else {\r\n        console.log('handleTimeChange - ignorando valor vazio');\r\n      }\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex flex-col\">\r\n        <label className={`${labelClasses} text-xs`}>\r\n          {label}\r\n        </label>\r\n        <TimeInput\r\n          value={value || ''}\r\n          onChange={handleTimeChange}\r\n          className={`${inputClasses} ${hasError ? 'border-red-500 dark:border-red-700' : ''}`}\r\n          disabled={isLoading}\r\n          error={hasError}\r\n          errorClassName={errorClasses}\r\n        />\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Expor a função de validação para o componente pai via useImperativeHandle\r\n  React.useImperativeHandle(ref, () => ({\r\n    validateAllTimeSlots\r\n  }));\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <h4 className=\"text-base font-medium text-neutral-800 dark:text-gray-200 flex items-center gap-2\">\r\n          <Clock className=\"h-4 w-4 text-primary-500 dark:text-primary-400\" />\r\n          Horários de Trabalho Padrão\r\n        </h4>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => {\r\n            console.log('Botão \"Copiar Segunda para dias úteis\" clicado');\r\n            copyMondayToWeekdays();\r\n          }}\r\n          className=\"text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors\"\r\n        >\r\n          Copiar Segunda para dias úteis\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-4\">\r\n        <div className=\"flex items-start gap-2\">\r\n          <Info size={16} className=\"text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0\" />\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n            Configure os horários de trabalho padrão para esta unidade. Estes horários serão aplicados automaticamente a todos os usuários associados a esta unidade.\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-8\">\r\n        {DAYS_OF_WEEK.map((day) => (\r\n          <div key={day.value} className=\"border-b border-gray-200 dark:border-gray-700 pb-6 last:border-0\">\r\n            <div className=\"flex justify-between items-center mb-3\">\r\n              <h5 className=\"font-medium text-gray-800 dark:text-gray-200\">{day.label}</h5>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => addTimeSlot(day.value)}\r\n                disabled={workingHours[day.value] && workingHours[day.value].length > 0}\r\n                className=\"text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                <Plus size={12} />\r\n                Adicionar Horário\r\n              </button>\r\n            </div>\r\n\r\n            {(!workingHours[day.value] || workingHours[day.value].length === 0) ? (\r\n              <div className=\"text-sm text-gray-500 dark:text-gray-400 italic\">\r\n                Nenhum horário configurado para este dia\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-4\">\r\n                {workingHours[day.value].map((slot, index) => (\r\n                  <div key={index} className=\"grid grid-cols-1 md:grid-cols-3 gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700\">\r\n                    {/* Start Time */}\r\n                    <div className=\"md:col-span-1\">\r\n                      {renderTimeInput(day.value, index, 'startTime', 'Início', slot.startTime)}\r\n                    </div>\r\n\r\n                    {/* End Time */}\r\n                    <div className=\"md:col-span-1\">\r\n                      {renderTimeInput(day.value, index, 'endTime', 'Término', slot.endTime)}\r\n                    </div>\r\n\r\n                    {/* Remove Button */}\r\n                    <div className=\"md:col-span-1 flex items-end justify-end\">\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => removeTimeSlot(day.value, index)}\r\n                        className=\"text-xs px-2 py-1 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors flex items-center gap-1\"\r\n                      >\r\n                        <Trash size={12} />\r\n                        Remover\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nexport default BranchWorkingHoursForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAAA;AAAA;AAAA;AAHA;;;;;AAMA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAK,OAAO;IAAgB;IACrC;QAAE,OAAO;QAAK,OAAO;IAAc;IACnC;QAAE,OAAO;QAAK,OAAO;IAAe;IACpC;QAAE,OAAO;QAAK,OAAO;IAAe;IACpC;QAAE,OAAO;QAAK,OAAO;IAAc;IACnC;QAAE,OAAO;QAAK,OAAO;IAAS;IAC9B;QAAE,OAAO;QAAK,OAAO;IAAU;CAChC;AAED,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzC,mBAAmB,EACnB,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,kBAAkB,AAAC,uEAAuE;EAC3F,EAAE;IACD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEtC,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB;YACvB,sDAAsD;YACtD,MAAM,eAAe,CAAC;YAEtB,OAAO,IAAI,CAAC,qBAAqB,OAAO,CAAC,CAAA;gBACvC,YAAY,CAAC,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACxD,WAAW,cAAc,KAAK,gBAAgB;wBAC9C,SAAS,cAAc,KAAK,cAAc;oBAC5C,CAAC;YACH;YAEA,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,gBAAgB;QAClB,OAAO;YACL,sDAAsD;YACtD,MAAM,eAAe,CAAC;YAEtB,uCAAuC;YACvC,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;gBAC3B,YAAY,CAAC,EAAE,GAAG;oBAChB;wBAAE,WAAW;wBAAS,SAAS;oBAAQ;iBACxC;YACH;YAEA,QAAQ,GAAG,CAAC,uCAAuC;YACnD,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAoB;IAExB,yCAAyC;IACzC,MAAM,gBAAgB,CAAC;QACrB,IAAI,YAAY,QAAQ,YAAY,WAAW,OAAO;QACtD,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,OAAO,OAAO,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,MAAM,QAAQ,CAAC,GAAG,MAAM;IAC7E;IAEA,yCAAyC;IACzC,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,SAAS,OAAO;QAErB,8CAA8C;QAC9C,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM;YAC1B,mCAAmC;YACnC,IAAI,QAAQ,MAAM,IAAI,GAAG;gBACvB,eAAe;gBACf,OAAO,SAAS,WAAW;YAC7B,OAAO;gBACL,uEAAuE;gBACvE,MAAM,QAAQ,SAAS,QAAQ,KAAK,CAAC,GAAG;gBACxC,MAAM,UAAU,SAAS,QAAQ,KAAK,CAAC;gBACvC,OAAO,QAAQ,KAAK;YACtB;QACF;QAEA,uBAAuB;QACvB,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE,IAAI;QACnC,MAAM,UAAU,SAAS,KAAK,CAAC,EAAE,IAAI;QAErC,mBAAmB;QACnB,IAAI,MAAM,UAAU,MAAM,UAAU,OAAO;QAE3C,OAAO,QAAQ,KAAK;IACtB;IAEA,+CAA+C;IAC/C,MAAM,+BAA+B,CAAC;QACpC,QAAQ,GAAG,CAAC,mDAAmD;QAC/D,MAAM,YAAY,CAAC;QAEnB,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAA;YACpC,QAAQ,GAAG,CAAC,mDAAmD,WAAW,UAAU,gBAAgB,CAAC,UAAU;YAE/G,SAAS,CAAC,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;gBACrD,2EAA2E;gBAC3E,IAAI,KAAK,UAAU,KAAK,aAAa,KAAK,QAAQ,KAAK,WAAW;oBAChE,QAAQ,GAAG,CAAC;oBACZ,OAAO;wBACL,WAAW,KAAK,UAAU,IAAI;wBAC9B,SAAS,KAAK,QAAQ,IAAI;oBAC5B;gBACF;gBAEA,kDAAkD;gBAClD,MAAM,YAAY,cAAc,KAAK,gBAAgB;gBACrD,MAAM,UAAU,cAAc,KAAK,cAAc;gBAEjD,QAAQ,GAAG,CAAC,oDACV,qBAAqB,KAAK,gBAAgB,EAAE,MAAM,WAClD,mBAAmB,KAAK,cAAc,EAAE,MAAM;gBAEhD,OAAO;oBACL;oBACA;gBAEF;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,uDAAuD;QACnE,OAAO;IACT;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B,CAAC;QAChC,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,MAAM,YAAY,CAAC;QAEnB,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAA;YACpC,QAAQ,GAAG,CAAC,+CAA+C,WAAW,UAAU,gBAAgB,CAAC,UAAU;YAE3G,wDAAwD;YACxD,MAAM,aAAa,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;gBACpD,MAAM,eAAe,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,OAAO;gBACjE,MAAM,aAAa,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,OAAO;gBAC3D,OAAO,gBAAgB;YACzB;YAEA,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,SAAS,CAAC,UAAU,GAAG,WAAW,GAAG,CAAC,CAAA;gBACpC,uDAAuD;gBACvD,MAAM,YAAY,KAAK,SAAS,IAAI;gBACpC,MAAM,UAAU,KAAK,OAAO,IAAI;gBAEhC,MAAM,mBAAmB,cAAc;gBACvC,MAAM,iBAAiB,cAAc;gBAErC,QAAQ,GAAG,CAAC,gDACV,cAAc,WAAW,MAAM,kBAAkB,WACjD,YAAY,SAAS,MAAM,gBAAgB;gBAE7C,OAAO;oBACL;oBACA;oBACA,mCAAmC;oBACnC,mBAAmB;oBACnB,iBAAiB;gBACnB;YACF;YAEA,sDAAsD;YACtD,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;gBACrC,SAAS,CAAC,UAAU,GAAG;oBAAC;wBACtB,kBAAkB;wBAClB,gBAAgB;wBAChB,mBAAmB;wBACnB,iBAAiB;oBACnB;iBAAE;YACJ;QACF;QAEA,QAAQ,GAAG,CAAC,mDAAmD;QAC/D,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,2BAA2B,CAAC,WAAW,OAAO,OAAO;QACzD,QAAQ,GAAG,CAAC,mCAAmC,WAAW,WAAW,OAAO,UAAU,OAAO,UAAU;QAEvG,8EAA8E;QAC9E,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAElD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;YAC/B,eAAe,CAAC,UAAU,GAAG,EAAE;YAC/B,QAAQ,GAAG,CAAC,uDAAuD;QACrE;QAEA,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE;YACtC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG;gBAClC,WAAW;gBACX,SAAS;YACX;YACA,QAAQ,GAAG,CAAC,2DAA2D;QACzE;QAEA,wEAAwE;QACxE,QAAQ,GAAG,CAAC,8CAA8C,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM;QAClG,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG;QAC3C,QAAQ,GAAG,CAAC,0CAA0C,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM;QAC9F,QAAQ,GAAG,CAAC,6CAA6C,eAAe,CAAC,UAAU,CAAC,MAAM;QAE1F,0BAA0B;QAC1B,gBAAgB;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,+CAA+C;QAE3D,sBAAsB;QACtB,iBAAiB,WAAW,OAAO,eAAe,CAAC,UAAU,CAAC,MAAM;QAEpE,oDAAoD;QACpD,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YAEZ,0CAA0C;YAC1C,MAAM,UAAU,yBAAyB;YACzC,QAAQ,GAAG,CAAC,yDAAyD;YACrE,SAAS;QACX;IACF;IAEA,mEAAmE;IACnE,MAAM,mBAAmB,CAAC,WAAW,OAAO;QAC1C,MAAM,YAAY;YAAE,GAAG,MAAM;QAAC;QAE9B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YACzB,SAAS,CAAC,UAAU,GAAG,EAAE;QAC3B;QAEA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE;YAChC,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;QACjC;QAEA,wBAAwB;QACxB,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;QAE/B,yGAAyG;QACzG,uDAAuD;QAEvD,kEAAkE;QAClE,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,EAAE;YAClC,MAAM,eAAe,cAAc,KAAK,SAAS;YACjD,MAAM,aAAa,cAAc,KAAK,OAAO;YAE7C,IAAI,cAAc,cAAc;gBAC9B,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG;YACxC;QACF;QAEA,UAAU;IACZ;IAEA,4EAA4E;IAC5E,MAAM,uBAAuB;QAC3B,MAAM,YAAY,CAAC;QACnB,IAAI,UAAU;QAEd,iCAAiC;QACjC,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;YAChC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;gBACzB,SAAS,CAAC,UAAU,GAAG,EAAE;YAC3B;YAEA,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM;gBACrC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE;oBAChC,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;gBACjC;gBAEA,0BAA0B;gBAC1B,IAAI,CAAC,KAAK,SAAS,EAAE;oBACnB,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,GAAG;oBACxC,UAAU;gBACZ;gBAEA,wBAAwB;gBACxB,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG;oBACtC,UAAU;gBACZ;gBAEA,+CAA+C;gBAC/C,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,EAAE;oBAClC,MAAM,eAAe,cAAc,KAAK,SAAS;oBACjD,MAAM,aAAa,cAAc,KAAK,OAAO;oBAE7C,IAAI,cAAc,cAAc;wBAC9B,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,GAAG;wBACtC,UAAU;oBACZ;gBACF;YAEA,qCAAqC;YACvC;QACF;QAEA,UAAU;QAEV,yDAAyD;QACzD,IAAI,oBAAoB;YACtB,mBAAmB;QACrB;QAEA,OAAO;IACT;IAEA,4DAA4D;IAC5D,MAAM,cAAc,CAAC;QACnB,MAAM,kBAAkB;YAAE,GAAG,YAAY;QAAC;QAE1C,4DAA4D;QAC5D,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;YACvE;QACF;QAEA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE;YAC/B,eAAe,CAAC,UAAU,GAAG,EAAE;QACjC;QAEA,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC;YAC9B,WAAW;YACX,SAAS;QACX;QAEA,gBAAgB;QAEhB,0BAA0B;QAC1B,IAAI,UAAU;YACZ,SAAS,yBAAyB;QACpC;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC,WAAW;QACjC,MAAM,kBAAkB;YAAE,GAAG,YAAY;QAAC;QAE1C,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;YACvE,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;YAEzC,gDAAgD;YAChD,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;gBAC3C,OAAO,eAAe,CAAC,UAAU;YACnC;YAEA,gBAAgB;YAEhB,8BAA8B;YAC9B,MAAM,YAAY;gBAAE,GAAG,MAAM;YAAC;YAC9B,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;gBAC3D,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;gBAEnC,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,GAAG;oBACrC,OAAO,SAAS,CAAC,UAAU;gBAC7B;gBAEA,UAAU;YACZ;YAEA,0BAA0B;YAC1B,IAAI,UAAU;gBACZ,SAAS,yBAAyB;YACpC;QACF;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,CAAC,SAAS;QACjC,QAAQ,GAAG,CAAC,mCAAmC,SAAS,SAAS;QACjE,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG;YAChE,QAAQ,GAAG,CAAC,yDAAyD;YACrE;QACF;QAEA,8DAA8D;QAC9D,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAElD,QAAQ,GAAG,CAAC,iDAAiD,YAAY,CAAC,QAAQ;QAElF,OAAO,OAAO,CAAC,CAAA;YACb,QAAQ,GAAG,CAAC,2CAA2C;YACvD,eAAe,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,YAAY,CAAC,QAAQ;QAC1E;QAEA,QAAQ,GAAG,CAAC,sCAAsC;QAElD,gBAAgB;QAChB,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ,MAAM,gBAAgB,yBAAyB;YAC/C,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,SAAS;QACX;IACF;IAEA,8BAA8B;IAC9B,MAAM,uBAAuB;QAC3B,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,mDAAmD;QACnD,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG;YACxD,QAAQ,GAAG,CAAC;YACZ,MAAM;YACN;QACF;QAEA,+DAA+D;QAC/D,MAAM,cAAc,YAAY,CAAC,IAAI;QACrC,QAAQ,GAAG,CAAC,kDAAkD;QAE9D,4EAA4E;QAC5E,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA;YACpC,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,OAAO,MAC5C,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,OAAO;QACjD;QAEA,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,MAAM;YACN;QACF;QAEA,iCAAiC;QACjC,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA;YAC1C,OAAO,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI,OAAO,MAC5C,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,OAAO;QACjD;QAEA,QAAQ,GAAG,CAAC,0DAA0D;QAEtE,0CAA0C;QAC1C,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QAElD,mEAAmE;QACnE;YAAC;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAC,CAAA;YAC3B,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,KAAK;YAE/D,wCAAwC;YACxC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;gBACzB,eAAe,CAAC,IAAI,GAAG,EAAE;YAC3B;YAEA,mDAAmD;YACnD,eAAe,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;QACnD;QAEA,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,oBAAoB;QACpB,gBAAgB;QAChB,QAAQ,GAAG,CAAC;QAEZ,4BAA4B;QAC5B,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YAEZ,0CAA0C;YAC1C,MAAM,UAAU,yBAAyB;YACzC,QAAQ,GAAG,CAAC,qDAAqD;YACjE,SAAS;QACX;QAEA,MAAM;IACR;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC,WAAW,OAAO,OAAO,OAAO;QACvD,MAAM,WAAW,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM;QAEpD,kEAAkE;QAClE,MAAM,mBAAmB,CAAC;YACxB,oCAAoC;YACpC,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC,yCAAyC;gBACrD,yBAAyB,WAAW,OAAO,OAAO;YACpD,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAW,GAAG,aAAa,QAAQ,CAAC;8BACxC;;;;;;8BAEH,8OAAC,wIAAA,CAAA,UAAS;oBACR,OAAO,SAAS;oBAChB,UAAU;oBACV,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW,uCAAuC,IAAI;oBACpF,UAAU;oBACV,OAAO;oBACP,gBAAgB;;;;;;;;;;;;IAIxB;IAEA,4EAA4E;IAC5E,qMAAA,CAAA,UAAK,CAAC,mBAAmB,CAAC,KAAK,IAAM,CAAC;YACpC;QACF,CAAC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAmD;;;;;;;kCAItE,8OAAC;wBACC,MAAK;wBACL,SAAS;4BACP,QAAQ,GAAG,CAAC;4BACZ;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC1B,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;;;;;;0BAM5D,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;wBAAoB,WAAU;;0CAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgD,IAAI,KAAK;;;;;;kDACvE,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,YAAY,IAAI,KAAK;wCACpC,UAAU,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,GAAG;wCACtE,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;4BAKpB,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,KAAK,kBAC/D,8OAAC;gCAAI,WAAU;0CAAkD;;;;;qDAIjE,8OAAC;gCAAI,WAAU;0CACZ,YAAY,CAAC,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;wCAAgB,WAAU;;0DAEzB,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,IAAI,KAAK,EAAE,OAAO,aAAa,UAAU,KAAK,SAAS;;;;;;0DAI1E,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,IAAI,KAAK,EAAE,OAAO,WAAW,WAAW,KAAK,OAAO;;;;;;0DAIvE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,eAAe,IAAI,KAAK,EAAE;oDACzC,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;uCAlBf;;;;;;;;;;;uBArBR,IAAI,KAAK;;;;;;;;;;;;;;;;AAoD7B;uCAEe"}}, {"offset": {"line": 2767, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}