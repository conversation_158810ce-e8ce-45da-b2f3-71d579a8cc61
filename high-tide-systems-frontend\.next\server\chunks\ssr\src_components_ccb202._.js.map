{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialTriggerButton.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { HelpCircle } from 'lucide-react';\r\nimport { useTutorial } from '@/contexts/TutorialContext';\r\n\r\n/**\r\n * <PERSON>t<PERSON> para iniciar um tutorial\r\n * \r\n * @param {Object} props - Propriedades do componente\r\n * @param {Array} props.steps - Etapas do tutorial\r\n * @param {string} props.tutorialName - Nome único do tutorial\r\n * @param {boolean} props.showIfCompleted - Se deve mostrar o botão mesmo se o tutorial já foi concluído\r\n * @param {string} props.size - Tamanho do botão ('sm', 'md', 'lg')\r\n * @param {Object} props.className - Classes adicionais para o botão\r\n */\r\nconst TutorialTriggerButton = ({\r\n  steps,\r\n  tutorialName,\r\n  showIfCompleted = true,\r\n  size = 'md',\r\n  className = '',\r\n  children\r\n}) => {\r\n  const { startTutorial, isTutorialCompleted } = useTutorial();\r\n\r\n  // Não renderizar o botão se o tutorial já foi concluído e showIfCompleted for falso\r\n  if (!showIfCompleted && isTutorialCompleted(tutorialName)) {\r\n    return null;\r\n  }\r\n\r\n  // Determinar tamanho do botão\r\n  const sizeClasses = {\r\n    sm: 'p-1.5 text-xs',\r\n    md: 'p-2 text-sm',\r\n    lg: 'p-2.5 text-base'\r\n  };\r\n\r\n  // Determinar tamanho do ícone\r\n  const iconSize = {\r\n    sm: 14,\r\n    md: 16,\r\n    lg: 20\r\n  };\r\n\r\n  const isCompleted = isTutorialCompleted(tutorialName);\r\n\r\n  return (\r\n    <button\r\n      onClick={() => startTutorial(steps, tutorialName)}\r\n      className={`\r\n        relative flex items-center gap-1.5 rounded-full \r\n        ${isCompleted ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : 'bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/40 dark:text-primary-300 dark:hover:bg-primary-800/60'}\r\n        transition-colors ${sizeClasses[size]} ${className}\r\n      `}\r\n      aria-label={`Abrir tutorial: ${tutorialName}`}\r\n      title={isCompleted ? \"Ver tutorial novamente\" : \"Ver tutorial\"}\r\n    >\r\n      <HelpCircle size={iconSize[size]} />\r\n      {children}\r\n      \r\n      {/* Indicador de novo para tutoriais não completados */}\r\n      {!isCompleted && (\r\n        <span className=\"absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-primary-500 dark:bg-primary-400\" />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default TutorialTriggerButton;"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAHA;;;;;AAMA;;;;;;;;;CASC,GACD,MAAM,wBAAwB,CAAC,EAC7B,KAAK,EACL,YAAY,EACZ,kBAAkB,IAAI,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACT;IACC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAEzD,oFAAoF;IACpF,IAAI,CAAC,mBAAmB,oBAAoB,eAAe;QACzD,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,8BAA8B;IAC9B,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,oBAAoB;IAExC,qBACE,8OAAC;QACC,SAAS,IAAM,cAAc,OAAO;QACpC,WAAW,CAAC;;QAEV,EAAE,cAAc,2GAA2G,iIAAiI;0BAC1O,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU;MACrD,CAAC;QACD,cAAY,CAAC,gBAAgB,EAAE,cAAc;QAC7C,OAAO,cAAc,2BAA2B;;0BAEhD,8OAAC,kNAAA,CAAA,aAAU;gBAAC,MAAM,QAAQ,CAAC,KAAK;;;;;;YAC/B;YAGA,CAAC,6BACA,8OAAC;gBAAK,WAAU;;;;;;;;;;;;AAIxB;uCAEe"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/forms/ProfileImageUpload.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Upload, X, User } from 'lucide-react';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { personsService } from '@/app/modules/people/services/personsService';\r\nimport api from '@/utils/api';\r\n\r\nconst ProfileImageUpload = ({\r\n  personId,\r\n  onImageUploaded,\r\n  initialImageUrl = null,\r\n  deferUpload = false,\r\n  uploadRef = null,\r\n  showPreviewOnly = false,\r\n  previewFile = null\r\n}) => {\r\n  const [imageUrl, setImageUrl] = useState(initialImageUrl);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [previewUrl, setPreviewUrl] = useState(null);\r\n  const fileInputRef = useRef(null);\r\n  const { toast_success, toast_error } = useToast();\r\n\r\n  // Atualizar a URL da imagem quando o initialImageUrl mudar\r\n  useEffect(() => {\r\n    console.log('initialImageUrl mudou:', initialImageUrl);\r\n    setImageUrl(initialImageUrl);\r\n  }, [initialImageUrl]);\r\n\r\n  // Atualizar a pré-visualização quando o arquivo de pré-visualização mudar\r\n  useEffect(() => {\r\n    if (previewFile) {\r\n      console.log('Arquivo de pré-visualização recebido:', previewFile.name);\r\n      // Criar URL de pré-visualização para o arquivo\r\n      const url = URL.createObjectURL(previewFile);\r\n      setPreviewUrl(url);\r\n      setSelectedFile(previewFile);\r\n    }\r\n  }, [previewFile]);\r\n\r\n  // Limpar a URL de pré-visualização quando o componente for desmontado\r\n  useEffect(() => {\r\n    return () => {\r\n      if (previewUrl) {\r\n        URL.revokeObjectURL(previewUrl);\r\n      }\r\n    };\r\n  }, [previewUrl]);\r\n\r\n  // Expor o método de upload para o componente pai\r\n  useEffect(() => {\r\n    if (uploadRef) {\r\n      uploadRef.current = {\r\n        uploadSelectedImage: async () => {\r\n          console.log('Método uploadSelectedImage chamado');\r\n          console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');\r\n          console.log('ID da pessoa:', personId);\r\n\r\n          if (selectedFile && personId) {\r\n            console.log('Iniciando upload do arquivo selecionado');\r\n            const result = await uploadImage(selectedFile);\r\n            console.log('Resultado do upload:', result);\r\n            return result;\r\n          }\r\n          console.log('Nenhum arquivo para upload ou ID da pessoa ausente');\r\n          return null;\r\n        },\r\n        hasSelectedFile: () => {\r\n          const hasFile = !!selectedFile;\r\n          console.log('Verificando se há arquivo selecionado:', hasFile);\r\n          return hasFile;\r\n        }\r\n      };\r\n    }\r\n  }, [selectedFile, personId, uploadRef]);\r\n\r\n  const handleUploadClick = () => {\r\n    fileInputRef.current.click();\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    console.log('Arquivo selecionado');\r\n    const file = e.target.files[0];\r\n    if (!file) {\r\n      console.log('Nenhum arquivo selecionado');\r\n      return;\r\n    }\r\n    console.log('Arquivo:', file.name, file.type, file.size);\r\n\r\n    // Validar tipo de arquivo\r\n    if (!file.type.startsWith('image/')) {\r\n      toast_error({\r\n        title: 'Erro',\r\n        message: 'Por favor, selecione uma imagem válida'\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Validar tamanho do arquivo (2MB)\r\n    if (file.size > 2 * 1024 * 1024) {\r\n      toast_error({\r\n        title: 'Erro',\r\n        message: 'A imagem deve ter no máximo 2MB'\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Armazenar o arquivo selecionado\r\n    setSelectedFile(file);\r\n\r\n    // Criar URL de pré-visualização\r\n    if (previewUrl) {\r\n      URL.revokeObjectURL(previewUrl);\r\n    }\r\n    const newPreviewUrl = URL.createObjectURL(file);\r\n    setPreviewUrl(newPreviewUrl);\r\n\r\n    // Se não estiver adiando o upload, fazer o upload imediatamente\r\n    if (!deferUpload && personId) {\r\n      uploadImage(file);\r\n    } else {\r\n      // Notificar o componente pai sobre a mudança de arquivo\r\n      if (onImageUploaded) {\r\n        onImageUploaded(null, file);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Método para fazer o upload da imagem\r\n  const uploadImage = async (file) => {\r\n    if (!file || !personId) {\r\n      console.error('Upload cancelado: arquivo ou personId ausente', { file: !!file, personId });\r\n      return null;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    console.log('Iniciando upload de imagem para pessoa ID:', personId);\r\n    console.log('Arquivo a ser enviado:', file.name, file.type, file.size);\r\n\r\n    try {\r\n      console.log('Chamando serviço de upload de imagem');\r\n      const response = await personsService.uploadProfileImage(personId, file);\r\n      console.log('Upload de imagem concluído com sucesso');\r\n      console.log('Resposta completa:', JSON.stringify(response));\r\n\r\n      // Atualizar URL da imagem com timestamp para evitar cache\r\n      const timestamp = new Date().getTime();\r\n\r\n      // Usar a URL completa retornada pelo servidor\r\n      const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;\r\n      console.log('Nova URL da imagem:', newImageUrl);\r\n\r\n      setImageUrl(newImageUrl);\r\n      setSelectedFile(null); // Limpar o arquivo selecionado após o upload\r\n\r\n      if (onImageUploaded) {\r\n        onImageUploaded(newImageUrl);\r\n      }\r\n\r\n      toast_success({\r\n        title: 'Sucesso',\r\n        message: 'Imagem de perfil atualizada com sucesso'\r\n      });\r\n\r\n      return newImageUrl;\r\n    } catch (error) {\r\n      console.error('Erro ao fazer upload da imagem:', error);\r\n      toast_error({\r\n        title: 'Erro',\r\n        message: 'Erro ao fazer upload da imagem. Tente novamente.'\r\n      });\r\n      return null;\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleRemoveImage = async () => {\r\n    if (!imageUrl && !previewUrl && !selectedFile) return;\r\n\r\n    if (!confirm('Tem certeza que deseja remover a imagem de perfil?')) {\r\n      return;\r\n    }\r\n\r\n    // Se estiver apenas em pré-visualização (ainda não foi feito upload)\r\n    if (previewUrl && !imageUrl) {\r\n      // Limpar a pré-visualização\r\n      URL.revokeObjectURL(previewUrl);\r\n      setPreviewUrl(null);\r\n      setSelectedFile(null);\r\n\r\n      if (onImageUploaded) {\r\n        onImageUploaded(null, null);\r\n      }\r\n\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n\r\n    try {\r\n      // Enviar uma imagem vazia para remover a atual\r\n      // Criar um arquivo vazio (1x1 pixel transparente)\r\n      const emptyBlob = new Blob([''], { type: 'image/png' });\r\n      await personsService.uploadProfileImage(personId, new File([emptyBlob], 'empty.png', { type: 'image/png' }));\r\n\r\n      setImageUrl(null);\r\n      setPreviewUrl(null);\r\n      setSelectedFile(null);\r\n\r\n      if (onImageUploaded) {\r\n        onImageUploaded(null, null);\r\n      }\r\n\r\n      toast_success({\r\n        title: 'Sucesso',\r\n        message: 'Imagem de perfil removida com sucesso'\r\n      });\r\n    } catch (error) {\r\n      console.error('Erro ao remover a imagem:', error);\r\n      toast_error({\r\n        title: 'Erro',\r\n        message: 'Erro ao remover a imagem. Tente novamente.'\r\n      });\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center w-full\">\r\n      <div className=\"relative mb-4\">\r\n        <div\r\n          className={`w-32 h-32 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${!showPreviewOnly ? 'cursor-pointer' : ''}`}\r\n          onClick={!showPreviewOnly ? handleUploadClick : undefined}\r\n        >\r\n          {isUploading ? (\r\n            <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white\"></div>\r\n            </div>\r\n          ) : null}\r\n\r\n          {imageUrl ? (\r\n            <>\r\n              <img\r\n                src={imageUrl}\r\n                alt=\"Foto de perfil\"\r\n                className=\"w-full h-full object-cover\"\r\n                onError={(e) => {\r\n                  console.error('Erro ao carregar imagem:', imageUrl);\r\n                  e.target.onerror = null;\r\n                  e.target.src = '';\r\n                  e.target.classList.add('hidden');\r\n                  setImageUrl(null);\r\n                }}\r\n              />\r\n              {/* Exibir URL da imagem para depuração */}\r\n              <div className=\"hidden\">{imageUrl}</div>\r\n            </>\r\n          ) : previewUrl ? (\r\n            <img\r\n              src={previewUrl}\r\n              alt=\"Pré-visualização\"\r\n              className=\"w-full h-full object-cover\"\r\n            />\r\n          ) : (\r\n            <User className=\"w-16 h-16 text-gray-400 dark:text-gray-500\" />\r\n          )}\r\n        </div>\r\n\r\n        {/* Botão de remoção absoluto - apenas se não estiver no modo de pré-visualização */}\r\n        {(imageUrl || previewUrl) && !showPreviewOnly && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleRemoveImage}\r\n            className=\"absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors\"\r\n            disabled={isUploading}\r\n          >\r\n            <X className=\"w-4 h-4\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      <input\r\n        type=\"file\"\r\n        ref={fileInputRef}\r\n        onChange={handleFileChange}\r\n        accept=\"image/*\"\r\n        className=\"hidden\"\r\n      />\r\n\r\n      {/* Botões de ação - apenas se não estiver no modo de pré-visualização */}\r\n      {!showPreviewOnly && (\r\n        <button\r\n          type=\"button\"\r\n          onClick={handleUploadClick}\r\n          className=\"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50\"\r\n          disabled={isUploading}\r\n        >\r\n          <Upload className=\"w-4 h-4\" />\r\n          {imageUrl || previewUrl ? 'Alterar foto' : 'Adicionar foto'}\r\n        </button>\r\n      )}\r\n\r\n      {/* Mensagens de status - apenas se não estiver no modo de pré-visualização */}\r\n      {!showPreviewOnly && (\r\n        <div className=\"h-12 flex items-center justify-center\">\r\n          {isUploading && (\r\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              Enviando imagem...\r\n            </p>\r\n          )}\r\n\r\n          {deferUpload && selectedFile && !isUploading && (\r\n            <p className=\"text-sm text-green-500 dark:text-green-400 text-center\">\r\n              Imagem selecionada\r\n          </p>\r\n        )}\r\n      </div>\r\n      )}\r\n\r\n      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n        Tamanho máximo: 2MB\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileImageUpload;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAHA;AAAA;AAAA;;;;;;;AAKA,MAAM,qBAAqB,CAAC,EAC1B,QAAQ,EACR,eAAe,EACf,kBAAkB,IAAI,EACtB,cAAc,KAAK,EACnB,YAAY,IAAI,EAChB,kBAAkB,KAAK,EACvB,cAAc,IAAI,EACnB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE9C,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,0BAA0B;QACtC,YAAY;IACd,GAAG;QAAC;KAAgB;IAEpB,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,yCAAyC,YAAY,IAAI;YACrE,+CAA+C;YAC/C,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;YACd,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAY;IAEhB,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,YAAY;gBACd,IAAI,eAAe,CAAC;YACtB;QACF;IACF,GAAG;QAAC;KAAW;IAEf,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,UAAU,OAAO,GAAG;gBAClB,qBAAqB;oBACnB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,wBAAwB,eAAe,aAAa,IAAI,GAAG;oBACvE,QAAQ,GAAG,CAAC,iBAAiB;oBAE7B,IAAI,gBAAgB,UAAU;wBAC5B,QAAQ,GAAG,CAAC;wBACZ,MAAM,SAAS,MAAM,YAAY;wBACjC,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;gBACA,iBAAiB;oBACf,MAAM,UAAU,CAAC,CAAC;oBAClB,QAAQ,GAAG,CAAC,0CAA0C;oBACtD,OAAO;gBACT;YACF;QACF;IACF,GAAG;QAAC;QAAc;QAAU;KAAU;IAEtC,MAAM,oBAAoB;QACxB,aAAa,OAAO,CAAC,KAAK;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,QAAQ,GAAG,CAAC,YAAY,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAEvD,0BAA0B;QAC1B,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,kCAAkC;QAClC,gBAAgB;QAEhB,gCAAgC;QAChC,IAAI,YAAY;YACd,IAAI,eAAe,CAAC;QACtB;QACA,MAAM,gBAAgB,IAAI,eAAe,CAAC;QAC1C,cAAc;QAEd,gEAAgE;QAChE,IAAI,CAAC,eAAe,UAAU;YAC5B,YAAY;QACd,OAAO;YACL,wDAAwD;YACxD,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;QACF;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB,QAAQ,KAAK,CAAC,iDAAiD;gBAAE,MAAM,CAAC,CAAC;gBAAM;YAAS;YACxF,OAAO;QACT;QAEA,eAAe;QACf,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,QAAQ,GAAG,CAAC,0BAA0B,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAErE,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;YACnE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAEjD,0DAA0D;YAC1D,MAAM,YAAY,IAAI,OAAO,OAAO;YAEpC,8CAA8C;YAC9C,MAAM,cAAc,SAAS,YAAY,GAAG,GAAG,SAAS,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG;YACxF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,YAAY;YACZ,gBAAgB,OAAO,6CAA6C;YAEpE,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;YAEA,cAAc;gBACZ,OAAO;gBACP,SAAS;YACX;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc;QAE/C,IAAI,CAAC,QAAQ,uDAAuD;YAClE;QACF;QAEA,qEAAqE;QACrE,IAAI,cAAc,CAAC,UAAU;YAC3B,4BAA4B;YAC5B,IAAI,eAAe,CAAC;YACpB,cAAc;YACd,gBAAgB;YAEhB,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;YAEA;QACF;QAEA,eAAe;QAEf,IAAI;YACF,+CAA+C;YAC/C,kDAAkD;YAClD,MAAM,YAAY,IAAI,KAAK;gBAAC;aAAG,EAAE;gBAAE,MAAM;YAAY;YACrD,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU,IAAI,KAAK;gBAAC;aAAU,EAAE,aAAa;gBAAE,MAAM;YAAY;YAEzG,YAAY;YACZ,cAAc;YACd,gBAAgB;YAEhB,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;YAEA,cAAc;gBACZ,OAAO;gBACP,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,qGAAqG,EAAE,CAAC,kBAAkB,mBAAmB,IAAI;wBAC7J,SAAS,CAAC,kBAAkB,oBAAoB;;4BAE/C,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;uCAEf;4BAEH,yBACC;;kDACE,8OAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;wCACV,SAAS,CAAC;4CACR,QAAQ,KAAK,CAAC,4BAA4B;4CAC1C,EAAE,MAAM,CAAC,OAAO,GAAG;4CACnB,EAAE,MAAM,CAAC,GAAG,GAAG;4CACf,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;4CACvB,YAAY;wCACd;;;;;;kDAGF,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;+CAEzB,2BACF,8OAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;qDAGZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;oBAKnB,CAAC,YAAY,UAAU,KAAK,CAAC,iCAC5B,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,MAAK;gBACL,KAAK;gBACL,UAAU;gBACV,QAAO;gBACP,WAAU;;;;;;YAIX,CAAC,iCACA,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,YAAY,aAAa,iBAAiB;;;;;;;YAK9C,CAAC,iCACA,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;oBAKzD,eAAe,gBAAgB,CAAC,6BAC/B,8OAAC;wBAAE,WAAU;kCAAyD;;;;;;;;;;;;0BAO5E,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;;;;;;;AAK9D;uCAEe"}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/common/MaskedInput.js"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\n/**\n * Componente genérico para campos de entrada com máscara\n * \n * @param {Object} props - Propriedades do componente\n * @param {string} props.type - Tipo de máscara: 'cpf', 'cnpj', 'phone', 'cep', ou 'custom'\n * @param {string} props.value - Valor atual do campo\n * @param {Function} props.onChange - Função chamada quando o valor muda\n * @param {string} props.name - Nome do campo\n * @param {string} props.id - ID do campo\n * @param {string} props.placeholder - Texto de placeholder\n * @param {string} props.className - Classes CSS adicionais\n * @param {boolean} props.disabled - Se o campo está desabilitado\n * @param {string} props.customMask - Máscara personalizada (quando type='custom')\n * @param {Object} props.inputProps - Propriedades adicionais para o input\n */\nconst MaskedInput = ({\n  type = 'text',\n  value = '',\n  onChange,\n  name,\n  id,\n  placeholder,\n  className = '',\n  disabled = false,\n  customMask,\n  ...inputProps\n}) => {\n  // Estado interno para controlar o valor formatado\n  const [inputValue, setInputValue] = useState('');\n\n  // Atualiza o estado interno quando o valor externo muda\n  useEffect(() => {\n    if (value !== undefined) {\n      setInputValue(formatValue(value, type, customMask));\n    }\n  }, [value, type, customMask]);\n\n  // Função para aplicar a máscara ao valor\n  const applyMask = (value, mask) => {\n    let maskedValue = '';\n    let valueIndex = 0;\n\n    for (let i = 0; i < mask.length && valueIndex < value.length; i++) {\n      const maskChar = mask[i];\n      const valueChar = value[valueIndex];\n\n      if (maskChar === '#') {\n        // Apenas dígitos\n        if (/\\d/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else if (maskChar === 'A') {\n        // Apenas letras\n        if (/[a-zA-Z]/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else if (maskChar === 'S') {\n        // Letras ou dígitos\n        if (/[a-zA-Z0-9]/.test(valueChar)) {\n          maskedValue += valueChar;\n          valueIndex++;\n        } else {\n          valueIndex++;\n          i--;\n        }\n      } else {\n        // Caracteres especiais da máscara\n        maskedValue += maskChar;\n        \n        // Se o caractere do valor for igual ao caractere da máscara, avança\n        if (valueChar === maskChar) {\n          valueIndex++;\n        }\n      }\n    }\n\n    return maskedValue;\n  };\n\n  // Função para obter a máscara com base no tipo\n  const getMask = (type) => {\n    switch (type) {\n      case 'cpf':\n        return '###.###.###-##';\n      case 'cnpj':\n        return '##.###.###/####-##';\n      case 'phone':\n        return '(##) #####-####';\n      case 'cep':\n        return '#####-###';\n      case 'custom':\n        return customMask || '';\n      default:\n        return '';\n    }\n  };\n\n  // Função para formatar o valor com base no tipo\n  const formatValue = (value, type, customMask) => {\n    if (!value) return '';\n    \n    // Remove caracteres não numéricos para tipos numéricos\n    let cleanValue = value;\n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\n      cleanValue = value.replace(/\\D/g, '');\n    }\n    \n    const mask = getMask(type);\n    return applyMask(cleanValue, mask);\n  };\n\n  // Função para remover a máscara e obter apenas os dígitos\n  const unformatValue = (value) => {\n    if (!value) return '';\n    \n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\n      return value.replace(/\\D/g, '');\n    }\n    \n    return value;\n  };\n\n  // Manipulador de mudança de valor\n  const handleChange = (e) => {\n    const newValue = e.target.value;\n    const formattedValue = formatValue(newValue, type, customMask);\n    \n    setInputValue(formattedValue);\n    \n    if (onChange) {\n      // Cria um evento sintético com o valor formatado\n      const syntheticEvent = {\n        ...e,\n        target: {\n          ...e.target,\n          name: name,\n          value: formattedValue,\n          rawValue: unformatValue(formattedValue)\n        }\n      };\n      \n      onChange(syntheticEvent);\n    }\n  };\n\n  return (\n    <input\n      type=\"text\"\n      id={id}\n      name={name}\n      value={inputValue}\n      onChange={handleChange}\n      placeholder={placeholder}\n      className={className}\n      disabled={disabled}\n      {...inputProps}\n    />\n  );\n};\n\nexport default MaskedInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA;;;;;;;;;;;;;;CAcC,GACD,MAAM,cAAc,CAAC,EACnB,OAAO,MAAM,EACb,QAAQ,EAAE,EACV,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,WAAW,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,EACV,GAAG,YACJ;IACC,kDAAkD;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,cAAc,YAAY,OAAO,MAAM;QACzC;IACF,GAAG;QAAC;QAAO;QAAM;KAAW;IAE5B,yCAAyC;IACzC,MAAM,YAAY,CAAC,OAAO;QACxB,IAAI,cAAc;QAClB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,aAAa,MAAM,MAAM,EAAE,IAAK;YACjE,MAAM,WAAW,IAAI,CAAC,EAAE;YACxB,MAAM,YAAY,KAAK,CAAC,WAAW;YAEnC,IAAI,aAAa,KAAK;gBACpB,iBAAiB;gBACjB,IAAI,KAAK,IAAI,CAAC,YAAY;oBACxB,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,gBAAgB;gBAChB,IAAI,WAAW,IAAI,CAAC,YAAY;oBAC9B,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,oBAAoB;gBACpB,IAAI,cAAc,IAAI,CAAC,YAAY;oBACjC,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO;gBACL,kCAAkC;gBAClC,eAAe;gBAEf,oEAAoE;gBACpE,IAAI,cAAc,UAAU;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,cAAc;YACvB;gBACE,OAAO;QACX;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc,CAAC,OAAO,MAAM;QAChC,IAAI,CAAC,OAAO,OAAO;QAEnB,uDAAuD;QACvD,IAAI,aAAa;QACjB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,aAAa,MAAM,OAAO,CAAC,OAAO;QACpC;QAEA,MAAM,OAAO,QAAQ;QACrB,OAAO,UAAU,YAAY;IAC/B;IAEA,0DAA0D;IAC1D,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,OAAO,MAAM,OAAO,CAAC,OAAO;QAC9B;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,MAAM,iBAAiB,YAAY,UAAU,MAAM;QAEnD,cAAc;QAEd,IAAI,UAAU;YACZ,iDAAiD;YACjD,MAAM,iBAAiB;gBACrB,GAAG,CAAC;gBACJ,QAAQ;oBACN,GAAG,EAAE,MAAM;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU,cAAc;gBAC1B;YACF;YAEA,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACT,GAAG,UAAU;;;;;;AAGpB;uCAEe"}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/common/AddressForm.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { MapPin, Search, Loader2 } from 'lucide-react';\r\nimport { useCep } from '@/hooks/useCep';\r\nimport MaskedInput from './MaskedInput';\r\nimport { ModuleInput, ModuleMaskedInput } from '@/components/ui';\r\n\r\n/**\r\n * Componente reutilizável para formulários de endereço com busca automática por CEP\r\n *\r\n * @param {Object} props - Propriedades do componente\r\n * @param {Object} props.formData - Dados do formulário\r\n * @param {Function} props.setFormData - Função para atualizar os dados do formulário\r\n * @param {Object} props.errors - Erros de validação\r\n * @param {boolean} props.isLoading - Indica se o formulário está em carregamento\r\n * @param {Object} props.fieldMapping - Mapeamento dos campos do endereço para os campos do formulário\r\n * @param {string} props.prefix - Prefixo para campos aninhados (ex: \"person.\")\r\n * @param {Object} props.classes - Classes CSS personalizadas\r\n * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)\r\n */\r\nconst AddressForm = ({\r\n  formData,\r\n  setFormData,\r\n  errors = {},\r\n  isLoading = false,\r\n  fieldMapping = {},\r\n  prefix = '',\r\n  classes = {},\r\n  moduleColor = 'default'\r\n}) => {\r\n  // Hook para busca de CEP\r\n  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();\r\n\r\n  // Classes CSS padrão\r\n  const defaultClasses = {\r\n    label: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',\r\n    input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white',\r\n    error: 'text-sm text-red-600 dark:text-red-400 mt-1',\r\n    iconContainer: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none',\r\n    button: 'absolute inset-y-0 right-0 px-3 flex items-center bg-primary-500 hover:bg-primary-600 text-white rounded-r-md transition-colors'\r\n  };\r\n\r\n  // Mescla as classes padrão com as classes personalizadas\r\n  const mergedClasses = {\r\n    label: classes.label || defaultClasses.label,\r\n    input: classes.input || defaultClasses.input,\r\n    error: classes.error || defaultClasses.error,\r\n    iconContainer: classes.iconContainer || defaultClasses.iconContainer,\r\n    button: classes.button || defaultClasses.button\r\n  };\r\n\r\n  // Mapeamento padrão dos campos\r\n  const defaultMapping = {\r\n    cep: `${prefix}postalCode`,\r\n    logradouro: `${prefix}address`,\r\n    bairro: `${prefix}neighborhood`,\r\n    localidade: `${prefix}city`,\r\n    uf: `${prefix}state`\r\n  };\r\n\r\n  // Log para debug\r\n  console.log('Prefix:', prefix);\r\n  console.log('Default mapping:', defaultMapping);\r\n\r\n  // Mescla o mapeamento padrão com o mapeamento personalizado\r\n  const mergedMapping = { ...defaultMapping, ...fieldMapping };\r\n  console.log('Mapeamento mesclado:', mergedMapping);\r\n\r\n  // Função para lidar com a mudança de valores nos campos\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log(`Alterando campo: ${name} para valor: ${value}`);\r\n\r\n    // Suporte para campos aninhados (ex: \"person.address\")\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}. Estado atual:`, formData[parent]);\r\n\r\n      const newFormData = {\r\n        ...formData,\r\n        [parent]: {\r\n          ...formData[parent],\r\n          [child]: value\r\n        }\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    } else {\r\n      const newFormData = {\r\n        ...formData,\r\n        [name]: value\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    }\r\n  };\r\n\r\n  // Função para buscar endereço pelo CEP\r\n  const handleCepSearch = async () => {\r\n    // Obtém o valor do CEP do campo correspondente\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    console.log('Buscando endereço para o CEP:', cepValue);\r\n\r\n    if (cepValue) {\r\n      try {\r\n        const result = await searchAddressByCep(cepValue, setFormData, mergedMapping);\r\n        console.log('Resultado da busca de CEP:', result);\r\n\r\n        // Forçar a atualização do formulário com os dados recebidos\r\n        if (result) {\r\n          console.log('Dados de endereço recebidos com sucesso, atualizando formulário');\r\n\r\n          // Criar um objeto com os campos mapeados\r\n          const updatedFields = {};\r\n\r\n          // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário\r\n          Object.keys(result).forEach(apiField => {\r\n            const formField = mergedMapping[apiField];\r\n            if (formField && result[apiField]) {\r\n              updatedFields[formField] = result[apiField];\r\n            }\r\n          });\r\n\r\n          console.log('Campos a serem atualizados:', updatedFields);\r\n\r\n          // Atualizar o formulário com os dados do endereço\r\n          setFormData(prevData => ({\r\n            ...prevData,\r\n            ...updatedFields\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro ao buscar CEP:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Efeito para buscar endereço automaticamente quando o CEP for preenchido completamente\r\n  useEffect(() => {\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    // Formatar o CEP se necessário\r\n    if (cepValue && !cepValue.includes('-') && cepValue.replace(/\\D/g, '').length === 8) {\r\n      // Formatar o CEP no formato 00000-000\r\n      const cleanCep = cepValue.replace(/\\D/g, '');\r\n      const formattedCep = cleanCep.replace(/(\\d{5})(\\d{3})/, '$1-$2');\r\n\r\n      // Atualizar o formData com o CEP formatado\r\n      if (cepField.includes('.')) {\r\n        const [parent, child] = cepField.split('.');\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [parent]: {\r\n            ...prev[parent],\r\n            [child]: formattedCep\r\n          }\r\n        }));\r\n      } else {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [cepField]: formattedCep\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Se o CEP tiver 8 dígitos (sem contar a máscara), busca o endereço\r\n    if (cepValue && cepValue.replace(/\\D/g, '').length === 8) {\r\n      handleCepSearch();\r\n    }\r\n  }, [formData[mergedMapping.cep]]);\r\n\r\n  // Função para obter o valor de um campo, considerando campos aninhados\r\n  const getFieldValue = (fieldName) => {\r\n    console.log(`Obtendo valor para o campo: ${fieldName}`);\r\n\r\n    if (fieldName.includes('.')) {\r\n      const [parent, child] = fieldName.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}, valor:`, formData[parent]?.[child]);\r\n      return formData[parent]?.[child] || '';\r\n    }\r\n\r\n    console.log(`Campo simples: ${fieldName}, valor:`, formData[fieldName]);\r\n    return formData[fieldName] || '';\r\n  };\r\n\r\n  // Função para verificar se um campo tem erro\r\n  const hasError = (fieldName) => {\r\n    return errors[fieldName] ? true : false;\r\n  };\r\n\r\n  // Função para obter a mensagem de erro de um campo\r\n  const getErrorMessage = (fieldName) => {\r\n    return errors[fieldName] || '';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Seção de Endereço */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        {/* CEP com botão de busca */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.cep}>\r\n            CEP\r\n          </label>\r\n          <div className=\"relative\">\r\n            <div className={mergedClasses.iconContainer}>\r\n              <MapPin className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n            </div>\r\n            {moduleColor ? (\r\n              <div className=\"relative\">\r\n                <ModuleMaskedInput\r\n                  moduleColor={moduleColor}\r\n                  mask=\"99999-999\"\r\n                  replacement={{ 9: /[0-9]/ }}\r\n                  id={mergedMapping.cep}\r\n                  name={mergedMapping.cep}\r\n                  value={getFieldValue(mergedMapping.cep)}\r\n                  onChange={handleChange}\r\n                  placeholder=\"00000-000\"\r\n                  className=\"pl-10 pr-12\"\r\n                  disabled={isLoading || isCepLoading}\r\n                  error={hasError(mergedMapping.cep)}\r\n                />\r\n              </div>\r\n            ) : (\r\n              <MaskedInput\r\n                type=\"cep\"\r\n                id={mergedMapping.cep}\r\n                name={mergedMapping.cep}\r\n                value={getFieldValue(mergedMapping.cep)}\r\n                onChange={handleChange}\r\n                placeholder=\"00000-000\"\r\n                className={`${mergedClasses.input} pl-10 pr-12 ${hasError(mergedMapping.cep) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n                disabled={isLoading || isCepLoading}\r\n              />\r\n            )}\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCepSearch}\r\n              className={mergedClasses.button}\r\n              disabled={isLoading || isCepLoading}\r\n              aria-label=\"Buscar CEP\"\r\n            >\r\n              {isCepLoading ? (\r\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n              ) : (\r\n                <Search className=\"h-5 w-5\" />\r\n              )}\r\n            </button>\r\n          </div>\r\n          {hasError(mergedMapping.cep) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.cep)}</p>\r\n          )}\r\n          {cepError && (\r\n            <p className={mergedClasses.error}>{cepError}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Estado */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.uf}>\r\n            Estado\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n              error={hasError(mergedMapping.uf)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.uf) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.uf) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.uf)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Cidade */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.localidade}>\r\n            Cidade\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.localidade)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.localidade) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.localidade) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.localidade)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Bairro */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.bairro}>\r\n            Bairro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.bairro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.bairro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.bairro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.bairro)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Endereço (logradouro) */}\r\n        <div className=\"md:col-span-2\">\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.logradouro}>\r\n            Logradouro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.logradouro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.logradouro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.logradouro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.logradouro)}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddressForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;AAGA;AAHA;AAAA;AAGA;AANA;;;;;;;AAQA;;;;;;;;;;;;CAYC,GACD,MAAM,cAAc,CAAC,EACnB,QAAQ,EACR,WAAW,EACX,SAAS,CAAC,CAAC,EACX,YAAY,KAAK,EACjB,eAAe,CAAC,CAAC,EACjB,SAAS,EAAE,EACX,UAAU,CAAC,CAAC,EACZ,cAAc,SAAS,EACxB;IACC,yBAAyB;IACzB,MAAM,EAAE,kBAAkB,EAAE,WAAW,YAAY,EAAE,OAAO,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD;IAE9E,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,OAAO;QACP,OAAO;QACP,OAAO;QACP,eAAe;QACf,QAAQ;IACV;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QACpB,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,eAAe,QAAQ,aAAa,IAAI,eAAe,aAAa;QACpE,QAAQ,QAAQ,MAAM,IAAI,eAAe,MAAM;IACjD;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,KAAK,GAAG,OAAO,UAAU,CAAC;QAC1B,YAAY,GAAG,OAAO,OAAO,CAAC;QAC9B,QAAQ,GAAG,OAAO,YAAY,CAAC;QAC/B,YAAY,GAAG,OAAO,IAAI,CAAC;QAC3B,IAAI,GAAG,OAAO,KAAK,CAAC;IACtB;IAEA,iBAAiB;IACjB,QAAQ,GAAG,CAAC,WAAW;IACvB,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,4DAA4D;IAC5D,MAAM,gBAAgB;QAAE,GAAG,cAAc;QAAE,GAAG,YAAY;IAAC;IAC3D,QAAQ,GAAG,CAAC,wBAAwB;IAEpC,wDAAwD;IACxD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,aAAa,EAAE,OAAO;QAE3D,uDAAuD;QACvD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC;YACnC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,eAAe,CAAC,EAAE,QAAQ,CAAC,OAAO;YAEjF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,OAAO,EAAE;oBACR,GAAG,QAAQ,CAAC,OAAO;oBACnB,CAAC,MAAM,EAAE;gBACX;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd,OAAO;YACL,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB;QACtB,+CAA+C;QAC/C,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,IAAI,UAAU;YACZ,IAAI;gBACF,MAAM,SAAS,MAAM,mBAAmB,UAAU,aAAa;gBAC/D,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,4DAA4D;gBAC5D,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC;oBAEZ,yCAAyC;oBACzC,MAAM,gBAAgB,CAAC;oBAEvB,uFAAuF;oBACvF,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;wBAC1B,MAAM,YAAY,aAAa,CAAC,SAAS;wBACzC,IAAI,aAAa,MAAM,CAAC,SAAS,EAAE;4BACjC,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS;wBAC7C;oBACF;oBAEA,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,kDAAkD;oBAClD,YAAY,CAAA,WAAY,CAAC;4BACvB,GAAG,QAAQ;4BACX,GAAG,aAAa;wBAClB,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACvC;QACF;IACF;IAEA,wFAAwF;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,+BAA+B;QAC/B,IAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,QAAQ,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACnF,sCAAsC;YACtC,MAAM,WAAW,SAAS,OAAO,CAAC,OAAO;YACzC,MAAM,eAAe,SAAS,OAAO,CAAC,kBAAkB;YAExD,2CAA2C;YAC3C,IAAI,SAAS,QAAQ,CAAC,MAAM;gBAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;gBACvC,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,GAAG,IAAI,CAAC,OAAO;4BACf,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YACH,OAAO;gBACL,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,SAAS,EAAE;oBACd,CAAC;YACH;QACF;QAEA,oEAAoE;QACpE,IAAI,YAAY,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACxD;QACF;IACF,GAAG;QAAC,QAAQ,CAAC,cAAc,GAAG,CAAC;KAAC;IAEhC,uEAAuE;IACvE,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW;QAEtD,IAAI,UAAU,QAAQ,CAAC,MAAM;YAC3B,MAAM,CAAC,QAAQ,MAAM,GAAG,UAAU,KAAK,CAAC;YACxC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;YACnF,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,IAAI;QACtC;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,QAAQ,CAAC,EAAE,QAAQ,CAAC,UAAU;QACtE,OAAO,QAAQ,CAAC,UAAU,IAAI;IAChC;IAEA,6CAA6C;IAC7C,MAAM,WAAW,CAAC;QAChB,OAAO,MAAM,CAAC,UAAU,GAAG,OAAO;IACpC;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,CAAC,UAAU,IAAI;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,GAAG;sCAAE;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,cAAc,aAAa;8CACzC,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;gCAEnB,4BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4LAAA,CAAA,oBAAiB;wCAChB,aAAa;wCACb,MAAK;wCACL,aAAa;4CAAE,GAAG;wCAAQ;wCAC1B,IAAI,cAAc,GAAG;wCACrB,MAAM,cAAc,GAAG;wCACvB,OAAO,cAAc,cAAc,GAAG;wCACtC,UAAU;wCACV,aAAY;wCACZ,WAAU;wCACV,UAAU,aAAa;wCACvB,OAAO,SAAS,cAAc,GAAG;;;;;;;;;;yDAIrC,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,IAAI,cAAc,GAAG;oCACrB,MAAM,cAAc,GAAG;oCACvB,OAAO,cAAc,cAAc,GAAG;oCACtC,UAAU;oCACV,aAAY;oCACZ,WAAW,GAAG,cAAc,KAAK,CAAC,aAAa,EAAE,SAAS,cAAc,GAAG,IAAI,uCAAuC,IAAI;oCAC1H,UAAU,aAAa;;;;;;8CAG3B,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,cAAc,MAAM;oCAC/B,UAAU,aAAa;oCACvB,cAAW;8CAEV,6BACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIvB,SAAS,cAAc,GAAG,mBACzB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,GAAG;;;;;;wBAEtE,0BACC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG;;;;;;;;;;;;8BAKxC,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,EAAE;sCAAE;;;;;;wBAGjE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,WAAW;4BACX,OAAO,SAAS,cAAc,EAAE;;;;;iDAGlC,8OAAC;4BACC,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,EAAE,IAAI,uCAAuC,IAAI;4BAC7G,UAAU,aAAa;4BACvB,WAAW;;;;;;wBAGd,SAAS,cAAc,EAAE,mBACxB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,EAAE;;;;;;;;;;;;8BAKxE,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;8BAKhF,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,MAAM;sCAAE;;;;;;wBAGrE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,MAAM;;;;;iDAGtC,8OAAC;4BACC,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,MAAM,IAAI,uCAAuC,IAAI;4BACjH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,MAAM,mBAC5B,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,MAAM;;;;;;;;;;;;8BAK5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMxF;uCAEe"}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1227, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/PersonInfoTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { User, Mail, Phone, CreditCard, Calendar, MapPin, FileText, Users } from \"lucide-react\";\r\nimport { InputMask } from \"@react-input/mask\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport ProfileImageUpload from \"@/components/forms/ProfileImageUpload\";\r\nimport AddressForm from \"@/components/common/AddressForm\";\r\nimport { ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup, ModuleMaskedInput } from \"@/components/ui\";\r\n\r\nconst PersonInfoTab = ({\r\n  formData,\r\n  setFormData,\r\n  errors,\r\n  isLoading,\r\n  handleChange,\r\n  onSubmit,\r\n  personId,\r\n  profileImageUploadRef,\r\n  isCreating,\r\n  onSetTempProfileImage,\r\n  tempProfileImage\r\n}) => {\r\n  const [clients, setClients] = useState([]);\r\n  const [isLoadingClients, setIsLoadingClients] = useState(false);\r\n  const [selectedImageFile, setSelectedImageFile] = useState(null);\r\n  const [selectedClient, setSelectedClient] = useState(null);\r\n  const [isLoadingClientData, setIsLoadingClientData] = useState(false);\r\n\r\n  // Fetch clients for dropdown\r\n  useEffect(() => {\r\n    fetchClients();\r\n  }, []);\r\n\r\n  // Fetch client data when clientId changes\r\n  useEffect(() => {\r\n    if (formData.clientId) {\r\n      fetchClientData(formData.clientId);\r\n    } else {\r\n      setSelectedClient(null);\r\n    }\r\n  }, [formData.clientId]);\r\n\r\n  const fetchClients = async () => {\r\n    setIsLoadingClients(true);\r\n    try {\r\n      const clientsList = await personsService.getClientsForSelect();\r\n      setClients(clientsList);\r\n    } catch (error) {\r\n      console.error(\"Error fetching clients:\", error);\r\n    } finally {\r\n      setIsLoadingClients(false);\r\n    }\r\n  };\r\n\r\n  const fetchClientData = async (clientId) => {\r\n    if (!clientId) return;\r\n\r\n    setIsLoadingClientData(true);\r\n    try {\r\n      const clientData = await clientsService.getClient(clientId);\r\n      setSelectedClient(clientData);\r\n\r\n      // If checkboxes are checked, update the form data with client info\r\n      if (formData.useClientEmail && clientData.email) {\r\n        handleChange({\r\n          target: { name: \"email\", value: clientData.email }\r\n        });\r\n      }\r\n\r\n      // For phone, we need to get it from the client's person\r\n      if (formData.useClientPhone && clientData.persons && clientData.persons.length > 0) {\r\n        const personPhone = clientData.persons[0].phone;\r\n        if (personPhone) {\r\n          // Format phone number\r\n          const cleanPhone = personPhone.replace(/\\D/g, \"\");\r\n          const formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n\r\n          handleChange({\r\n            target: { name: \"phone\", value: formattedPhone }\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching client data:\", error);\r\n    } finally {\r\n      setIsLoadingClientData(false);\r\n    }\r\n  };\r\n\r\n  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo\r\n\r\n  // Simplificar o onSubmit para apenas chamar o onSubmit do componente pai\r\n  const handleSubmit = async () => {\r\n    console.log('Chamando onSubmit para salvar os dados da pessoa');\r\n    await onSubmit();\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>\r\n      <div className=\"flex flex-col md:flex-row gap-8 mb-6\">\r\n        {/* Upload de foto de perfil */}\r\n        <div className=\"flex-shrink-0 w-[200px] min-w-[200px]\">\r\n          {/* Exibir o componente de upload de imagem tanto para edição quanto para criação */}\r\n          <ProfileImageUpload\r\n            personId={personId}\r\n            initialImageUrl={formData.profileImageFullUrl}\r\n            deferUpload={true}\r\n            uploadRef={profileImageUploadRef}\r\n            onImageUploaded={(url, file) => {\r\n              console.log('Imagem selecionada ou URL recebida:', url ? 'URL' : 'Arquivo');\r\n\r\n              if (isCreating) {\r\n                // Se estiver criando uma nova pessoa, armazenar o arquivo para upload posterior\r\n                console.log('Armazenando arquivo de imagem temporário para nova pessoa');\r\n                onSetTempProfileImage && onSetTempProfileImage(file);\r\n              } else if (url) {\r\n                // Se estiver editando uma pessoa existente e receber uma URL\r\n                console.log('Nova URL da imagem recebida:', url);\r\n                handleChange({\r\n                  target: {\r\n                    name: 'profileImageFullUrl',\r\n                    value: url\r\n                  }\r\n                });\r\n              }\r\n\r\n              // Armazenar o arquivo selecionado para referência\r\n              setSelectedImageFile(file);\r\n              console.log('Arquivo de imagem armazenado:', file ? file.name : 'Nenhum');\r\n            }}\r\n            showPreviewOnly={isCreating}\r\n            previewFile={tempProfileImage}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex-grow\">\r\n          <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-3 mt-2\">\r\n            <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n              <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n              Dados Pessoais\r\n            </h3>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\r\n            {/* Nome completo */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Nome completo *\"\r\n              htmlFor=\"fullName\"\r\n              icon={<User size={16} />}\r\n              error={errors.fullName}\r\n              errorMessage={errors.fullName}\r\n              className=\"md:col-span-2\"\r\n            >\r\n              <ModuleInput\r\n                moduleColor=\"people\"\r\n                type=\"text\"\r\n                id=\"fullName\"\r\n                name=\"fullName\"\r\n                value={formData.fullName}\r\n                onChange={handleChange}\r\n                placeholder=\"Nome completo\"\r\n                disabled={isLoading}\r\n                error={!!errors.fullName}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* CPF */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"CPF\"\r\n              htmlFor=\"cpf\"\r\n              icon={<CreditCard size={16} />}\r\n              error={errors.cpf}\r\n              errorMessage={errors.cpf}\r\n            >\r\n              <ModuleMaskedInput\r\n                moduleColor=\"people\"\r\n                mask=\"999.999.999-99\"\r\n                replacement={{ 9: /[0-9]/ }}\r\n                value={formData.cpf}\r\n                onChange={(e) =>\r\n                  handleChange({\r\n                    target: { name: \"cpf\", value: e.target.value },\r\n                  })\r\n                }\r\n                placeholder=\"000.000.000-00\"\r\n                disabled={isLoading}\r\n                error={!!errors.cpf}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* Data de nascimento */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Data de Nascimento\"\r\n              htmlFor=\"birthDate\"\r\n              icon={<Calendar size={16} />}\r\n            >\r\n              <ModuleInput\r\n                moduleColor=\"people\"\r\n                type=\"date\"\r\n                id=\"birthDate\"\r\n                name=\"birthDate\"\r\n                value={formData.birthDate}\r\n                onChange={handleChange}\r\n                disabled={isLoading}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* Gênero */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Gênero\"\r\n              htmlFor=\"gender\"\r\n              icon={<User size={16} />}\r\n            >\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                id=\"gender\"\r\n                name=\"gender\"\r\n                value={formData.gender}\r\n                onChange={handleChange}\r\n                disabled={isLoading}\r\n              >\r\n                <option value=\"\">Selecione</option>\r\n                <option value=\"M\">Masculino</option>\r\n                <option value=\"F\">Feminino</option>\r\n                <option value=\"O\">Outro</option>\r\n              </ModuleSelect>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Email */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Email\"\r\n              htmlFor=\"email\"\r\n              icon={<Mail size={16} />}\r\n              error={errors.email}\r\n              errorMessage={errors.email}\r\n            >\r\n              <div className=\"space-y-2\">\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading || formData.useClientEmail}\r\n                  error={!!errors.email}\r\n                />\r\n                {formData.clientId && (\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"useClientEmail\"\r\n                      name=\"useClientEmail\"\r\n                      checked={formData.useClientEmail}\r\n                      onChange={(e) => {\r\n                        const isChecked = e.target.checked;\r\n                        handleChange({\r\n                          target: { name: \"useClientEmail\", value: isChecked }\r\n                        });\r\n\r\n                        // If checked and we have client data, update email\r\n                        if (isChecked && selectedClient?.email) {\r\n                          handleChange({\r\n                            target: { name: \"email\", value: selectedClient.email }\r\n                          });\r\n                        }\r\n                      }}\r\n                      disabled={isLoading || isLoadingClientData || !selectedClient}\r\n                      className=\"h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark\"\r\n                    />\r\n                    <label htmlFor=\"useClientEmail\" className=\"ml-2 text-sm text-gray-600 dark:text-gray-300\">\r\n                      Usar email do cliente\r\n                    </label>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Telefone */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Telefone\"\r\n              htmlFor=\"phone\"\r\n              icon={<Phone size={16} />}\r\n            >\r\n              <div className=\"space-y-2\">\r\n                <ModuleMaskedInput\r\n                  moduleColor=\"people\"\r\n                  mask=\"(99) 99999-9999\"\r\n                  replacement={{ 9: /[0-9]/ }}\r\n                  value={formData.phone}\r\n                  onChange={(e) =>\r\n                    handleChange({\r\n                      target: { name: \"phone\", value: e.target.value },\r\n                    })\r\n                  }\r\n                  placeholder=\"(00) 00000-0000\"\r\n                  disabled={isLoading || formData.useClientPhone}\r\n                />\r\n                {formData.clientId && (\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"useClientPhone\"\r\n                      name=\"useClientPhone\"\r\n                      checked={formData.useClientPhone}\r\n                      onChange={(e) => {\r\n                        const isChecked = e.target.checked;\r\n                        handleChange({\r\n                          target: { name: \"useClientPhone\", value: isChecked }\r\n                        });\r\n\r\n                        // If checked and we have client data with persons, update phone\r\n                        if (isChecked && selectedClient?.persons && selectedClient.persons.length > 0) {\r\n                          const personPhone = selectedClient.persons[0].phone;\r\n                          if (personPhone) {\r\n                            // Format phone number\r\n                            const cleanPhone = personPhone.replace(/\\D/g, \"\");\r\n                            const formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n\r\n                            handleChange({\r\n                              target: { name: \"phone\", value: formattedPhone }\r\n                            });\r\n                          }\r\n                        }\r\n                      }}\r\n                      disabled={isLoading || isLoadingClientData || !selectedClient || !selectedClient.persons || selectedClient.persons.length === 0}\r\n                      className=\"h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark\"\r\n                    />\r\n                    <label htmlFor=\"useClientPhone\" className=\"ml-2 text-sm text-gray-600 dark:text-gray-300\">\r\n                      Usar telefone do cliente\r\n                    </label>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Endereço */}\r\n            <div className=\"md:col-span-2\">\r\n              <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-3 mt-4\">\r\n                <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                  <MapPin className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                  Endereço\r\n                </h3>\r\n              </div>\r\n              <AddressForm\r\n                formData={formData}\r\n                setFormData={setFormData}\r\n                errors={errors}\r\n                isLoading={isLoading}\r\n                fieldMapping={{\r\n                  // Mapeamento personalizado para os campos da API ViaCEP\r\n                  logradouro: \"address\",\r\n                  bairro: \"neighborhood\",\r\n                  localidade: \"city\",\r\n                  uf: \"state\",\r\n                  cep: \"postalCode\"\r\n                }}\r\n                moduleColor=\"people\"\r\n                classes={{\r\n                  label: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\r\n                  input: \"block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\r\n                  error: \"mt-1 text-xs text-red-600 dark:text-red-400\",\r\n                  iconContainer: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n                }}\r\n              />\r\n        </div>\r\n\r\n        {/* Cliente */}\r\n        <ModuleFormGroup\r\n          moduleColor=\"people\"\r\n          label=\"Cliente\"\r\n          htmlFor=\"clientId\"\r\n          icon={<Users size={16} />}\r\n        >\r\n          <ModuleSelect\r\n            moduleColor=\"people\"\r\n            id=\"clientId\"\r\n            name=\"clientId\"\r\n            value={formData.clientId}\r\n            onChange={handleChange}\r\n            disabled={isLoading || isLoadingClients}\r\n          >\r\n            <option value=\"\">Selecione um cliente</option>\r\n            {isLoadingClients ? (\r\n              <option disabled>Carregando...</option>\r\n            ) : (\r\n              clients.map((client) => (\r\n                <option key={client.value} value={client.value}>\r\n                  {client.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </ModuleSelect>\r\n        </ModuleFormGroup>\r\n\r\n        {/* Relacionamento (somente se clientId estiver preenchido) */}\r\n        {formData.clientId && (\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Relacionamento *\"\r\n            htmlFor=\"relationship\"\r\n            icon={<User size={16} />}\r\n            error={errors.relationship}\r\n            errorMessage={errors.relationship}\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"relationship\"\r\n              name=\"relationship\"\r\n              value={formData.relationship}\r\n              onChange={handleChange}\r\n              disabled={isLoading}\r\n              error={!!errors.relationship}\r\n            >\r\n              <option value=\"\">Selecione</option>\r\n              <option value=\"Titular\">Titular</option>\r\n              <option value=\"Cônjuge\">Cônjuge</option>\r\n              <option value=\"Filho\">Filho</option>\r\n              <option value=\"Filha\">Filha</option>\r\n              <option value=\"Pai\">Pai</option>\r\n              <option value=\"Mãe\">Mãe</option>\r\n              <option value=\"Outro\">Outro</option>\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n        )}\r\n\r\n        {/* Observações */}\r\n        <ModuleFormGroup\r\n          moduleColor=\"people\"\r\n          label=\"Observações\"\r\n          htmlFor=\"notes\"\r\n          icon={<FileText size={16} />}\r\n          className=\"md:col-span-2\"\r\n        >\r\n          <ModuleTextarea\r\n            moduleColor=\"people\"\r\n            id=\"notes\"\r\n            name=\"notes\"\r\n            value={formData.notes}\r\n            onChange={handleChange}\r\n            placeholder=\"Observações adicionais\"\r\n            rows={3}\r\n            disabled={isLoading}\r\n          />\r\n        </ModuleFormGroup>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default PersonInfoTab;"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AANA;AAMA;AAAA;AANA;AAMA;AANA;AAMA;AANA;AAAA;AAAA;AAAA;AAAA;AAMA;AATA;;;;;;;;;;AAWA,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,qBAAqB,EACrB,UAAU,EACV,qBAAqB,EACrB,gBAAgB,EACjB;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ,EAAE;YACrB,gBAAgB,SAAS,QAAQ;QACnC,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC,SAAS,QAAQ;KAAC;IAEtB,MAAM,eAAe;QACnB,oBAAoB;QACpB,IAAI;YACF,MAAM,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,mBAAmB;YAC5D,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,UAAU;QAEf,uBAAuB;QACvB,IAAI;YACF,MAAM,aAAa,MAAM,6JAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YAClD,kBAAkB;YAElB,mEAAmE;YACnE,IAAI,SAAS,cAAc,IAAI,WAAW,KAAK,EAAE;gBAC/C,aAAa;oBACX,QAAQ;wBAAE,MAAM;wBAAS,OAAO,WAAW,KAAK;oBAAC;gBACnD;YACF;YAEA,wDAAwD;YACxD,IAAI,SAAS,cAAc,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBAClF,MAAM,cAAc,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;gBAC/C,IAAI,aAAa;oBACf,sBAAsB;oBACtB,MAAM,aAAa,YAAY,OAAO,CAAC,OAAO;oBAC9C,MAAM,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;oBAEnE,aAAa;wBACX,QAAQ;4BAAE,MAAM;4BAAS,OAAO;wBAAe;oBACjD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,6EAA6E;IAE7E,yEAAyE;IACzE,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,qBACE,8OAAC;QAAK,UAAU,CAAC;YAAQ,EAAE,cAAc;YAAI;QAAgB;kBAC3D,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,gJAAA,CAAA,UAAkB;wBACjB,UAAU;wBACV,iBAAiB,SAAS,mBAAmB;wBAC7C,aAAa;wBACb,WAAW;wBACX,iBAAiB,CAAC,KAAK;4BACrB,QAAQ,GAAG,CAAC,uCAAuC,MAAM,QAAQ;4BAEjE,IAAI,YAAY;gCACd,gFAAgF;gCAChF,QAAQ,GAAG,CAAC;gCACZ,yBAAyB,sBAAsB;4BACjD,OAAO,IAAI,KAAK;gCACd,6DAA6D;gCAC7D,QAAQ,GAAG,CAAC,gCAAgC;gCAC5C,aAAa;oCACX,QAAQ;wCACN,MAAM;wCACN,OAAO;oCACT;gCACF;4BACF;4BAEA,kDAAkD;4BAClD,qBAAqB;4BACrB,QAAQ,GAAG,CAAC,iCAAiC,OAAO,KAAK,IAAI,GAAG;wBAClE;wBACA,iBAAiB;wBACjB,aAAa;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAsE;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,QAAQ;oCACtB,cAAc,OAAO,QAAQ;oCAC7B,WAAU;8CAEV,cAAA,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,aAAY;wCACZ,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;;;;;;;;;;;8CAK5B,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACxB,OAAO,OAAO,GAAG;oCACjB,cAAc,OAAO,GAAG;8CAExB,cAAA,8OAAC,4LAAA,CAAA,oBAAiB;wCAChB,aAAY;wCACZ,MAAK;wCACL,aAAa;4CAAE,GAAG;wCAAQ;wCAC1B,OAAO,SAAS,GAAG;wCACnB,UAAU,CAAC,IACT,aAAa;gDACX,QAAQ;oDAAE,MAAM;oDAAO,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/C;wCAEF,aAAY;wCACZ,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,GAAG;;;;;;;;;;;8CAKvB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;8CAEtB,cAAA,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU;wCACV,UAAU;;;;;;;;;;;8CAKd,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;8CAElB,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,MAAM;wCACtB,UAAU;wCACV,UAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;0DAClB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;0DAClB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;;;;;;;;;;;;8CAKtB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,KAAK;oCACnB,cAAc,OAAO,KAAK;8CAE1B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gLAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,UAAU,aAAa,SAAS,cAAc;gDAC9C,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;4CAEtB,SAAS,QAAQ,kBAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,SAAS,cAAc;wDAChC,UAAU,CAAC;4DACT,MAAM,YAAY,EAAE,MAAM,CAAC,OAAO;4DAClC,aAAa;gEACX,QAAQ;oEAAE,MAAM;oEAAkB,OAAO;gEAAU;4DACrD;4DAEA,mDAAmD;4DACnD,IAAI,aAAa,gBAAgB,OAAO;gEACtC,aAAa;oEACX,QAAQ;wEAAE,MAAM;wEAAS,OAAO,eAAe,KAAK;oEAAC;gEACvD;4DACF;wDACF;wDACA,UAAU,aAAa,uBAAuB,CAAC;wDAC/C,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAiB,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;8CASlG,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;8CAEnB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4LAAA,CAAA,oBAAiB;gDAChB,aAAY;gDACZ,MAAK;gDACL,aAAa;oDAAE,GAAG;gDAAQ;gDAC1B,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,aAAa;wDACX,QAAQ;4DAAE,MAAM;4DAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjD;gDAEF,aAAY;gDACZ,UAAU,aAAa,SAAS,cAAc;;;;;;4CAE/C,SAAS,QAAQ,kBAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,SAAS,cAAc;wDAChC,UAAU,CAAC;4DACT,MAAM,YAAY,EAAE,MAAM,CAAC,OAAO;4DAClC,aAAa;gEACX,QAAQ;oEAAE,MAAM;oEAAkB,OAAO;gEAAU;4DACrD;4DAEA,gEAAgE;4DAChE,IAAI,aAAa,gBAAgB,WAAW,eAAe,OAAO,CAAC,MAAM,GAAG,GAAG;gEAC7E,MAAM,cAAc,eAAe,OAAO,CAAC,EAAE,CAAC,KAAK;gEACnD,IAAI,aAAa;oEACf,sBAAsB;oEACtB,MAAM,aAAa,YAAY,OAAO,CAAC,OAAO;oEAC9C,MAAM,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;oEAEnE,aAAa;wEACX,QAAQ;4EAAE,MAAM;4EAAS,OAAO;wEAAe;oEACjD;gEACF;4DACF;wDACF;wDACA,UAAU,aAAa,uBAAuB,CAAC,kBAAkB,CAAC,eAAe,OAAO,IAAI,eAAe,OAAO,CAAC,MAAM,KAAK;wDAC9H,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAiB,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;8CASlG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAsE;;;;;;;;;;;;sDAI5F,8OAAC,0IAAA,CAAA,UAAW;4CACV,UAAU;4CACV,aAAa;4CACb,QAAQ;4CACR,WAAW;4CACX,cAAc;gDACZ,wDAAwD;gDACxD,YAAY;gDACZ,QAAQ;gDACR,YAAY;gDACZ,IAAI;gDACJ,KAAK;4CACP;4CACA,aAAY;4CACZ,SAAS;gDACP,OAAO;gDACP,OAAO;gDACP,OAAO;gDACP,eAAe;4CACjB;;;;;;;;;;;;8CAKR,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;8CAEnB,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,UAAU,aAAa;;0DAEvB,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,iCACC,8OAAC;gDAAO,QAAQ;0DAAC;;;;;uDAEjB,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;;;;;;;gCAShC,SAAS,QAAQ,kBAChB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,YAAY;oCAC1B,cAAc,OAAO,YAAY;8CAEjC,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,YAAY;;0DAE5B,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;8CAM5B,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACtB,WAAU;8CAEV,cAAA,8OAAC,sLAAA,CAAA,iBAAc;wCACb,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,aAAY;wCACZ,MAAM;wCACN,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe"}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/DocumentsTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport {\r\n  FileText,\r\n  Upload,\r\n  Plus,\r\n  Loader2,\r\n  Trash,\r\n  Eye,\r\n  Download,\r\n  ChevronDown\r\n} from \"lucide-react\";\r\nimport { api } from \"@/utils/api\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\nconst DocumentsTab = ({ personId, onClose, isCreating, onAddTempDocument, tempDocuments = [] }) => {\r\n  const dropdownRef = useRef(null);\r\n  const buttonRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n\r\n  const [documents, setDocuments] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [needsSave, setNeedsSave] = useState(!personId);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [documentToDelete, setDocumentToDelete] = useState(null);\r\n\r\n  // Upload state\r\n  const [documentType, setDocumentType] = useState(\"\");\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadError, setUploadError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadDocuments();\r\n    } else if (isCreating && tempDocuments.length > 0) {\r\n      // Se estiver no modo de criação e houver documentos temporários, exibi-los\r\n      const formattedTempDocs = tempDocuments.map((doc, index) => ({\r\n        id: `temp-${index}`,\r\n        fileName: doc.file.name,\r\n        fileSize: doc.file.size,\r\n        fileType: doc.file.type,\r\n        documentType: doc.type,\r\n        createdAt: new Date().toISOString(),\r\n        isTemp: true\r\n      }));\r\n\r\n      setDocuments(formattedTempDocs);\r\n      setIsLoading(false);\r\n    } else {\r\n      setIsLoading(false);\r\n    }\r\n  }, [personId, isCreating, tempDocuments]);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 256) // Mínimo de 256px (w-64)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const loadDocuments = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = await api.get(\"/documents\", {\r\n        params: {\r\n          targetId: personId,\r\n          targetType: \"person\"\r\n        }\r\n      });\r\n      setDocuments(response.data || []);\r\n    } catch (err) {\r\n      console.error(\"Error fetching documents:\", err);\r\n      setError(\"Não foi possível carregar os documentos.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenUploadModal = (type) => {\r\n    setDocumentType(type);\r\n    setSelectedFile(null);\r\n    setUploadError(null);\r\n    setDropdownOpen(false);\r\n\r\n    // Trigger file input click\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      setSelectedFile(e.target.files[0]);\r\n      setUploadError(null);\r\n    }\r\n  };\r\n\r\n  const handleUploadDocument = async () => {\r\n    if (!selectedFile || !documentType) {\r\n      setUploadError(\"Selecione um arquivo e um tipo de documento\");\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    setUploadProgress(0);\r\n    setUploadError(null);\r\n\r\n    // Se estiver no modo de criação, armazenar o documento temporariamente\r\n    if (isCreating) {\r\n      console.log('Adicionando documento temporário:', { file: selectedFile, type: documentType });\r\n\r\n      // Adicionar o documento à lista de documentos temporários\r\n      onAddTempDocument && onAddTempDocument({ file: selectedFile, type: documentType });\r\n\r\n      // Adicionar o documento à lista local para exibição\r\n      const newTempDoc = {\r\n        id: `temp-${Date.now()}`,\r\n        fileName: selectedFile.name,\r\n        fileSize: selectedFile.size,\r\n        fileType: selectedFile.type,\r\n        documentType: documentType,\r\n        createdAt: new Date().toISOString(),\r\n        isTemp: true\r\n      };\r\n\r\n      setDocuments(prev => [...prev, newTempDoc]);\r\n      setSelectedFile(null);\r\n      setDocumentType(\"\");\r\n      setIsUploading(false);\r\n      return;\r\n    }\r\n\r\n    // Se não estiver no modo de criação, fazer o upload normalmente\r\n    const formData = new FormData();\r\n    formData.append(\"documents\", selectedFile);\r\n    formData.append(\"types\", JSON.stringify([documentType]));\r\n\r\n    try {\r\n      await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n        onUploadProgress: (progressEvent) => {\r\n          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n          setUploadProgress(percentCompleted);\r\n        },\r\n      });\r\n\r\n      // Reload documents after successful upload\r\n      loadDocuments();\r\n      setSelectedFile(null);\r\n      setDocumentType(\"\");\r\n    } catch (err) {\r\n      console.error(\"Error uploading document:\", err);\r\n      setUploadError(err.response?.data?.message || \"Erro ao fazer upload do documento\");\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteDocument = (documentId) => {\r\n    setDocumentToDelete(documentId);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const confirmDeleteDocument = async () => {\r\n    if (!documentToDelete) return;\r\n\r\n    try {\r\n      await api.delete(`/documents/${documentToDelete}`);\r\n      setDocuments(documents.filter(doc => doc.id !== documentToDelete));\r\n      setConfirmDialogOpen(false);\r\n      setDocumentToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting document:\", err);\r\n      setError(\"Não foi possível excluir o documento.\");\r\n    }\r\n  };\r\n\r\n  const handleViewDocument = (documentId) => {\r\n    window.open(`/api/documents/${documentId}`, '_blank');\r\n  };\r\n\r\n  const documentTypes = [\r\n    { id: \"RG\", label: \"RG\" },\r\n    { id: \"CPF\", label: \"CPF\" },\r\n    { id: \"CNH\", label: \"Carteira de Motorista\" },\r\n    { id: \"COMP_RESIDENCIA\", label: \"Comprovante de Residência\" },\r\n    { id: \"CERTIDAO_NASCIMENTO\", label: \"Certidão de Nascimento\" },\r\n    { id: \"CERTIDAO_CASAMENTO\", label: \"Certidão de Casamento\" },\r\n    { id: \"CARTAO_VACINACAO\", label: \"Cartão de Vacinação\" },\r\n    { id: \"PASSAPORTE\", label: \"Passaporte\" },\r\n    { id: \"TITULO_ELEITOR\", label: \"Título de Eleitor\" },\r\n    { id: \"CARTEIRA_TRABALHO\", label: \"Carteira de Trabalho\" },\r\n    { id: \"OUTROS\", label: \"Outros\" }\r\n  ];\r\n\r\n  const getDocumentTypeDisplay = (type) => {\r\n    const found = documentTypes.find(docType => docType.id === type);\r\n    return found ? found.label : type;\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  // Se não estiver no modo de criação e não tiver ID, exibir mensagem\r\n  if (!personId && !isCreating) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <FileText size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Documentos</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar documentos.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">Documentos</h3>\r\n          {isLoading ? (\r\n            <Loader2 size={16} className=\"animate-spin text-neutral-400 dark:text-gray-500\" />\r\n          ) : (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {documents.length}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Dropdown menu for document type selection */}\r\n          <div className=\"relative\">\r\n            <button\r\n              ref={buttonRef}\r\n              onClick={() => setDropdownOpen(!dropdownOpen)}\r\n              className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n            >\r\n              <Upload size={16} />\r\n              <span>Adicionar</span>\r\n              <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n            </button>\r\n\r\n            {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n            {dropdownOpen && mounted && createPortal(\r\n              <div\r\n                ref={dropdownRef}\r\n                className=\"fixed z-[9999] w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n                style={{\r\n                  top: `${dropdownPosition.top}px`,\r\n                  right: `${dropdownPosition.right}px`,\r\n                  width: `${dropdownPosition.width}px`,\r\n                }}\r\n              >\r\n                <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n                  <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Selecione o tipo de documento</h4>\r\n                </div>\r\n                <div className=\"max-h-96 overflow-y-auto\">\r\n                  {documentTypes.map(type => (\r\n                    <button\r\n                      key={type.id}\r\n                      onClick={() => handleOpenUploadModal(type.id)}\r\n                      className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors\"\r\n                    >\r\n                      <FileText size={14} className=\"text-neutral-400 dark:text-gray-500\" />\r\n                      <span>{type.label}</span>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>,\r\n              document.body\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input */}\r\n      <input\r\n        type=\"file\"\r\n        ref={fileInputRef}\r\n        className=\"hidden\"\r\n        onChange={handleFileChange}\r\n        accept=\".pdf,.jpg,.jpeg,.png\"\r\n      />\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\">\r\n          {error}\r\n          <button\r\n            onClick={loadDocuments}\r\n            className=\"ml-2 underline hover:no-underline\"\r\n          >\r\n            Tentar novamente\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* File upload preview */}\r\n      {selectedFile && (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm dark:shadow-md dark:shadow-black/20 p-4 border border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"p-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400\">\r\n                <FileText size={20} />\r\n              </div>\r\n              <div>\r\n                <p className=\"font-medium text-neutral-800 dark:text-gray-100\">{selectedFile.name}</p>\r\n                <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                  {getDocumentTypeDisplay(documentType)} • {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              <button\r\n                onClick={() => setSelectedFile(null)}\r\n                className=\"p-2 text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {uploadError && (\r\n            <div className=\"mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded text-sm\">\r\n              {uploadError}\r\n            </div>\r\n          )}\r\n\r\n          {isUploading && (\r\n            <div className=\"mt-3 space-y-1\">\r\n              <div className=\"h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden\">\r\n                <div\r\n                  className=\"h-full bg-primary-500 dark:bg-primary-600 rounded-full\"\r\n                  style={{ width: `${uploadProgress}%` }}\r\n                ></div>\r\n              </div>\r\n              <p className=\"text-right text-xs text-neutral-500 dark:text-gray-400\">\r\n                {uploadProgress}%\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"mt-3 flex justify-end\">\r\n            <button\r\n              onClick={handleUploadDocument}\r\n              disabled={isUploading}\r\n              className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2 disabled:opacity-50\"\r\n            >\r\n              {isUploading ? (\r\n                <>\r\n                  <Loader2 size={16} className=\"animate-spin\" />\r\n                  <span>Enviando...</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Upload size={16} />\r\n                  <span>Enviar documento</span>\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Document list */}\r\n      {isLoading ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\">\r\n          <div className=\"flex flex-col items-center\">\r\n            <Loader2 size={32} className=\"text-primary-500 dark:text-primary-400 animate-spin mb-4\" />\r\n            <p className=\"text-neutral-600 dark:text-gray-300\">Carregando documentos...</p>\r\n          </div>\r\n        </div>\r\n      ) : documents.length === 0 ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n          <FileText size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n          <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum documento</h4>\r\n          <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n            Adicione documentos importantes como RG, CPF, comprovantes e outros para esta pessoa.\r\n          </p>\r\n          <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2 max-w-2xl\">\r\n            {documentTypes.slice(0, 6).map(type => (\r\n              <button\r\n                key={type.id}\r\n                onClick={() => handleOpenUploadModal(type.id)}\r\n                className=\"flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\"\r\n              >\r\n                <span className=\"text-xs text-neutral-600 dark:text-gray-300 text-center\">{type.label}</span>\r\n                <Plus size={16} className=\"text-primary-500 dark:text-primary-400 mt-1\" />\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n          <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n            <thead>\r\n              <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Tipo\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Arquivo\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Data de Upload\r\n                </th>\r\n                <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Ações\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              {documents.map(document => (\r\n                <tr key={document.id} className={`hover:bg-neutral-50 dark:hover:bg-gray-700 ${document.isTemp ? 'bg-yellow-50 dark:bg-yellow-900/20' : ''}`}>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <span className=\"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-200\">\r\n                      {document.isTemp ? document.documentType : getDocumentTypeDisplay(document.type)}\r\n                    </span>\r\n                    {document.isTemp && (\r\n                      <span className=\"ml-2 px-2 py-1 text-xs rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300\">\r\n                        Pendente\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <FileText className=\"h-4 w-4 text-neutral-400 dark:text-gray-500 mr-2\" />\r\n                      <span className=\"text-neutral-700 dark:text-gray-200\">\r\n                        {document.isTemp ? document.fileName : document.filename}\r\n                      </span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-neutral-500 dark:text-gray-400\">\r\n                    {formatDate(document.createdAt)}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                    <div className=\"flex justify-end\">\r\n                      {document.isTemp ? (\r\n                        <span className=\"text-xs text-amber-600 dark:text-amber-400\">\r\n                          Será salvo quando a pessoa for criada\r\n                        </span>\r\n                      ) : (\r\n                        <>\r\n                          <button\r\n                            onClick={() => handleViewDocument(document.id)}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                            title=\"Visualizar\"\r\n                          >\r\n                            <Eye size={16} />\r\n                          </button>\r\n                          <button\r\n                            onClick={() => window.open(`/api/documents/${document.id}?download=true`, '_blank')}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                            title=\"Baixar\"\r\n                          >\r\n                            <Download size={16} />\r\n                          </button>\r\n                          <button\r\n                            onClick={() => handleDeleteDocument(document.id)}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                            title=\"Excluir\"\r\n                          >\r\n                            <Trash size={16} />\r\n                          </button>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmDialogOpen}\r\n        onClose={() => {\r\n          setConfirmDialogOpen(false);\r\n          setDocumentToDelete(null);\r\n        }}\r\n        onConfirm={confirmDeleteDocument}\r\n        title=\"Excluir Documento\"\r\n        message=\"Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.\"\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentsTab;"], "names": [], "mappings": ";;;;AAEA;AACA;AAWA;AACA;AACA;AACA;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;;;AAmBA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,EAAE;IAC5F,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IAEtF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF,OAAO,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG;YACjD,2EAA2E;YAC3E,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;oBAC3D,IAAI,CAAC,KAAK,EAAE,OAAO;oBACnB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,cAAc,IAAI,IAAI;oBACtB,WAAW,IAAI,OAAO,WAAW;oBACjC,QAAQ;gBACV,CAAC;YAED,aAAa;YACb,aAAa;QACf,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAU;QAAY;KAAc;IAExC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,cAAc;gBAC3C,QAAQ;oBACN,UAAU;oBACV,YAAY;gBACd;YACF;YACA,aAAa,SAAS,IAAI,IAAI,EAAE;QAClC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACjC,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,eAAe;YACf;QACF;QAEA,eAAe;QACf,kBAAkB;QAClB,eAAe;QAEf,uEAAuE;QACvE,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,qCAAqC;gBAAE,MAAM;gBAAc,MAAM;YAAa;YAE1F,0DAA0D;YAC1D,qBAAqB,kBAAkB;gBAAE,MAAM;gBAAc,MAAM;YAAa;YAEhF,oDAAoD;YACpD,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,aAAa,IAAI;gBAC3B,UAAU,aAAa,IAAI;gBAC3B,UAAU,aAAa,IAAI;gBAC3B,cAAc;gBACd,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YAEA,aAAa,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YAC1C,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf;QACF;QAEA,gEAAgE;QAChE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC;YAAC;SAAa;QAEtD,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,kBAAkB,CAAC,EAAE,UAAU;gBACnF,SAAS;oBACP,gBAAgB;gBAClB;gBACA,kBAAkB,CAAC;oBACjB,MAAM,mBAAmB,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBACtF,kBAAkB;gBACpB;YACF;YAEA,2CAA2C;YAC3C;YACA,gBAAgB;YAChB,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,qBAAqB;IACvB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,kBAAkB;YACjD,aAAa,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAChD,qBAAqB;YACrB,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE;IAC9C;IAEA,MAAM,gBAAgB;QACpB;YAAE,IAAI;YAAM,OAAO;QAAK;QACxB;YAAE,IAAI;YAAO,OAAO;QAAM;QAC1B;YAAE,IAAI;YAAO,OAAO;QAAwB;QAC5C;YAAE,IAAI;YAAmB,OAAO;QAA4B;QAC5D;YAAE,IAAI;YAAuB,OAAO;QAAyB;QAC7D;YAAE,IAAI;YAAsB,OAAO;QAAwB;QAC3D;YAAE,IAAI;YAAoB,OAAO;QAAsB;QACvD;YAAE,IAAI;YAAc,OAAO;QAAa;QACxC;YAAE,IAAI;YAAkB,OAAO;QAAoB;QACnD;YAAE,IAAI;YAAqB,OAAO;QAAuB;QACzD;YAAE,IAAI;YAAU,OAAO;QAAS;KACjC;IAED,MAAM,yBAAyB,CAAC;QAC9B,MAAM,QAAQ,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,OAAO,QAAQ,MAAM,KAAK,GAAG;IAC/B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,oEAAoE;IACpE,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BACtE,0BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,8OAAC;gCAAK,WAAU;0CACb,UAAU,MAAM;;;;;;;;;;;;kCAKvB,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;sDACd,8OAAC;sDAAK;;;;;;sDACN,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;gCAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;oCACC,KAAK;oCACL,WAAU;oCACV,OAAO;wCACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;wCAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;wCACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oCACtC;;sDAEA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;;;;;;sDAE1E,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;oDAEC,SAAS,IAAM,sBAAsB,KAAK,EAAE;oDAC5C,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC9B,8OAAC;sEAAM,KAAK,KAAK;;;;;;;mDALZ,KAAK,EAAE;;;;;;;;;;;;;;;0CAUpB,SAAS,IAAI;;;;;;;;;;;;;;;;;;0BAOrB,8OAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,UAAU;gBACV,QAAO;;;;;;YAIR,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAOJ,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAElB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAmD,aAAa,IAAI;;;;;;0DACjF,8OAAC;gDAAE,WAAU;;oDACV,uBAAuB;oDAAc;oDAAI,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK7F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;oBAKlB,6BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIJ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGzC,8OAAC;gCAAE,WAAU;;oCACV;oCAAe;;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,4BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC7B,8OAAC;kDAAK;;;;;;;6DAGR;;kDACE,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;kDACd,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YASjB,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;uBAGrD,UAAU,MAAM,KAAK,kBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC9B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBAAI,WAAU;kCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,qBAC7B,8OAAC;gCAEC,SAAS,IAAM,sBAAsB,KAAK,EAAE;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA2D,KAAK,KAAK;;;;;;kDACrF,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;+BALrB,KAAK,EAAE;;;;;;;;;;;;;;;qCAWpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,UAAU,GAAG,CAAC,CAAA,0BACb,8OAAC;oCAAqB,WAAW,CAAC,2CAA2C,EAAE,UAAS,MAAM,GAAG,uCAAuC,IAAI;;sDAC1I,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DACb,UAAS,MAAM,GAAG,UAAS,YAAY,GAAG,uBAAuB,UAAS,IAAI;;;;;;gDAEhF,UAAS,MAAM,kBACd,8OAAC;oDAAK,WAAU;8DAA2G;;;;;;;;;;;;sDAK/H,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEACb,UAAS,MAAM,GAAG,UAAS,QAAQ,GAAG,UAAS,QAAQ;;;;;;;;;;;;;;;;;sDAI9D,8OAAC;4CAAG,WAAU;sDACX,WAAW,UAAS,SAAS;;;;;;sDAEhC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;0DACZ,UAAS,MAAM,iBACd,8OAAC;oDAAK,WAAU;8DAA6C;;;;;yEAI7D;;sEACE,8OAAC;4DACC,SAAS,IAAM,mBAAmB,UAAS,EAAE;4DAC7C,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,MAAM;;;;;;;;;;;sEAEb,8OAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,UAAS,EAAE,CAAC,cAAc,CAAC,EAAE;4DAC1E,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,MAAM;;;;;;;;;;;sEAElB,8OAAC;4DACC,SAAS,IAAM,qBAAqB,UAAS,EAAE;4DAC/C,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;mCAjDhB,UAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0BA+D9B,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,oBAAoB;gBACtB;gBACA,WAAW;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;;;;;;;AAInB;uCAEe"}}, {"offset": {"line": 3080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/ContactsTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  UserPlus,\r\n  Users,\r\n  Loader2,\r\n  Edit,\r\n  Trash,\r\n  Mail,\r\n  Phone,\r\n  FileText,\r\n  X,\r\n  User\r\n} from \"lucide-react\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport { contactsService } from \"@/app/modules/people/services/contactsService\";\r\nimport MaskedInput from \"@/components/common/MaskedInput\";\r\nimport { ModuleSelect, ModuleFormGroup } from \"@/components/ui\";\r\n\r\nconst ContactsTab = ({ personId, onClose, isCreating = false, onAddTempContact, tempContacts = [] }) => {\r\n  const [contacts, setContacts] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const [contactFormOpen, setContactFormOpen] = useState(false);\r\n  const [selectedContact, setSelectedContact] = useState(null);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [contactToDelete, setContactToDelete] = useState(null);\r\n  const [notesModalOpen, setNotesModalOpen] = useState(false);\r\n  const [selectedNotes, setSelectedNotes] = useState({ name: \"\", notes: \"\" });\r\n\r\n  // Contact form state\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    relationship: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    notes: \"\"\r\n  });\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadContacts();\r\n    }\r\n  }, [personId]);\r\n\r\n  const loadContacts = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const data = await contactsService.getContactsByPerson(personId);\r\n      setContacts(data || []);\r\n    } catch (err) {\r\n      console.error(\"Error fetching contacts:\", err);\r\n      setError(\"Não foi possível carregar os contatos.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenContactForm = (contact = null) => {\r\n    if (contact) {\r\n      setFormData({\r\n        name: contact.name || \"\",\r\n        relationship: contact.relationship || \"\",\r\n        email: contact.email || \"\",\r\n        phone: contact.phone || \"\",\r\n        notes: contact.notes || \"\"\r\n      });\r\n      setSelectedContact(contact);\r\n    } else {\r\n      setFormData({\r\n        name: \"\",\r\n        relationship: \"\",\r\n        email: \"\",\r\n        phone: \"\",\r\n        notes: \"\"\r\n      });\r\n      setSelectedContact(null);\r\n    }\r\n\r\n    setFormErrors({});\r\n    setContactFormOpen(true);\r\n  };\r\n\r\n  const handleCloseContactForm = () => {\r\n    setContactFormOpen(false);\r\n    setSelectedContact(null);\r\n    setFormData({\r\n      name: \"\",\r\n      relationship: \"\",\r\n      email: \"\",\r\n      phone: \"\",\r\n      notes: \"\"\r\n    });\r\n    setFormErrors({});\r\n  };\r\n\r\n  const handleDeleteContact = (contact) => {\r\n    setContactToDelete(contact.id);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const handleViewNotes = (contact) => {\r\n    setSelectedNotes({\r\n      name: contact.name,\r\n      notes: contact.notes || \"\"\r\n    });\r\n    setNotesModalOpen(true);\r\n  };\r\n\r\n  const confirmDeleteContact = async () => {\r\n    if (!contactToDelete) return;\r\n\r\n    try {\r\n      await contactsService.deleteContact(contactToDelete);\r\n      setContacts(contacts.filter(c => c.id !== contactToDelete));\r\n      setConfirmDialogOpen(false);\r\n      setContactToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting contact:\", err);\r\n      setError(\"Não foi possível excluir o contato.\");\r\n    }\r\n  };\r\n\r\n  const handleFormChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n\r\n    // Clear error when field is modified\r\n    if (formErrors[name]) {\r\n      setFormErrors(prev => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  const validateContactForm = () => {\r\n    const errors = {};\r\n\r\n    if (!formData.name.trim()) {\r\n      errors.name = \"Nome é obrigatório\";\r\n    }\r\n\r\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      errors.email = \"Email inválido\";\r\n    }\r\n\r\n    setFormErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleSubmitContact = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateContactForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const payload = {\r\n        personId: personId,\r\n        name: formData.name,\r\n        relationship: formData.relationship || null,\r\n        email: formData.email || null,\r\n        phone: formData.phone ? formData.phone.replace(/\\D/g, \"\") : null,\r\n        notes: formData.notes || null\r\n      };\r\n\r\n      if (isCreating) {\r\n        // Modo de criação - adicionar contato temporário\r\n        if (onAddTempContact) {\r\n          onAddTempContact({\r\n            id: `temp-${Date.now()}`,\r\n            ...payload\r\n          });\r\n        }\r\n        handleCloseContactForm();\r\n      } else {\r\n        // Modo de edição - salvar no banco de dados\r\n        if (selectedContact) {\r\n          // Update existing contact\r\n          await contactsService.updateContact(selectedContact.id, payload);\r\n        } else {\r\n          // Create new contact\r\n          await contactsService.createContact(payload);\r\n        }\r\n\r\n        // Reload contacts and close form\r\n        loadContacts();\r\n        handleCloseContactForm();\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error saving contact:\", err);\r\n      setFormErrors({\r\n        submit: err.response?.data?.message || \"Erro ao salvar contato\"\r\n      });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formatPhone = (phone) => {\r\n    if (!phone) return \"N/A\";\r\n\r\n    // Phone format: (00) 00000-0000\r\n    const phoneNumbers = phone.replace(/\\D/g, '');\r\n    return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\r\n  };\r\n\r\n  // Se não estiver no modo de criação e não tiver personId, mostrar mensagem\r\n  if (!isCreating && !personId) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Users size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Contatos</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar contatos.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">Contatos</h3>\r\n          {isCreating ? (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {tempContacts.length}\r\n            </span>\r\n          ) : isLoading ? (\r\n            <Loader2 size={16} className=\"animate-spin text-neutral-400 dark:text-gray-500\" />\r\n          ) : (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {contacts.length}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        <button\r\n          onClick={() => handleOpenContactForm()}\r\n          className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          <UserPlus size={16} />\r\n          <span>Adicionar Contato</span>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\">\r\n          {error}\r\n          <button\r\n            onClick={loadContacts}\r\n            className=\"ml-2 underline hover:no-underline\"\r\n          >\r\n            Tentar novamente\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Contacts list */}\r\n      {isCreating ? (\r\n        // Modo de criação - mostrar contatos temporários\r\n        tempContacts.length === 0 ? (\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n            <Users size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n            <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum contato</h4>\r\n            <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n              Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.\r\n            </p>\r\n            <button\r\n              onClick={() => handleOpenContactForm()}\r\n              className=\"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n            >\r\n              <UserPlus size={16} />\r\n              <span>Adicionar Contato</span>\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n            <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              <thead>\r\n                <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Nome\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Relacionamento\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Email\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Telefone\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Ações\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n                {tempContacts.map(contact => (\r\n                  <tr key={contact.id} className=\"hover:bg-neutral-50 dark:hover:bg-gray-700\">\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\">\r\n                          {contact.name.charAt(0).toUpperCase()}\r\n                        </div>\r\n                        <span className=\"ml-2 text-neutral-700 dark:text-gray-200\">{contact.name}</span>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\">\r\n                        {contact.relationship || \"Não especificado\"}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      {contact.email ? (\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Mail className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                          <span className=\"text-primary-600 dark:text-primary-400\">\r\n                            {contact.email}\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      {contact.phone ? (\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Phone className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                          <span className=\"text-neutral-700 dark:text-gray-300\">\r\n                            {formatPhone(contact.phone)}\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                      <div className=\"flex justify-end\">\r\n                        <button\r\n                          onClick={() => handleOpenContactForm(contact)}\r\n                          className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                          title=\"Editar\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDeleteContact(contact)}\r\n                          className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                          title=\"Excluir\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )\r\n      ) : isLoading ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\">\r\n          <div className=\"flex flex-col items-center\">\r\n            <Loader2 size={32} className=\"text-primary-500 dark:text-primary-400 animate-spin mb-4\" />\r\n            <p className=\"text-neutral-600 dark:text-gray-300\">Carregando contatos...</p>\r\n          </div>\r\n        </div>\r\n      ) : contacts.length === 0 ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n          <Users size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n          <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum contato</h4>\r\n          <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n            Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.\r\n          </p>\r\n          <button\r\n            onClick={() => handleOpenContactForm()}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n          >\r\n            <UserPlus size={16} />\r\n            <span>Adicionar Contato</span>\r\n          </button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n          <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n            <thead>\r\n              <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Nome\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Relacionamento\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Email\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Telefone\r\n                </th>\r\n                <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Ações\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              {contacts.map(contact => (\r\n                <tr key={contact.id} className=\"hover:bg-neutral-50 dark:hover:bg-gray-700\">\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\">\r\n                        {contact.name.charAt(0).toUpperCase()}\r\n                      </div>\r\n                      <span className=\"ml-2 text-neutral-700 dark:text-gray-200\">{contact.name}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\">\r\n                      {contact.relationship || \"Não especificado\"}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    {contact.email ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Mail className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                        <a href={`mailto:${contact.email}`} className=\"text-primary-600 dark:text-primary-400 hover:underline\">\r\n                          {contact.email}\r\n                        </a>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    {contact.phone ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Phone className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                        <a href={`tel:${contact.phone}`} className=\"text-neutral-700 dark:text-gray-300\">\r\n                          {formatPhone(contact.phone)}\r\n                        </a>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                    <div className=\"flex justify-end\">\r\n                      <button\r\n                        onClick={() => handleOpenContactForm(contact)}\r\n                        className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                        title=\"Editar\"\r\n                      >\r\n                        <Edit size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleViewNotes(contact)}\r\n                        className={`p-1 ${contact.notes ? 'text-neutral-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400' : 'text-neutral-300 dark:text-gray-600 cursor-not-allowed'}`}\r\n                        title={contact.notes ? \"Ver Observações\" : \"Sem Observações\"}\r\n                        disabled={!contact.notes}\r\n                      >\r\n                        <FileText size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDeleteContact(contact)}\r\n                        className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                        title=\"Excluir\"\r\n                      >\r\n                        <Trash size={16} />\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Contact Form Modal */}\r\n      {contactFormOpen && (\r\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n          <div className=\"fixed inset-0 bg-black/50\" onClick={handleCloseContactForm}></div>\r\n\r\n          <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[60]\">\r\n            <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n              <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-gray-100\">\r\n                {selectedContact ? \"Editar Contato\" : \"Novo Contato\"}\r\n              </h3>\r\n              <button\r\n                onClick={handleCloseContactForm}\r\n                className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              {formErrors.submit && (\r\n                <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg text-sm\">\r\n                  {formErrors.submit}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleSubmitContact} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"name\">\r\n                    Nome *\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <User className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"name\"\r\n                      name=\"name\"\r\n                      type=\"text\"\r\n                      value={formData.name}\r\n                      onChange={handleFormChange}\r\n                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.name ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}\r\n                      placeholder=\"Nome do contato\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                  {formErrors.name && <p className=\"mt-1 text-xs text-red-600 dark:text-red-400\">{formErrors.name}</p>}\r\n                </div>\r\n\r\n                <ModuleFormGroup\r\n                  moduleColor=\"people\"\r\n                  label=\"Relacionamento\"\r\n                  htmlFor=\"relationship\"\r\n                  icon={<Users size={16} />}\r\n                >\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    id=\"relationship\"\r\n                    name=\"relationship\"\r\n                    value={formData.relationship}\r\n                    onChange={handleFormChange}\r\n                    placeholder=\"Selecione o relacionamento\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    <option value=\"\">Selecione</option>\r\n                    <option value=\"Cônjuge\">Cônjuge</option>\r\n                    <option value=\"Filho/Filha\">Filho/Filha</option>\r\n                    <option value=\"Pai/Mãe\">Pai/Mãe</option>\r\n                    <option value=\"Irmão/Irmã\">Irmão/Irmã</option>\r\n                    <option value=\"Avô/Avó\">Avô/Avó</option>\r\n                    <option value=\"Tio/Tia\">Tio/Tia</option>\r\n                    <option value=\"Primo/Prima\">Primo/Prima</option>\r\n                    <option value=\"Amigo\">Amigo</option>\r\n                    <option value=\"Colega\">Colega</option>\r\n                    <option value=\"Outro\">Outro</option>\r\n                  </ModuleSelect>\r\n                </ModuleFormGroup>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"email\">\r\n                    Email\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Mail className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"email\"\r\n                      name=\"email\"\r\n                      type=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleFormChange}\r\n                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.email ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}\r\n                      placeholder=\"<EMAIL>\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                  {formErrors.email && <p className=\"mt-1 text-xs text-red-600 dark:text-red-400\">{formErrors.email}</p>}\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"phone\">\r\n                    Telefone\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Phone className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <MaskedInput\r\n                      type=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={(e) =>\r\n                        handleFormChange({\r\n                          target: { name: \"phone\", value: e.target.value },\r\n                        })\r\n                      }\r\n                      placeholder=\"(00) 00000-0000\"\r\n                      className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"notes\">\r\n                    Observações\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute top-3 left-3 flex items-start pointer-events-none\">\r\n                      <FileText className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <textarea\r\n                      id=\"notes\"\r\n                      name=\"notes\"\r\n                      value={formData.notes}\r\n                      onChange={handleFormChange}\r\n                      className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none\"\r\n                      placeholder=\"Observações sobre o contato\"\r\n                      rows={3}\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-end gap-3 pt-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleCloseContactForm}\r\n                    className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    Cancelar\r\n                  </button>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? (\r\n                      <>\r\n                        <Loader2 size={16} className=\"animate-spin\" />\r\n                        <span>Salvando...</span>\r\n                      </>\r\n                    ) : (\r\n                      <span>Salvar</span>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Notes Modal */}\r\n      {notesModalOpen && (\r\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n          <div className=\"fixed inset-0 bg-black/50\" onClick={() => setNotesModalOpen(false)}></div>\r\n\r\n          <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[60]\">\r\n            <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n              <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-gray-100\">\r\n                Observações de {selectedNotes.name}\r\n              </h3>\r\n              <button\r\n                onClick={() => setNotesModalOpen(false)}\r\n                className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg border border-neutral-200 dark:border-gray-600 text-neutral-700 dark:text-gray-300 min-h-[100px] whitespace-pre-wrap\">\r\n                {selectedNotes.notes || \"Nenhuma observação disponível.\"}\r\n              </div>\r\n\r\n              <div className=\"mt-4 flex justify-end\">\r\n                <button\r\n                  onClick={() => setNotesModalOpen(false)}\r\n                  className=\"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  Fechar\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmDialogOpen}\r\n        onClose={() => {\r\n          setConfirmDialogOpen(false);\r\n          setContactToDelete(null);\r\n        }}\r\n        onConfirm={confirmDeleteContact}\r\n        title=\"Excluir Contato\"\r\n        message=\"Tem certeza que deseja excluir este contato? Esta ação não pode ser desfeita.\"\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactsTab;"], "names": [], "mappings": ";;;;AAEA;AAaA;AACA;AACA;AACA;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAlBA;;;;;;;;AAoBA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,EAAE;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,OAAO;IAAG;IAEzE,qBAAqB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,8JAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;YACvD,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,UAAU,IAAI;QAC3C,IAAI,SAAS;YACX,YAAY;gBACV,MAAM,QAAQ,IAAI,IAAI;gBACtB,cAAc,QAAQ,YAAY,IAAI;gBACtC,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;YAC1B;YACA,mBAAmB;QACrB,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;YACA,mBAAmB;QACrB;QAEA,cAAc,CAAC;QACf,mBAAmB;IACrB;IAEA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,mBAAmB;QACnB,YAAY;YACV,MAAM;YACN,cAAc;YACd,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,cAAc,CAAC;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB,QAAQ,EAAE;QAC7B,qBAAqB;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YACf,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK,IAAI;QAC1B;QACA,kBAAkB;IACpB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;YACpC,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1C,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,qCAAqC;QACrC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACvD;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,SAAS,CAAC;QAEhB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB;QAEA,IAAI,SAAS,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1D,OAAO,KAAK,GAAG;QACjB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAEhB,IAAI,CAAC,uBAAuB;YAC1B;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,UAAU;gBACd,UAAU;gBACV,MAAM,SAAS,IAAI;gBACnB,cAAc,SAAS,YAAY,IAAI;gBACvC,OAAO,SAAS,KAAK,IAAI;gBACzB,OAAO,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC5D,OAAO,SAAS,KAAK,IAAI;YAC3B;YAEA,IAAI,YAAY;gBACd,iDAAiD;gBACjD,IAAI,kBAAkB;oBACpB,iBAAiB;wBACf,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;wBACxB,GAAG,OAAO;oBACZ;gBACF;gBACA;YACF,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,iBAAiB;oBACnB,0BAA0B;oBAC1B,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,gBAAgB,EAAE,EAAE;gBAC1D,OAAO;oBACL,qBAAqB;oBACrB,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;gBACtC;gBAEA,iCAAiC;gBACjC;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,cAAc;gBACZ,QAAQ,IAAI,QAAQ,EAAE,MAAM,WAAW;YACzC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,gCAAgC;QAChC,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO,aAAa,OAAO,CAAC,yBAAyB;IACvD;IAEA,2EAA2E;IAC3E,IAAI,CAAC,cAAc,CAAC,UAAU;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;;;;;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BACtE,2BACC,8OAAC;gCAAK,WAAU;0CACb,aAAa,MAAM;;;;;uCAEpB,0BACF,8OAAC,iNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,8OAAC;gCAAK,WAAU;0CACb,SAAS,MAAM;;;;;;;;;;;;kCAKtB,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAKT,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAOJ,aACC,iDAAiD;YACjD,aAAa,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAErC,8OAAC;wDAAK,WAAU;kEAA4C,QAAQ,IAAI;;;;;;;;;;;;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,IAAI;;;;;;;;;;;sDAG7B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,QAAQ,KAAK;;;;;;;;;;;qEAIlB,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEACb,YAAY,QAAQ,KAAK;;;;;;;;;;;qEAI9B,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,8OAAC;wDACC,SAAS,IAAM,oBAAoB;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mCApDZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;uBA8D3B,0BACF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;uBAGrD,SAAS,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAErC,8OAAC;wDAAK,WAAU;kEAA4C,QAAQ,IAAI;;;;;;;;;;;;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,IAAI;;;;;;;;;;;sDAG7B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,MAAM,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;wDAAE,WAAU;kEAC3C,QAAQ,KAAK;;;;;;;;;;;qEAIlB,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,KAAK,EAAE;wDAAE,WAAU;kEACxC,YAAY,QAAQ,KAAK;;;;;;;;;;;qEAI9B,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,8OAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAW,CAAC,IAAI,EAAE,QAAQ,KAAK,GAAG,qFAAqF,0DAA0D;wDACjL,OAAO,QAAQ,KAAK,GAAG,oBAAoB;wDAC3C,UAAU,CAAC,QAAQ,KAAK;kEAExB,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;;;;;;kEAElB,8OAAC;wDACC,SAAS,IAAM,oBAAoB;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mCA5DZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YAwE5B,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA4B,SAAS;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,kBAAkB,mBAAmB;;;;;;kDAExC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;oCACZ,WAAW,MAAM,kBAChB,8OAAC;wCAAI,WAAU;kDACZ,WAAW,MAAM;;;;;;kDAItB,8OAAC;wCAAK,UAAU;wCAAqB,WAAU;;0DAC7C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAO;;;;;;kEAGlG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAW,CAAC,oCAAoC,EAAE,WAAW,IAAI,GAAG,uCAAuC,uCAAuC,mIAAmI,CAAC;gEACtR,aAAY;gEACZ,UAAU;;;;;;;;;;;;oDAGb,WAAW,IAAI,kBAAI,8OAAC;wDAAE,WAAU;kEAA+C,WAAW,IAAI;;;;;;;;;;;;0DAGjG,8OAAC,wLAAA,CAAA,kBAAe;gDACd,aAAY;gDACZ,OAAM;gDACN,SAAQ;gDACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;0DAEnB,cAAA,8OAAC,kLAAA,CAAA,eAAY;oDACX,aAAY;oDACZ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,YAAY;oDAC5B,UAAU;oDACV,aAAY;oDACZ,UAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAW,CAAC,oCAAoC,EAAE,WAAW,KAAK,GAAG,uCAAuC,uCAAuC,mIAAmI,CAAC;gEACvR,aAAY;gEACZ,UAAU;;;;;;;;;;;;oDAGb,WAAW,KAAK,kBAAI,8OAAC;wDAAE,WAAU;kEAA+C,WAAW,KAAK;;;;;;;;;;;;0DAGnG,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC,0IAAA,CAAA,UAAW;gEACV,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IACT,iBAAiB;wEACf,QAAQ;4EAAE,MAAM;4EAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACjD;gEAEF,aAAY;gEACZ,WAAU;gEACV,UAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;gEACZ,MAAM;gEACN,UAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,WAAU;wDACV,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,UAAU;kEAET,6BACC;;8EACE,8OAAC,iNAAA,CAAA,UAAO;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC7B,8OAAC;8EAAK;;;;;;;yFAGR,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrB,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA4B,SAAS,IAAM,kBAAkB;;;;;;kCAE5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAA4D;4CACxD,cAAc,IAAI;;;;;;;kDAEpC,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,cAAc,KAAK,IAAI;;;;;;kDAG1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,mBAAmB;gBACrB;gBACA,WAAW;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;;;;;;;AAInB;uCAEe"}}, {"offset": {"line": 4731, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4747, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/PersonInsuranceFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { AlertCircle, Calendar, CreditCard, FileText } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { format } from \"date-fns\";\r\n\r\nconst PersonInsuranceFormModal = ({ isOpen, onClose, personId, personInsurance, onSuccess, isCreating = false, onAddTempInsurance }) => {\r\n  const [formData, setFormData] = useState({\r\n    personId: personId,\r\n    insuranceId: \"\",\r\n    policyNumber: \"\",\r\n    validUntil: \"\",\r\n    notes: \"\"\r\n  });\r\n\r\n  const [allInsurances, setAllInsurances] = useState([]);\r\n  const [availableInsurances, setAvailableInsurances] = useState([]);\r\n  const [personInsurances, setPersonInsurances] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      try {\r\n        // Carregar todos os convênios disponíveis no sistema\r\n        const insurancesData = await insurancesService.getInsurances();\r\n        setAllInsurances(insurancesData);\r\n\r\n        if (!isCreating && personId) {\r\n          // Carregar convênios já associados a esta pessoa (apenas no modo de edição)\r\n          const personInsurancesData = await insurancesService.listPersonInsurances(personId);\r\n          setPersonInsurances(personInsurancesData);\r\n\r\n          // Filtrar convênios disponíveis (que ainda não estão associados à pessoa)\r\n          filterAvailableInsurances(insurancesData, personInsurancesData);\r\n        } else {\r\n          // No modo de criação, todos os convênios estão disponíveis\r\n          setAvailableInsurances(insurancesData);\r\n          setPersonInsurances([]);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Erro ao carregar dados:\", err);\r\n        setError(\"Erro ao carregar dados. Por favor, tente novamente.\");\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      loadData();\r\n    }\r\n  }, [isOpen, personId, isCreating]);\r\n\r\n  const filterAvailableInsurances = (allIns, personIns) => {\r\n    // Extrair IDs dos convênios da pessoa\r\n    const personInsuranceIds = personIns.map(pi => {\r\n      if (pi.insuranceId) return pi.insuranceId;\r\n      if (pi.insurance && pi.insurance.id) return pi.insurance.id;\r\n      return pi.id;\r\n    });\r\n\r\n    // Filtrar convênios não associados (para o modo de adição)\r\n    const available = allIns.filter(insurance =>\r\n      !personInsuranceIds.includes(insurance.id)\r\n    );\r\n\r\n    setAvailableInsurances(available);\r\n  };\r\n\r\n  // Preencher formulário quando editando\r\n  useEffect(() => {\r\n    if (personInsurance) {\r\n      let insuranceId = personInsurance.insuranceId ||\r\n                       (personInsurance.insurance ? personInsurance.insurance.id : personInsurance.id);\r\n\r\n      let validUntilFormatted = \"\";\r\n      try {\r\n        if (personInsurance.validUntil) {\r\n          validUntilFormatted = format(new Date(personInsurance.validUntil), \"yyyy-MM-dd\");\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Error formatting date:\", e);\r\n      }\r\n\r\n      setFormData({\r\n        personId: personId,\r\n        insuranceId: insuranceId,\r\n        policyNumber: personInsurance.policyNumber || \"\",\r\n        validUntil: validUntilFormatted,\r\n        notes: personInsurance.notes || \"\"\r\n      });\r\n    } else {\r\n      setFormData({\r\n        personId: personId,\r\n        insuranceId: \"\",\r\n        policyNumber: \"\",\r\n        validUntil: \"\",\r\n        notes: \"\"\r\n      });\r\n    }\r\n  }, [personInsurance, personId]);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Converter validUntil para formato ISO se fornecido\r\n      const validUntilFormatted = formData.validUntil\r\n        ? new Date(formData.validUntil + 'T00:00:00Z').toISOString()\r\n        : null;\r\n\r\n      // Preparar o payload\r\n      const payload = {\r\n        personId: formData.personId,\r\n        insuranceId: formData.insuranceId,\r\n        policyNumber: formData.policyNumber,\r\n        validUntil: validUntilFormatted,\r\n        notes: formData.notes\r\n      };\r\n\r\n      if (isCreating) {\r\n        // Modo de criação - adicionar convênio temporário\r\n        if (onAddTempInsurance) {\r\n          // Adicionar informações do convênio selecionado\r\n          const selectedInsurance = allInsurances.find(ins => ins.id === formData.insuranceId);\r\n\r\n          onAddTempInsurance({\r\n            ...payload,\r\n            name: selectedInsurance?.name || \"Convênio\",\r\n            isTemp: true\r\n          });\r\n\r\n          // Exibir toast de sucesso\r\n          if (window.showToast) {\r\n            window.showToast({\r\n              type: \"success\",\r\n              message: \"Convênio adicionado temporariamente\"\r\n            });\r\n          }\r\n\r\n          // Fechar o modal\r\n          onSuccess();\r\n        }\r\n      } else if (personInsurance) {\r\n        // Modo de edição\r\n        await insurancesService.updatePersonInsurance(personId, formData.insuranceId, {\r\n          policyNumber: formData.policyNumber,\r\n          validUntil: validUntilFormatted,\r\n          notes: formData.notes\r\n        });\r\n\r\n        // Chamar callback de sucesso\r\n        onSuccess();\r\n\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: \"success\",\r\n            message: \"Convênio atualizado com sucesso\"\r\n          });\r\n        }\r\n      } else {\r\n        // Modo de adição normal\r\n        await insurancesService.addPersonInsurance(payload);\r\n\r\n        // Chamar callback de sucesso\r\n        onSuccess();\r\n\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: \"success\",\r\n            message: \"Convênio adicionado com sucesso\"\r\n          });\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Erro ao salvar convênio:\", err);\r\n      const errorMessage = err.response?.data?.message || \"Ocorreu um erro ao salvar o convênio.\";\r\n      setError(errorMessage);\r\n\r\n      // Exibir toast de erro se disponível\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"error\",\r\n          message: errorMessage\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Função para buscar o nome de um convênio pelo ID\r\n  const getInsuranceName = (id) => {\r\n    const insurance = allInsurances.find(ins => ins.id === id);\r\n    return insurance ? insurance.name : \"Convênio\";\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isSubmitting}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"person-insurance-form\"\r\n        isLoading={isSubmitting}\r\n        disabled={isSubmitting || (!personInsurance && !formData.insuranceId)}\r\n      >\r\n        {personInsurance ? \"Atualizar\" : \"Adicionar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={personInsurance ? `Editar ${getInsuranceName(formData.insuranceId)}` : \"Adicionar Convênio\"}\r\n      icon={<CreditCard size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"md\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"person-insurance-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6\">\r\n          {error && (\r\n            <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2\">\r\n              <AlertCircle size={16} className=\"mt-0.5\" />\r\n              <span>{error}</span>\r\n            </div>\r\n          )}\r\n\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-8\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500\"></div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div>\r\n                <h4 className=\"text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4\">\r\n                  <CreditCard className=\"w-4 h-4\" />\r\n                  Informações do Convênio\r\n                </h4>\r\n              </div>\r\n\r\n              {!personInsurance && (\r\n                <ModuleFormGroup\r\n                  moduleColor=\"people\"\r\n                  label=\"Convênio\"\r\n                  htmlFor=\"insuranceId\"\r\n                  icon={<CreditCard size={16} />}\r\n                  required\r\n                  helpText={availableInsurances.length === 0 && !isLoading ? \"Todos os convênios já estão associados a esta pessoa.\" : \"\"}\r\n                >\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    id=\"insuranceId\"\r\n                    name=\"insuranceId\"\r\n                    value={formData.insuranceId}\r\n                    onChange={handleChange}\r\n                    required\r\n                    disabled={isSubmitting}\r\n                    placeholder=\"Selecione um convênio\"\r\n                  >\r\n                    {availableInsurances.length === 0 ? (\r\n                      <option disabled>Nenhum convênio disponível</option>\r\n                    ) : (\r\n                      availableInsurances.map(insurance => (\r\n                        <option key={insurance.id} value={insurance.id}>\r\n                          {insurance.name}\r\n                        </option>\r\n                      ))\r\n                    )}\r\n                  </ModuleSelect>\r\n                </ModuleFormGroup>\r\n              )}\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Número da Carteirinha\"\r\n                htmlFor=\"policyNumber\"\r\n                icon={<CreditCard size={16} />}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"policyNumber\"\r\n                  name=\"policyNumber\"\r\n                  value={formData.policyNumber}\r\n                  onChange={handleChange}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Validade\"\r\n                htmlFor=\"validUntil\"\r\n                icon={<Calendar size={16} />}\r\n              >\r\n                <div className=\"relative\">\r\n                  <ModuleInput\r\n                    moduleColor=\"people\"\r\n                    type=\"date\"\r\n                    id=\"validUntil\"\r\n                    name=\"validUntil\"\r\n                    value={formData.validUntil}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 pointer-events-none\" size={18} />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Observações\"\r\n                htmlFor=\"notes\"\r\n                icon={<FileText size={16} />}\r\n              >\r\n                <ModuleTextarea\r\n                  moduleColor=\"people\"\r\n                  id=\"notes\"\r\n                  name=\"notes\"\r\n                  value={formData.notes}\r\n                  onChange={handleChange}\r\n                  rows={3}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n            </>\r\n          )}\r\n        </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default PersonInsuranceFormModal;"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAFA;AAAA;AADA;AAAA;AACA;AAAA;AAAA;AADA;AAAA;AACA;;;;;;;AAIA,MAAM,2BAA2B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,KAAK,EAAE,kBAAkB,EAAE;IACjI,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,cAAc;QACd,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,aAAa;YACb,SAAS;YACT,IAAI;gBACF,qDAAqD;gBACrD,MAAM,iBAAiB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,aAAa;gBAC5D,iBAAiB;gBAEjB,IAAI,CAAC,cAAc,UAAU;oBAC3B,4EAA4E;oBAC5E,MAAM,uBAAuB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;oBAC1E,oBAAoB;oBAEpB,0EAA0E;oBAC1E,0BAA0B,gBAAgB;gBAC5C,OAAO;oBACL,2DAA2D;oBAC3D,uBAAuB;oBACvB,oBAAoB,EAAE;gBACxB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;QAAU;KAAW;IAEjC,MAAM,4BAA4B,CAAC,QAAQ;QACzC,sCAAsC;QACtC,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAA;YACvC,IAAI,GAAG,WAAW,EAAE,OAAO,GAAG,WAAW;YACzC,IAAI,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO,GAAG,SAAS,CAAC,EAAE;YAC3D,OAAO,GAAG,EAAE;QACd;QAEA,2DAA2D;QAC3D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,YAC9B,CAAC,mBAAmB,QAAQ,CAAC,UAAU,EAAE;QAG3C,uBAAuB;IACzB;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,IAAI,cAAc,gBAAgB,WAAW,IAC5B,CAAC,gBAAgB,SAAS,GAAG,gBAAgB,SAAS,CAAC,EAAE,GAAG,gBAAgB,EAAE;YAE/F,IAAI,sBAAsB;YAC1B,IAAI;gBACF,IAAI,gBAAgB,UAAU,EAAE;oBAC9B,sBAAsB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,gBAAgB,UAAU,GAAG;gBACrE;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;YAEA,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc,gBAAgB,YAAY,IAAI;gBAC9C,YAAY;gBACZ,OAAO,gBAAgB,KAAK,IAAI;YAClC;QACF,OAAO;YACL,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,OAAO;YACT;QACF;IACF,GAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,qDAAqD;YACrD,MAAM,sBAAsB,SAAS,UAAU,GAC3C,IAAI,KAAK,SAAS,UAAU,GAAG,cAAc,WAAW,KACxD;YAEJ,qBAAqB;YACrB,MAAM,UAAU;gBACd,UAAU,SAAS,QAAQ;gBAC3B,aAAa,SAAS,WAAW;gBACjC,cAAc,SAAS,YAAY;gBACnC,YAAY;gBACZ,OAAO,SAAS,KAAK;YACvB;YAEA,IAAI,YAAY;gBACd,kDAAkD;gBAClD,IAAI,oBAAoB;oBACtB,gDAAgD;oBAChD,MAAM,oBAAoB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,SAAS,WAAW;oBAEnF,mBAAmB;wBACjB,GAAG,OAAO;wBACV,MAAM,mBAAmB,QAAQ;wBACjC,QAAQ;oBACV;oBAEA,0BAA0B;oBAC1B,IAAI,OAAO,SAAS,EAAE;wBACpB,OAAO,SAAS,CAAC;4BACf,MAAM;4BACN,SAAS;wBACX;oBACF;oBAEA,iBAAiB;oBACjB;gBACF;YACF,OAAO,IAAI,iBAAiB;gBAC1B,iBAAiB;gBACjB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAAC,UAAU,SAAS,WAAW,EAAE;oBAC5E,cAAc,SAAS,YAAY;oBACnC,YAAY;oBACZ,OAAO,SAAS,KAAK;gBACvB;gBAEA,6BAA6B;gBAC7B;gBAEA,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,wBAAwB;gBACxB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC;gBAE3C,6BAA6B;gBAC7B;gBAEA,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YAET,qCAAqC;YACrC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,mDAAmD;IACnD,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACvD,OAAO,YAAY,UAAU,IAAI,GAAG;IACtC;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;gBACX,UAAU,gBAAiB,CAAC,mBAAmB,CAAC,SAAS,WAAW;0BAEnE,kBAAkB,cAAc;;;;;;;;;;;;IAKvC,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,SAAS,WAAW,GAAG,GAAG;QAC9E,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAwB,UAAU;YAAc,WAAU;;gBAC9D,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACjC,8OAAC;sCAAM;;;;;;;;;;;;gBAIV,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;yCAGjB;;sCACE,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;wBAKrC,CAAC,iCACA,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;4BACxB,QAAQ;4BACR,UAAU,oBAAoB,MAAM,KAAK,KAAK,CAAC,YAAY,0DAA0D;sCAErH,cAAA,8OAAC,kLAAA,CAAA,eAAY;gCACX,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,QAAQ;gCACR,UAAU;gCACV,aAAY;0CAEX,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;oCAAO,QAAQ;8CAAC;;;;;2CAEjB,oBAAoB,GAAG,CAAC,CAAA,0BACtB,8OAAC;wCAA0B,OAAO,UAAU,EAAE;kDAC3C,UAAU,IAAI;uCADJ,UAAU,EAAE;;;;;;;;;;;;;;;sCASnC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;sCAExB,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;;;;;;;;;;;sCAId,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,UAAU;wCAC1B,UAAU;;;;;;kDAEZ,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAiH,MAAM;;;;;;;;;;;;;;;;;sCAI/I,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC,sLAAA,CAAA,iBAAc;gCACb,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AASxB;uCAEe"}}, {"offset": {"line": 5232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/PersonInsurancesTab.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Plus, Edit, Trash, Calendar, CreditCard, AlertCircle } from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport PersonInsuranceFormModal from \"@/components/people/PersonInsuranceFormModal\";\r\n\r\nconst PersonInsurancesTab = ({ personId, onClose, isCreating = false, onAddTempInsurance, tempInsurances = [] }) => {\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [formModalOpen, setFormModalOpen] = useState(false);\r\n  const [selectedInsurance, setSelectedInsurance] = useState(null);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [insuranceToDelete, setInsuranceToDelete] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadInsurances();\r\n    } else if (isCreating) {\r\n      // Se estiver no modo de criação, não precisa carregar do servidor\r\n      setIsLoading(false);\r\n    }\r\n  }, [personId, isCreating]);\r\n\r\n  const loadInsurances = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await insurancesService.listPersonInsurances(personId);\r\n      setInsurances(data);\r\n    } catch (err) {\r\n      console.error(\"Erro ao carregar convênios:\", err);\r\n      setError(\"Não foi possível carregar os convênios da pessoa.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAddInsurance = () => {\r\n    setSelectedInsurance(null);\r\n    setFormModalOpen(true);\r\n  };\r\n\r\n  // Função para adicionar um convênio temporário durante a criação\r\n  const handleAddTempInsurance = (insurance) => {\r\n    if (isCreating && onAddTempInsurance) {\r\n      onAddTempInsurance({\r\n        id: `temp-${Date.now()}`,\r\n        ...insurance,\r\n        isTemp: true\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleEditInsurance = (insurance) => {\r\n    setSelectedInsurance(insurance);\r\n    setFormModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteInsurance = (insurance) => {\r\n    setInsuranceToDelete(insurance);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    try {\r\n      await insurancesService.removePersonInsurance(personId, insuranceToDelete.insuranceId || insuranceToDelete.id);\r\n      loadInsurances();\r\n      setConfirmDialogOpen(false);\r\n      // Adicionar notificação de sucesso\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"success\",\r\n          message: `Convênio removido com sucesso`\r\n        });\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Erro ao excluir convênio:\", err);\r\n      setError(\"Não foi possível excluir o convênio.\");\r\n      // Adicionar notificação de erro\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"error\",\r\n          message: `Erro ao remover convênio: ${err.response?.data?.message || \"Erro desconhecido\"}`\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  const renderInsurancesList = () => {\r\n    // Determine which list of insurances to usar\r\n    const insuranceList = isCreating ? tempInsurances : insurances;\r\n\r\n    // Normalize insurance data structure as it could vary based on API response\r\n    const normalizedInsurances = insuranceList.map(ins => {\r\n      // If the insurance object is already normalized\r\n      if (ins.insurance) {\r\n        return {\r\n          id: ins.insurance.id,\r\n          name: ins.insurance.name,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          // For deletion, we need the join record ID\r\n          insuranceId: ins.insurance.id\r\n        };\r\n      }\r\n      // If we directly get Insurance objects\r\n      else if (ins.id && ins.name) {\r\n        return {\r\n          id: ins.id,\r\n          name: ins.name,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          insuranceId: ins.id\r\n        };\r\n      }\r\n      // If we get records from PersonInsurance with separate insurance properties\r\n      else if (ins.insuranceId) {\r\n        return {\r\n          id: ins.insuranceId,\r\n          name: ins.insuranceName || `Convênio ${ins.insuranceId}`,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          insuranceId: ins.insuranceId\r\n        };\r\n      }\r\n      // Fallback for unexpected structure\r\n      return ins;\r\n    });\r\n\r\n    if (normalizedInsurances.length === 0) {\r\n      return (\r\n        <div className=\"bg-neutral-50 dark:bg-gray-700 p-6 rounded-lg text-center text-neutral-600 dark:text-neutral-300\">\r\n          <p>Nenhum convênio associado a esta pessoa.</p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"grid gap-4\">\r\n        {normalizedInsurances.map((insurance) => (\r\n          <div\r\n            key={insurance.id || insurance.insuranceId}\r\n            className=\"bg-white dark:bg-gray-700 border border-neutral-200 dark:border-gray-600 rounded-lg p-4 shadow-sm\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <h4 className=\"font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\">\r\n                  {insurance.name}\r\n                </h4>\r\n                <div className=\"flex flex-wrap items-center gap-x-4 gap-y-2 text-sm\">\r\n                  {insurance.policyNumber && (\r\n                    <span className=\"flex items-center gap-1 text-neutral-600 dark:text-neutral-300\">\r\n                      <CreditCard size={14} />\r\n                      <span>Carteirinha: {insurance.policyNumber}</span>\r\n                    </span>\r\n                  )}\r\n                  {insurance.validUntil && (\r\n                    <span className=\"flex items-center gap-1 text-neutral-600 dark:text-neutral-300\">\r\n                      <Calendar size={14} />\r\n                      <span>Validade: {formatDate(insurance.validUntil)}</span>\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                {insurance.notes && (\r\n                  <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                    {insurance.notes}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  onClick={() => handleEditInsurance(insurance)}\r\n                  className=\"p-2 hover:bg-neutral-100 dark:hover:bg-gray-600 rounded-lg text-neutral-600 dark:text-neutral-300\"\r\n                >\r\n                  <Edit size={18} />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDeleteInsurance(insurance)}\r\n                  className=\"p-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg text-red-600 dark:text-red-400\"\r\n                >\r\n                  <Trash size={18} />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Verificar se há um ID de pessoa e não está no modo de criação\r\n  if (!personId && !isCreating) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <CreditCard size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Convênios</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar convênios.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-medium text-neutral-800 dark:text-neutral-200\">\r\n          Convênios Associados\r\n        </h3>\r\n        <button\r\n          onClick={handleAddInsurance}\r\n          className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          <Plus size={16} />\r\n          <span>Adicionar Convênio</span>\r\n        </button>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2\">\r\n          <AlertCircle size={16} className=\"mt-0.5\" />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n\r\n      {renderInsurancesList()}\r\n\r\n      {formModalOpen && (\r\n        <PersonInsuranceFormModal\r\n          isOpen={formModalOpen}\r\n          onClose={() => setFormModalOpen(false)}\r\n          personId={personId}\r\n          personInsurance={selectedInsurance}\r\n          isCreating={isCreating}\r\n          onAddTempInsurance={onAddTempInsurance}\r\n          onSuccess={() => {\r\n            if (!isCreating) {\r\n              loadInsurances();\r\n            }\r\n            setFormModalOpen(false);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {confirmDialogOpen && (\r\n        <ConfirmationDialog\r\n          isOpen={confirmDialogOpen}\r\n          onClose={() => setConfirmDialogOpen(false)}\r\n          onConfirm={confirmDelete}\r\n          title=\"Remover Convênio\"\r\n          message={`Tem certeza que deseja remover o convênio ${insuranceToDelete?.name} desta pessoa?`}\r\n          variant=\"danger\"\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonInsurancesTab;"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;AACA;AAJA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAOA,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,EAAE;IAC7G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF,OAAO,IAAI,YAAY;YACrB,kEAAkE;YAClE,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;YAC1D,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,iBAAiB;IACnB;IAEA,iEAAiE;IACjE,MAAM,yBAAyB,CAAC;QAC9B,IAAI,cAAc,oBAAoB;YACpC,mBAAmB;gBACjB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,GAAG,SAAS;gBACZ,QAAQ;YACV;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,qBAAqB;QACrB,iBAAiB;IACnB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gKAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAAC,UAAU,kBAAkB,WAAW,IAAI,kBAAkB,EAAE;YAC7G;YACA,qBAAqB;YACrB,mCAAmC;YACnC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,CAAC,6BAA6B,CAAC;gBAC1C;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;YACT,gCAAgC;YAChC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,CAAC,0BAA0B,EAAE,IAAI,QAAQ,EAAE,MAAM,WAAW,qBAAqB;gBAC5F;YACF;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB;QAC3B,6CAA6C;QAC7C,MAAM,gBAAgB,aAAa,iBAAiB;QAEpD,4EAA4E;QAC5E,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA;YAC7C,gDAAgD;YAChD,IAAI,IAAI,SAAS,EAAE;gBACjB,OAAO;oBACL,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,IAAI;oBACxB,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,2CAA2C;oBAC3C,aAAa,IAAI,SAAS,CAAC,EAAE;gBAC/B;YACF,OAEK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;gBAC3B,OAAO;oBACL,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;oBACd,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,EAAE;gBACrB;YACF,OAEK,IAAI,IAAI,WAAW,EAAE;gBACxB,OAAO;oBACL,IAAI,IAAI,WAAW;oBACnB,MAAM,IAAI,aAAa,IAAI,CAAC,SAAS,EAAE,IAAI,WAAW,EAAE;oBACxD,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,WAAW;gBAC9B;YACF;YACA,oCAAoC;YACpC,OAAO;QACT;QAEA,IAAI,qBAAqB,MAAM,KAAK,GAAG;YACrC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;QAGT;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,qBAAqB,GAAG,CAAC,CAAC,0BACzB,8OAAC;oBAEC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,UAAU,IAAI;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,YAAY,kBACrB,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,kNAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;kEAClB,8OAAC;;4DAAK;4DAAc,UAAU,YAAY;;;;;;;;;;;;;4CAG7C,UAAU,UAAU,kBACnB,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,8OAAC;;4DAAK;4DAAW,WAAW,UAAU,UAAU;;;;;;;;;;;;;;;;;;;oCAIrD,UAAU,KAAK,kBACd,8OAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;0CAItB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mBAvCd,UAAU,EAAE,IAAI,UAAU,WAAW;;;;;;;;;;IA+CpD;IAEA,gEAAgE;IAChE,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAG3E,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAIT,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCACjC,8OAAC;kCAAM;;;;;;;;;;;;YAIV;YAEA,+BACC,8OAAC,uJAAA,CAAA,UAAwB;gBACvB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,UAAU;gBACV,iBAAiB;gBACjB,YAAY;gBACZ,oBAAoB;gBACpB,WAAW;oBACT,IAAI,CAAC,YAAY;wBACf;oBACF;oBACA,iBAAiB;gBACnB;;;;;;YAIH,mCACC,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;gBACX,OAAM;gBACN,SAAS,CAAC,0CAA0C,EAAE,mBAAmB,KAAK,cAAc,CAAC;gBAC7F,SAAQ;;;;;;;;;;;;AAKlB;uCAEe"}}, {"offset": {"line": 5730, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/PersonFormModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { Loader2, Shield, FileText, Users, CreditCard, Check, AlertCircle, User } from \"lucide-react\";\r\nimport ModuleModal from \"@/components/ui/ModuleModal\";\r\nimport ModalButton from \"@/components/ui/ModalButton\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { format } from \"date-fns\";\r\nimport PersonInfoTab from \"./PersonInfoTab\";\r\nimport DocumentsTab from \"./DocumentsTab\";\r\nimport ContactsTab from \"./ContactsTab\";\r\nimport PersonInsurancesTab from \"./PersonInsurancesTab\";\r\n\r\nconst PersonFormModal = ({ isOpen, onClose, person, onSuccess, initialClientId }) => {\r\n  const [activeTab, setActiveTab] = useState(\"info\"); // \"info\", \"documents\", \"contacts\", \"insurances\"\r\n  const [savedPersonId, setSavedPersonId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [availableTabs, setAvailableTabs] = useState({\r\n    info: true,\r\n    documents: false,\r\n    contacts: false,\r\n    insurances: false\r\n  });\r\n  const [showSuccessMessage, setShowSuccessMessage] = useState(false);\r\n  const [tempDocuments, setTempDocuments] = useState([]);\r\n  const [tempContacts, setTempContacts] = useState([]);\r\n  const profileImageUploadRef = useRef(null);\r\n  const [tempInsurances, setTempInsurances] = useState([]);\r\n  const [tempProfileImage, setTempProfileImage] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    fullName: \"\",\r\n    cpf: \"\",\r\n    birthDate: \"\",\r\n    address: \"\",\r\n    neighborhood: \"\",\r\n    city: \"\",\r\n    state: \"\",\r\n    postalCode: \"\",\r\n    phone: \"\",\r\n    email: \"\",\r\n    gender: \"\",\r\n    notes: \"\",\r\n    clientId: \"\",\r\n    relationship: \"\",\r\n    useClientEmail: false,\r\n    useClientPhone: false,\r\n  });\r\n  const [errors, setErrors] = useState({});\r\n\r\n  // Fetch clients and initialize form data when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      console.log('Modal aberto, inicializando dados do formulário');\r\n      console.log('Dados da pessoa recebidos no modal:', person);\r\n      initializeFormData();\r\n    }\r\n  }, [isOpen, person, initialClientId]);\r\n\r\n  const initializeFormData = () => {\r\n    // Reset form data\r\n    let newFormData = {\r\n      fullName: \"\",\r\n      cpf: \"\",\r\n      birthDate: \"\",\r\n      address: \"\",\r\n      neighborhood: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      postalCode: \"\",\r\n      phone: \"\",\r\n      email: \"\",\r\n      gender: \"\",\r\n      notes: \"\",\r\n      clientId: \"\",\r\n      relationship: \"\",\r\n      useClientEmail: false,\r\n      useClientPhone: false,\r\n    };\r\n\r\n    // If editing an existing person\r\n    if (person) {\r\n      let formattedCpf = \"\";\r\n      if (person.cpf) {\r\n        const cleanCpf = person.cpf.replace(/\\D/g, \"\");\r\n        formattedCpf = cleanCpf.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n      }\r\n\r\n      // Formatar telefone\r\n      let formattedPhone = \"\";\r\n      if (person.phone) {\r\n        const cleanPhone = person.phone.replace(/\\D/g, \"\");\r\n        formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n      }\r\n\r\n      let birthDateFormatted = \"\";\r\n      try {\r\n        if (person.birthDate) {\r\n          birthDateFormatted = format(new Date(person.birthDate), \"yyyy-MM-dd\");\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Error formatting date:\", e);\r\n      }\r\n\r\n      // Formatar CEP\r\n      let formattedPostalCode = \"\";\r\n      if (person.postalCode) {\r\n        const cleanPostalCode = person.postalCode.replace(/\\D/g, \"\");\r\n        formattedPostalCode = cleanPostalCode.replace(/(\\d{5})(\\d{3})/, \"$1-$2\");\r\n      }\r\n\r\n      newFormData = {\r\n        fullName: person.fullName || \"\",\r\n        cpf: formattedCpf || \"\",\r\n        birthDate: birthDateFormatted,\r\n        address: person.address || \"\",\r\n        neighborhood: person.neighborhood || \"\",\r\n        city: person.city || \"\",\r\n        state: person.state || \"\",\r\n        postalCode: formattedPostalCode || \"\",\r\n        phone: formattedPhone || \"\",\r\n        email: person.email || \"\",\r\n        gender: person.gender || \"\",\r\n        notes: person.notes || \"\",\r\n        clientId: person.clientId || \"\",\r\n        relationship: person.relationship || \"\",\r\n        profileImageFullUrl: person.profileImageFullUrl || null,\r\n        useClientEmail: person.useClientEmail || false,\r\n        useClientPhone: person.useClientPhone || false,\r\n      };\r\n\r\n      // Log para depuração\r\n      console.log('Dados da pessoa carregados:', person);\r\n      console.log('URL da imagem de perfil:', person.profileImageFullUrl);\r\n\r\n      // Set saved person ID for document and contact tabs\r\n      setSavedPersonId(person.id);\r\n\r\n      // Enable all tabs when editing an existing person\r\n      setAvailableTabs({\r\n        info: true,\r\n        documents: true,\r\n        contacts: true,\r\n        insurances: true\r\n      });\r\n    }\r\n    // If creating a new person with a pre-selected client\r\n    else if (initialClientId) {\r\n      newFormData.clientId = initialClientId;\r\n      newFormData.relationship = \"Titular\";\r\n    }\r\n\r\n    setFormData(newFormData);\r\n    setErrors({});\r\n\r\n    // Default to info tab when opening modal\r\n    setActiveTab(\"info\");\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (!formData.fullName) {\r\n      newErrors.fullName = \"Nome completo é obrigatório\";\r\n    }\r\n\r\n    if (formData.cpf) {\r\n      const cleanCpf = formData.cpf.replace(/\\D/g, \"\");\r\n      if (cleanCpf.length !== 11) {\r\n        newErrors.cpf = \"CPF deve ter 11 dígitos\";\r\n      }\r\n    }\r\n\r\n    // Only validate email format if an email is provided (it's not required)\r\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Email inválido\";\r\n    }\r\n\r\n    if (formData.clientId && !formData.relationship) {\r\n      newErrors.relationship = \"Informe o relacionamento com o cliente\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async (data) => {\r\n    console.log('Validando formulário antes de salvar');\r\n    if (!validateForm()) {\r\n      console.log('Validação falhou, não será possível salvar');\r\n      return false;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    console.log('Iniciando processo de salvamento dos dados');\r\n\r\n    try {\r\n      console.log('Preparando payload para envio');\r\n      const payload = {\r\n        fullName: data.fullName,\r\n        cpf: data.cpf ? data.cpf.replace(/\\D/g, \"\") : null,\r\n        birthDate: data.birthDate || null,\r\n        address: data.address || null,\r\n        neighborhood: data.neighborhood || null,\r\n        city: data.city || null,\r\n        state: data.state || null,\r\n        postalCode: data.postalCode || null,\r\n        phone: data.phone ? data.phone.replace(/\\D/g, \"\") : null,\r\n        email: data.email || null,\r\n        gender: data.gender || null,\r\n        notes: data.notes || null,\r\n        clientId: data.clientId || null,\r\n        relationship: data.relationship || null,\r\n        profileImageUrl: data.profileImageUrl || null,\r\n        useClientEmail: data.useClientEmail || false,\r\n        useClientPhone: data.useClientPhone || false,\r\n      };\r\n\r\n      console.log('Enviando dados para o backend:', payload);\r\n\r\n\r\n      let savedPerson;\r\n\r\n      if (person) {\r\n        // Update existing person\r\n        console.log(`Atualizando pessoa existente (ID: ${person.id})`);\r\n        savedPerson = await personsService.updatePerson(person.id, payload);\r\n        console.log('Pessoa atualizada com sucesso:', savedPerson);\r\n      } else {\r\n        // Create new person\r\n        console.log('Criando nova pessoa');\r\n        savedPerson = await personsService.createPerson(payload);\r\n        console.log('Nova pessoa criada com sucesso:', savedPerson);\r\n      }\r\n\r\n      // Set the ID so we can use it for documents and contacts tabs\r\n      if (savedPerson && savedPerson.id) {\r\n        console.log(`Definindo ID da pessoa salva: ${savedPerson.id}`);\r\n        setSavedPersonId(savedPerson.id);\r\n\r\n        // Processar dados temporários após salvar a pessoa\r\n        await processTemporaryData(savedPerson.id);\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar pessoa:\", error);\r\n\r\n      // Handle API validation errors\r\n      if (error.response?.data?.errors) {\r\n        const apiErrors = {};\r\n        error.response.data.errors.forEach(err => {\r\n          apiErrors[err.param] = err.msg;\r\n        });\r\n        setErrors(apiErrors);\r\n      } else {\r\n        setErrors({\r\n          submit: error.response?.data?.message || \"Erro ao salvar pessoa\"\r\n        });\r\n      }\r\n\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Método para processar os dados temporários após salvar a pessoa\r\n  const processTemporaryData = async (personId) => {\r\n    console.log('Processando dados temporários para a pessoa ID:', personId);\r\n\r\n    try {\r\n      // Processar foto de perfil\r\n      if (tempProfileImage) {\r\n        console.log('Processando foto de perfil temporária');\r\n        await personsService.uploadProfileImage(personId, tempProfileImage);\r\n        setTempProfileImage(null);\r\n      }\r\n\r\n      // Processar documentos temporários\r\n      if (tempDocuments.length > 0) {\r\n        console.log('Processando documentos temporários:', tempDocuments.length);\r\n        for (const doc of tempDocuments) {\r\n          await personsService.uploadDocument(personId, doc.file, doc.type);\r\n        }\r\n        setTempDocuments([]);\r\n      }\r\n\r\n      // Processar contatos temporários\r\n      if (tempContacts.length > 0) {\r\n        console.log('Processando contatos temporários:', tempContacts.length);\r\n        for (const contact of tempContacts) {\r\n          await personsService.createContact(personId, contact);\r\n        }\r\n        setTempContacts([]);\r\n      }\r\n\r\n      // Processar convênios temporários\r\n      if (tempInsurances.length > 0) {\r\n        console.log('Processando convênios temporários:', tempInsurances.length);\r\n        for (const insurance of tempInsurances) {\r\n          await personsService.addPersonInsurance(personId, insurance);\r\n        }\r\n        setTempInsurances([]);\r\n      }\r\n\r\n      console.log('Processamento de dados temporários concluído com sucesso');\r\n    } catch (error) {\r\n      console.error('Erro ao processar dados temporários:', error);\r\n      // Exibir mensagem de erro\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: 'Erro ao processar alguns dados adicionais. Por favor, verifique e tente novamente.'\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (tab) => {\r\n    // Limpar mensagens de erro ao mudar de aba\r\n    setErrors({});\r\n    setShowSuccessMessage(false);\r\n\r\n    // Verificar se a aba está disponível\r\n    if (!availableTabs[tab]) {\r\n      // Se a aba não estiver disponível, mostrar mensagem\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'warning',\r\n          message: 'Complete a etapa atual antes de avançar para a próxima'\r\n        });\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Permitir a navegação entre abas se estiverem disponíveis\r\n    setActiveTab(tab);\r\n\r\n    // Exibir uma mensagem informativa se a pessoa ainda não foi salva\r\n    if ((tab === \"documents\" || tab === \"contacts\" || tab === \"insurances\") && !savedPersonId && !person) {\r\n      console.log('Mudando para a aba', tab, 'sem ID de pessoa');\r\n    }\r\n  };\r\n\r\n  // Função para avançar para a próxima aba\r\n  const handleNextTab = async () => {\r\n    if (activeTab === \"documents\") {\r\n      // Avançar para a próxima aba - Contatos\r\n      setAvailableTabs(prev => ({\r\n        ...prev,\r\n        contacts: true\r\n      }));\r\n\r\n      setActiveTab(\"contacts\");\r\n\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'success',\r\n          message: tempDocuments.length > 0\r\n            ? `${tempDocuments.length} documento(s) adicionado(s) com sucesso`\r\n            : \"Você pode continuar sem adicionar documentos\"\r\n        });\r\n      }\r\n    } else if (activeTab === \"contacts\") {\r\n      // Avançar para a próxima aba - Convênios\r\n      setAvailableTabs(prev => ({\r\n        ...prev,\r\n        insurances: true\r\n      }));\r\n\r\n      setActiveTab(\"insurances\");\r\n\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'success',\r\n          message: tempContacts.length > 0\r\n            ? `${tempContacts.length} contato(s) adicionado(s) com sucesso`\r\n            : \"Você pode continuar sem adicionar contatos\"\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePersonInfoSubmit = async () => {\r\n    console.log('Iniciando salvamento dos dados da pessoa');\r\n\r\n    try {\r\n      // Verificar se há uma imagem para fazer upload\r\n      if (profileImageUploadRef.current && profileImageUploadRef.current.hasSelectedFile && profileImageUploadRef.current.hasSelectedFile()) {\r\n        console.log('Nova imagem detectada, fazendo upload antes de salvar');\r\n\r\n        // Obter o ID da pessoa (existente ou recém-criada)\r\n        const personId = person ? person.id : savedPersonId;\r\n\r\n        if (personId) {\r\n          try {\r\n            // Fazer o upload da imagem\r\n            const imageResponse = await profileImageUploadRef.current.uploadSelectedImage();\r\n            console.log('Upload concluído com sucesso:', imageResponse);\r\n\r\n            if (imageResponse && imageResponse.profileImageUrl) {\r\n              // Atualizar o formData com a nova URL da imagem\r\n              setFormData(prev => ({\r\n                ...prev,\r\n                profileImageUrl: imageResponse.profileImageUrl,\r\n                profileImageFullUrl: imageResponse.fullImageUrl\r\n              }));\r\n\r\n              console.log('FormData atualizado com a nova URL da imagem:', imageResponse.profileImageUrl);\r\n            }\r\n          } catch (error) {\r\n            console.error('Erro ao fazer upload da imagem:', error);\r\n          }\r\n        } else {\r\n          console.log('Pessoa ainda não tem ID, o upload será feito após salvar');\r\n        }\r\n      } else {\r\n        console.log('Nenhuma nova imagem para fazer upload');\r\n      }\r\n\r\n      // Se estiver editando uma pessoa existente\r\n      if (person || savedPersonId) {\r\n        // Salvar os dados da pessoa\r\n        const success = await handleSave(formData);\r\n        console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');\r\n\r\n        if (success) {\r\n          // Mostrar mensagem de sucesso\r\n          setShowSuccessMessage(true);\r\n\r\n          // Habilitar todas as abas após salvar com sucesso\r\n          setAvailableTabs({\r\n            info: true,\r\n            documents: true,\r\n            contacts: true,\r\n            insurances: true\r\n          });\r\n\r\n          // Exibir toast de sucesso\r\n          if (window.showToast) {\r\n            window.showToast({\r\n              type: 'success',\r\n              message: 'Informações salvas com sucesso. Agora você pode adicionar documentos, contatos e convênios.'\r\n            });\r\n          }\r\n\r\n          // Fechar o modal após salvar (apenas na edição)\r\n          onSuccess();\r\n        }\r\n      } else {\r\n        // Criando uma nova pessoa - fluxo de etapas\r\n        if (!validateForm()) return;\r\n\r\n        // Validar informações básicas e avançar para a próxima etapa\r\n        // Atualizar as abas disponíveis\r\n        setAvailableTabs(prev => ({\r\n          ...prev,\r\n          documents: true\r\n        }));\r\n\r\n        // Avançar para a próxima aba - Documentos\r\n        setActiveTab(\"documents\");\r\n\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: 'success',\r\n            message: 'Informações básicas validadas com sucesso'\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro durante o processo de salvamento:', error);\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: error.response?.data?.message || 'Erro ao salvar informações'\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Função para finalizar e criar a pessoa\r\n  const handleFinish = async () => {\r\n    console.log('Finalizando e criando a pessoa');\r\n\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      // Salvar os dados da pessoa\r\n      const success = await handleSave(formData);\r\n      console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');\r\n\r\n      if (success) {\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: 'success',\r\n            message: 'Paciente criado com sucesso!'\r\n          });\r\n        }\r\n\r\n        // Fechar o modal após criar a pessoa com sucesso\r\n        onSuccess();\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro ao finalizar criação da pessoa:', error);\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: error.response?.data?.message || 'Erro ao criar paciente'\r\n        });\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log(`PersonFormModal.handleChange: Atualizando campo ${name} para ${value}`);\r\n\r\n    // Log do estado atual antes da atualização\r\n    console.log('Estado atual do formulário antes da atualização:', formData);\r\n\r\n    setFormData((prev) => {\r\n      const newState = { ...prev, [name]: value };\r\n      console.log('Novo estado do formulário após atualização:', newState);\r\n      return newState;\r\n    });\r\n\r\n    // Clear error when user starts typing\r\n    if (errors[name]) {\r\n      setErrors((prev) => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-between items-center\">\r\n      <div>\r\n        {activeTab !== \"info\" && (\r\n          <ModalButton\r\n            variant=\"secondary\"\r\n            moduleColor=\"people\"\r\n            onClick={() => {\r\n              // Definir a aba anterior com base na aba atual\r\n              const prevTab = {\r\n                documents: \"info\",\r\n                contacts: \"documents\",\r\n                insurances: \"contacts\"\r\n              }[activeTab];\r\n              setActiveTab(prevTab);\r\n            }}\r\n            disabled={isLoading}\r\n          >\r\n            Voltar\r\n          </ModalButton>\r\n        )}\r\n      </div>\r\n      <div className=\"flex gap-3\">\r\n        <ModalButton\r\n          variant=\"secondary\"\r\n          moduleColor=\"people\"\r\n          onClick={onClose}\r\n          disabled={isLoading}\r\n        >\r\n          Cancelar\r\n        </ModalButton>\r\n\r\n        {person || savedPersonId ? (\r\n          // Usuário existente - botão Salvar\r\n          <ModalButton\r\n            variant=\"primary\"\r\n            moduleColor=\"people\"\r\n            onClick={handlePersonInfoSubmit}\r\n            isLoading={isLoading}\r\n          >\r\n            Salvar\r\n          </ModalButton>\r\n        ) : (\r\n          // Novo usuário - botões Continuar ou Criar\r\n          activeTab === \"info\" ? (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handlePersonInfoSubmit}\r\n              isLoading={isLoading}\r\n            >\r\n              Continuar\r\n            </ModalButton>\r\n          ) : activeTab === \"insurances\" ? (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handleFinish}\r\n              isLoading={isLoading}\r\n            >\r\n              Criar Paciente\r\n            </ModalButton>\r\n          ) : (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handleNextTab}\r\n              isLoading={isLoading}\r\n            >\r\n              Continuar\r\n            </ModalButton>\r\n          )\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={person ? \"Editar Pessoa\" : \"Novo Paciente\"}\r\n      icon={<User size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"lg\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex\">\r\n            <button\r\n              onClick={() => handleTabChange(\"info\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"info\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                }`}\r\n            >\r\n              <User size={16} />\r\n              <span>Informações</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"documents\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"documents\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.documents\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <FileText size={16} />\r\n              <span>Documentos</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"contacts\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"contacts\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.contacts\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <Users size={16} />\r\n              <span>Contatos</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"insurances\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"insurances\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.insurances\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <CreditCard size={16} />\r\n              <span>Convênios</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"overflow-y-auto p-6\">\r\n          {errors.submit && (\r\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg\">\r\n              {errors.submit}\r\n            </div>\r\n          )}\r\n\r\n          {showSuccessMessage && (\r\n            <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-400 rounded-lg flex items-center gap-2\">\r\n              <Check size={18} />\r\n              <span>Informações salvas com sucesso! Você pode continuar adicionando documentos, contatos e convênios.</span>\r\n            </div>\r\n          )}\r\n\r\n          {!savedPersonId && !person && activeTab !== \"info\" && (\r\n            <div className=\"mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex items-center gap-2\">\r\n              <AlertCircle size={18} />\r\n              <span>Salve as informações básicas antes de adicionar {activeTab === \"documents\" ? \"documentos\" : activeTab === \"contacts\" ? \"contatos\" : \"convênios\"}.</span>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === \"info\" && (\r\n            <PersonInfoTab\r\n              formData={formData}\r\n              setFormData={setFormData}\r\n              errors={errors}\r\n              isLoading={isLoading}\r\n              handleChange={handleChange}\r\n              onSubmit={handlePersonInfoSubmit}\r\n              personId={savedPersonId || person?.id}\r\n              profileImageUploadRef={profileImageUploadRef}\r\n              isCreating={!savedPersonId && !person}\r\n              onSetTempProfileImage={(file) => {\r\n                console.log('Definindo foto de perfil temporária:', file?.name);\r\n                setTempProfileImage(file);\r\n              }}\r\n              tempProfileImage={tempProfileImage}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"documents\" && (\r\n            <DocumentsTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempDocument={(doc) => {\r\n                console.log('Adicionando documento temporário:', doc);\r\n                setTempDocuments(prev => [...prev, doc]);\r\n              }}\r\n              tempDocuments={tempDocuments}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"contacts\" && (\r\n            <ContactsTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempContact={(contact) => {\r\n                console.log('Adicionando contato temporário:', contact);\r\n                setTempContacts(prev => [...prev, contact]);\r\n              }}\r\n              tempContacts={tempContacts}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"insurances\" && (\r\n            <PersonInsurancesTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempInsurance={(insurance) => {\r\n                console.log('Adicionando convênio temporário:', insurance);\r\n                setTempInsurances(prev => [...prev, insurance]);\r\n              }}\r\n              tempInsurances={tempInsurances}\r\n            />\r\n          )}\r\n        </div>\r\n\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default PersonFormModal;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gDAAgD;IACpG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,MAAM;QACN,WAAW;QACX,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,KAAK;QACL,WAAW;QACX,SAAS;QACT,cAAc;QACd,MAAM;QACN,OAAO;QACP,YAAY;QACZ,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,gBAAgB;IAClB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEtC,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uCAAuC;YACnD;QACF;IACF,GAAG;QAAC;QAAQ;QAAQ;KAAgB;IAEpC,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,IAAI,cAAc;YAChB,UAAU;YACV,KAAK;YACL,WAAW;YACX,SAAS;YACT,cAAc;YACd,MAAM;YACN,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,cAAc;YACd,gBAAgB;YAChB,gBAAgB;QAClB;QAEA,gCAAgC;QAChC,IAAI,QAAQ;YACV,IAAI,eAAe;YACnB,IAAI,OAAO,GAAG,EAAE;gBACd,MAAM,WAAW,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO;gBAC3C,eAAe,SAAS,OAAO,CAAC,gCAAgC;YAClE;YAEA,oBAAoB;YACpB,IAAI,iBAAiB;YACrB,IAAI,OAAO,KAAK,EAAE;gBAChB,MAAM,aAAa,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO;gBAC/C,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;YAC/D;YAEA,IAAI,qBAAqB;YACzB,IAAI;gBACF,IAAI,OAAO,SAAS,EAAE;oBACpB,qBAAqB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,SAAS,GAAG;gBAC1D;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;YAEA,eAAe;YACf,IAAI,sBAAsB;YAC1B,IAAI,OAAO,UAAU,EAAE;gBACrB,MAAM,kBAAkB,OAAO,UAAU,CAAC,OAAO,CAAC,OAAO;gBACzD,sBAAsB,gBAAgB,OAAO,CAAC,kBAAkB;YAClE;YAEA,cAAc;gBACZ,UAAU,OAAO,QAAQ,IAAI;gBAC7B,KAAK,gBAAgB;gBACrB,WAAW;gBACX,SAAS,OAAO,OAAO,IAAI;gBAC3B,cAAc,OAAO,YAAY,IAAI;gBACrC,MAAM,OAAO,IAAI,IAAI;gBACrB,OAAO,OAAO,KAAK,IAAI;gBACvB,YAAY,uBAAuB;gBACnC,OAAO,kBAAkB;gBACzB,OAAO,OAAO,KAAK,IAAI;gBACvB,QAAQ,OAAO,MAAM,IAAI;gBACzB,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU,OAAO,QAAQ,IAAI;gBAC7B,cAAc,OAAO,YAAY,IAAI;gBACrC,qBAAqB,OAAO,mBAAmB,IAAI;gBACnD,gBAAgB,OAAO,cAAc,IAAI;gBACzC,gBAAgB,OAAO,cAAc,IAAI;YAC3C;YAEA,qBAAqB;YACrB,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,QAAQ,GAAG,CAAC,4BAA4B,OAAO,mBAAmB;YAElE,oDAAoD;YACpD,iBAAiB,OAAO,EAAE;YAE1B,kDAAkD;YAClD,iBAAiB;gBACf,MAAM;gBACN,WAAW;gBACX,UAAU;gBACV,YAAY;YACd;QACF,OAEK,IAAI,iBAAiB;YACxB,YAAY,QAAQ,GAAG;YACvB,YAAY,YAAY,GAAG;QAC7B;QAEA,YAAY;QACZ,UAAU,CAAC;QAEX,yCAAyC;QACzC,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC;QAEnB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,GAAG,EAAE;YAChB,MAAM,WAAW,SAAS,GAAG,CAAC,OAAO,CAAC,OAAO;YAC7C,IAAI,SAAS,MAAM,KAAK,IAAI;gBAC1B,UAAU,GAAG,GAAG;YAClB;QACF;QAEA,yEAAyE;QACzE,IAAI,SAAS,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,SAAS,YAAY,EAAE;YAC/C,UAAU,YAAY,GAAG;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa,OAAO;QACxB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,gBAAgB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,aAAa;QACb,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,UAAU;gBACd,UAAU,KAAK,QAAQ;gBACvB,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC9C,WAAW,KAAK,SAAS,IAAI;gBAC7B,SAAS,KAAK,OAAO,IAAI;gBACzB,cAAc,KAAK,YAAY,IAAI;gBACnC,MAAM,KAAK,IAAI,IAAI;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,OAAO,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBACpD,OAAO,KAAK,KAAK,IAAI;gBACrB,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,cAAc,KAAK,YAAY,IAAI;gBACnC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,gBAAgB,KAAK,cAAc,IAAI;YACzC;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAG9C,IAAI;YAEJ,IAAI,QAAQ;gBACV,yBAAyB;gBACzB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC7D,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBAC3D,QAAQ,GAAG,CAAC,kCAAkC;YAChD,OAAO;gBACL,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;gBAChD,QAAQ,GAAG,CAAC,mCAAmC;YACjD;YAEA,8DAA8D;YAC9D,IAAI,eAAe,YAAY,EAAE,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,YAAY,EAAE,EAAE;gBAC7D,iBAAiB,YAAY,EAAE;gBAE/B,mDAAmD;gBACnD,MAAM,qBAAqB,YAAY,EAAE;YAC3C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,+BAA+B;YAC/B,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,MAAM,YAAY,CAAC;gBACnB,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBACjC,SAAS,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG;gBAChC;gBACA,UAAU;YACZ,OAAO;gBACL,UAAU;oBACR,QAAQ,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC3C;YACF;YAEA,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,kEAAkE;IAClE,MAAM,uBAAuB,OAAO;QAClC,QAAQ,GAAG,CAAC,mDAAmD;QAE/D,IAAI;YACF,2BAA2B;YAC3B,IAAI,kBAAkB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;gBAClD,oBAAoB;YACtB;YAEA,mCAAmC;YACnC,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,QAAQ,GAAG,CAAC,uCAAuC,cAAc,MAAM;gBACvE,KAAK,MAAM,OAAO,cAAe;oBAC/B,MAAM,6JAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,EAAE,IAAI,IAAI;gBAClE;gBACA,iBAAiB,EAAE;YACrB;YAEA,iCAAiC;YACjC,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,qCAAqC,aAAa,MAAM;gBACpE,KAAK,MAAM,WAAW,aAAc;oBAClC,MAAM,6JAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,UAAU;gBAC/C;gBACA,gBAAgB,EAAE;YACpB;YAEA,kCAAkC;YAClC,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,QAAQ,GAAG,CAAC,sCAAsC,eAAe,MAAM;gBACvE,KAAK,MAAM,aAAa,eAAgB;oBACtC,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;gBACpD;gBACA,kBAAkB,EAAE;YACtB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,0BAA0B;YAC1B,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,2CAA2C;QAC3C,UAAU,CAAC;QACX,sBAAsB;QAEtB,qCAAqC;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;YACvB,oDAAoD;YACpD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;YACA;QACF;QAEA,2DAA2D;QAC3D,aAAa;QAEb,kEAAkE;QAClE,IAAI,CAAC,QAAQ,eAAe,QAAQ,cAAc,QAAQ,YAAY,KAAK,CAAC,iBAAiB,CAAC,QAAQ;YACpG,QAAQ,GAAG,CAAC,sBAAsB,KAAK;QACzC;IACF;IAEA,yCAAyC;IACzC,MAAM,gBAAgB;QACpB,IAAI,cAAc,aAAa;YAC7B,wCAAwC;YACxC,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YAED,aAAa;YAEb,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,cAAc,MAAM,GAAG,IAC5B,GAAG,cAAc,MAAM,CAAC,uCAAuC,CAAC,GAChE;gBACN;YACF;QACF,OAAO,IAAI,cAAc,YAAY;YACnC,yCAAyC;YACzC,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,YAAY;gBACd,CAAC;YAED,aAAa;YAEb,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,aAAa,MAAM,GAAG,IAC3B,GAAG,aAAa,MAAM,CAAC,qCAAqC,CAAC,GAC7D;gBACN;YACF;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,+CAA+C;YAC/C,IAAI,sBAAsB,OAAO,IAAI,sBAAsB,OAAO,CAAC,eAAe,IAAI,sBAAsB,OAAO,CAAC,eAAe,IAAI;gBACrI,QAAQ,GAAG,CAAC;gBAEZ,mDAAmD;gBACnD,MAAM,WAAW,SAAS,OAAO,EAAE,GAAG;gBAEtC,IAAI,UAAU;oBACZ,IAAI;wBACF,2BAA2B;wBAC3B,MAAM,gBAAgB,MAAM,sBAAsB,OAAO,CAAC,mBAAmB;wBAC7E,QAAQ,GAAG,CAAC,iCAAiC;wBAE7C,IAAI,iBAAiB,cAAc,eAAe,EAAE;4BAClD,gDAAgD;4BAChD,YAAY,CAAA,OAAQ,CAAC;oCACnB,GAAG,IAAI;oCACP,iBAAiB,cAAc,eAAe;oCAC9C,qBAAqB,cAAc,YAAY;gCACjD,CAAC;4BAED,QAAQ,GAAG,CAAC,iDAAiD,cAAc,eAAe;wBAC5F;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,2CAA2C;YAC3C,IAAI,UAAU,eAAe;gBAC3B,4BAA4B;gBAC5B,MAAM,UAAU,MAAM,WAAW;gBACjC,QAAQ,GAAG,CAAC,4BAA4B,UAAU,YAAY;gBAE9D,IAAI,SAAS;oBACX,8BAA8B;oBAC9B,sBAAsB;oBAEtB,kDAAkD;oBAClD,iBAAiB;wBACf,MAAM;wBACN,WAAW;wBACX,UAAU;wBACV,YAAY;oBACd;oBAEA,0BAA0B;oBAC1B,IAAI,OAAO,SAAS,EAAE;wBACpB,OAAO,SAAS,CAAC;4BACf,MAAM;4BACN,SAAS;wBACX;oBACF;oBAEA,gDAAgD;oBAChD;gBACF;YACF,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,CAAC,gBAAgB;gBAErB,6DAA6D;gBAC7D,gCAAgC;gBAChC,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,WAAW;oBACb,CAAC;gBAED,0CAA0C;gBAC1C,aAAa;gBAEb,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC5C;YACF;QACF;IACF;IAEA,yCAAyC;IACzC,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,aAAa;YAEb,4BAA4B;YAC5B,MAAM,UAAU,MAAM,WAAW;YACjC,QAAQ,GAAG,CAAC,4BAA4B,UAAU,YAAY;YAE9D,IAAI,SAAS;gBACX,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,iDAAiD;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC5C;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,KAAK,MAAM,EAAE,OAAO;QAEnF,2CAA2C;QAC3C,QAAQ,GAAG,CAAC,oDAAoD;QAEhE,YAAY,CAAC;YACX,MAAM,WAAW;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM;YAC1C,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,OAAO;QACT;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACrD;IACF;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BACE,cAAc,wBACb,8OAAC,sIAAA,CAAA,UAAW;oBACV,SAAQ;oBACR,aAAY;oBACZ,SAAS;wBACP,+CAA+C;wBAC/C,MAAM,UAAU;4BACd,WAAW;4BACX,UAAU;4BACV,YAAY;wBACd,CAAC,CAAC,UAAU;wBACZ,aAAa;oBACf;oBACA,UAAU;8BACX;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,UAAU;kCACX;;;;;;oBAIA,UAAU,gBACT,mCAAmC;kCACnC,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;+BAID,2CAA2C;oBAC3C,cAAc,uBACZ,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;+BAGC,cAAc,6BAChB,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;6CAID,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;;;;;;;;;;;;;;IASX,qBACE,8OAAC,sIAAA,CAAA,UAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,SAAS,kBAAkB;QAClC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;;0BAIN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,SAChG,iGACA,+HACA;;8CAEJ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CACZ,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,cAChG,iGACA,cAAc,SAAS,GACrB,gIACA,0DACF;;8CAEJ,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,aAChG,iGACA,cAAc,QAAQ,GACpB,gIACA,0DACF;;8CAEJ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;8CACb,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,eAChG,iGACA,cAAc,UAAU,GACtB,gIACA,0DACF;;8CAEJ,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;8CAClB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,MAAM,kBACZ,8OAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM;;;;;;oBAIjB,oCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;0CACb,8OAAC;0CAAK;;;;;;;;;;;;oBAIT,CAAC,iBAAiB,CAAC,UAAU,cAAc,wBAC1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;0CACnB,8OAAC;;oCAAK;oCAAiD,cAAc,cAAc,eAAe,cAAc,aAAa,aAAa;oCAAY;;;;;;;;;;;;;oBAIzJ,cAAc,wBACb,8OAAC,4IAAA,CAAA,UAAa;wBACZ,UAAU;wBACV,aAAa;wBACb,QAAQ;wBACR,WAAW;wBACX,cAAc;wBACd,UAAU;wBACV,UAAU,iBAAiB,QAAQ;wBACnC,uBAAuB;wBACvB,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,uBAAuB,CAAC;4BACtB,QAAQ,GAAG,CAAC,wCAAwC,MAAM;4BAC1D,oBAAoB;wBACtB;wBACA,kBAAkB;;;;;;oBAIrB,cAAc,6BACb,8OAAC,2IAAA,CAAA,UAAY;wBACX,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,mBAAmB,CAAC;4BAClB,QAAQ,GAAG,CAAC,qCAAqC;4BACjD,iBAAiB,CAAA,OAAQ;uCAAI;oCAAM;iCAAI;wBACzC;wBACA,eAAe;;;;;;oBAIlB,cAAc,4BACb,8OAAC,0IAAA,CAAA,UAAW;wBACV,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,kBAAkB,CAAC;4BACjB,QAAQ,GAAG,CAAC,mCAAmC;4BAC/C,gBAAgB,CAAA,OAAQ;uCAAI;oCAAM;iCAAQ;wBAC5C;wBACA,cAAc;;;;;;oBAIjB,cAAc,8BACb,8OAAC,kJAAA,CAAA,UAAmB;wBAClB,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,oBAAoB,CAAC;4BACnB,QAAQ,GAAG,CAAC,oCAAoC;4BAChD,kBAAkB,CAAA,OAAQ;uCAAI;oCAAM;iCAAU;wBAChD;wBACA,gBAAgB;;;;;;;;;;;;;;;;;;AAO9B;uCAEe"}}, {"offset": {"line": 6597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ui/ExportMenu.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, Construction, Image } from \"lucide-react\";\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\n\r\nconst ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' }) => {\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n  const buttonRef = useRef(null);\r\n  const dropdownRef = useRef(null);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Fecha o dropdown ao clicar fora dele\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleExport = (format) => {\r\n    onExport(format);\r\n    setDropdownOpen(false);\r\n  };\r\n\r\n  // Se estiver em construção, mostrar o botão de construção\r\n  if (underConstruction) {\r\n    return (\r\n      <ConstructionButton\r\n        className=\"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors\"\r\n        title=\"Exportação em Construção\"\r\n        content=\"A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.\"\r\n        icon=\"FileText\"\r\n      >\r\n        <Download size={16} />\r\n        <span>Exportar</span>\r\n        <ChevronDown size={14} />\r\n      </ConstructionButton>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        ref={buttonRef}\r\n        onClick={() => setDropdownOpen(!dropdownOpen)}\r\n        className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`}\r\n        disabled={isExporting || (disabled && !underConstruction)}\r\n        title={disabled ? \"Não há dados para exportar\" : \"Exportar dados\"}\r\n      >\r\n        {isExporting ? (\r\n          <Loader2 size={16} className=\"animate-spin\" />\r\n        ) : (\r\n          <Download size={16} />\r\n        )}\r\n        <span>{isExporting ? \"Exportando...\" : \"Exportar\"}</span>\r\n        <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n      {dropdownOpen && mounted && createPortal(\r\n        <div\r\n          ref={dropdownRef}\r\n          className=\"fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n          style={{\r\n            top: `${dropdownPosition.top}px`,\r\n            right: `${dropdownPosition.right}px`,\r\n            width: `${dropdownPosition.width}px`,\r\n          }}\r\n        >\r\n          <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n            <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Formato de exportação</h4>\r\n          </div>\r\n          <div className=\"p-1\">\r\n            <button\r\n              onClick={() => handleExport('image')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <Image size={16} className=\"text-blue-500 dark:text-blue-400\" />\r\n              <span>Imagem (PNG)</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('pdf')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileText size={16} className=\"text-red-500 dark:text-red-400\" />\r\n              <span>PDF</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('xlsx')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileSpreadsheet size={16} className=\"text-green-500 dark:text-green-400\" />\r\n              <span>Excel (XLSX)</span>\r\n            </button>\r\n          </div>\r\n        </div>,\r\n        document.body\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportMenu;"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;;AAQA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,oBAAoB,KAAK,EAAE,YAAY,EAAE,EAAE;IAChH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IACtF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,gBAAgB;IAClB;IAEA,0DAA0D;IAC1D,IAAI,mBAAmB;QACrB,qBACE,8OAAC,wMAAA,CAAA,qBAAkB;YACjB,WAAU;YACV,OAAM;YACN,SAAQ;YACR,MAAK;;8BAEL,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;8BAChB,8OAAC;8BAAK;;;;;;8BACN,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAW,CAAC,+GAA+G,EAAE,UAAU,QAAQ,CAAC,gBAAgB,6CAA6C,4FAA4F,CAAC,EAAE,WAAW;gBACvT,UAAU,eAAgB,YAAY,CAAC;gBACvC,OAAO,WAAW,+BAA+B;;oBAEhD,4BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;kCAElB,8OAAC;kCAAM,cAAc,kBAAkB;;;;;;kCACvC,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;YAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oBACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBACtC;;kCAEA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAE1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;sBAIZ,SAAS,IAAI;;;;;;;AAIrB;uCAEe"}}, {"offset": {"line": 6872, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/ClientFormModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { User, Mail, Lock, Eye, EyeOff, CreditCard, Phone, Calendar, MapPin, FileText, Loader2, AlertCircle } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { format } from \"date-fns\";\r\nimport AddressForm from \"@/components/common/AddressForm\";\r\nimport MaskedInput from \"@/components/common/MaskedInput\";\r\n\r\nconst ClientFormModal = ({ isOpen, onClose, client, onSuccess }) => {\r\n  const [formData, setFormData] = useState({\r\n    login: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n    person: {\r\n      id: \"\",\r\n      fullName: \"\",\r\n      cpf: \"\",\r\n      birthDate: \"\",\r\n      phone: \"\",\r\n      email: \"\",\r\n      gender: \"\",\r\n      address: \"\",\r\n      neighborhood: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      postalCode: \"\",\r\n      notes: \"\",\r\n    }\r\n  });\r\n\r\n  const [errors, setErrors] = useState({});\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [editMode, setEditMode] = useState(false);\r\n\r\n  // Load client data when editing\r\n  useEffect(() => {\r\n    if (client && isOpen) {\r\n      setEditMode(true);\r\n\r\n      // Format birthDate, if it exists\r\n      let birthDateFormatted = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].birthDate) {\r\n        try {\r\n          birthDateFormatted = format(new Date(client.persons[0].birthDate), \"yyyy-MM-dd\");\r\n        } catch (e) {\r\n          console.error(\"Error formatting date:\", e);\r\n        }\r\n      }\r\n\r\n      // Formatar CPF\r\n      let formattedCpf = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].cpf) {\r\n        const cleanCpf = client.persons[0].cpf.replace(/\\D/g, \"\");\r\n        formattedCpf = cleanCpf.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n      }\r\n\r\n      // Formatar telefone\r\n      let formattedPhone = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].phone) {\r\n        const cleanPhone = client.persons[0].phone.replace(/\\D/g, \"\");\r\n        formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n      }\r\n\r\n      // Formatar CEP\r\n      let formattedPostalCode = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].postalCode) {\r\n        const cleanPostalCode = client.persons[0].postalCode.replace(/\\D/g, \"\");\r\n        formattedPostalCode = cleanPostalCode.replace(/(\\d{5})(\\d{3})/, \"$1-$2\");\r\n      }\r\n\r\n      setFormData({\r\n        login: client.login || \"\",\r\n        email: client.email || \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        person: {\r\n          id: client.persons && client.persons[0] ? client.persons[0].id || \"\" : \"\",\r\n          fullName: client.persons && client.persons[0] ? client.persons[0].fullName || \"\" : \"\",\r\n          cpf: formattedCpf || \"\",\r\n          birthDate: birthDateFormatted,\r\n          phone: formattedPhone || \"\",\r\n          email: client.persons && client.persons[0] ? client.persons[0].email || \"\" : \"\",\r\n          gender: client.persons && client.persons[0] ? client.persons[0].gender || \"\" : \"\",\r\n          address: client.persons && client.persons[0] ? client.persons[0].address || \"\" : \"\",\r\n          neighborhood: client.persons && client.persons[0] ? client.persons[0].neighborhood || \"\" : \"\",\r\n          city: client.persons && client.persons[0] ? client.persons[0].city || \"\" : \"\",\r\n          state: client.persons && client.persons[0] ? client.persons[0].state || \"\" : \"\",\r\n          postalCode: formattedPostalCode || \"\",\r\n          notes: client.persons && client.persons[0] ? client.persons[0].notes || \"\" : \"\",\r\n        }\r\n      });\r\n    } else if (isOpen) {\r\n      // Reset form when opening for new client\r\n      setEditMode(false);\r\n      resetForm();\r\n    }\r\n  }, [client, isOpen]);\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      login: \"\",\r\n      email: \"\",\r\n      password: \"\",\r\n      confirmPassword: \"\",\r\n      person: {\r\n        id: \"\",\r\n        fullName: \"\",\r\n        cpf: \"\",\r\n        birthDate: \"\",\r\n        phone: \"\",\r\n        email: \"\",\r\n        gender: \"\",\r\n        address: \"\",\r\n        neighborhood: \"\",\r\n        city: \"\",\r\n        state: \"\",\r\n        postalCode: \"\",\r\n        notes: \"\",\r\n      }\r\n    });\r\n    setErrors({});\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    // Client validation\r\n    if (!formData.login) {\r\n      newErrors.login = \"Login é obrigatório\";\r\n    }\r\n\r\n    if (!formData.email) {\r\n      newErrors.email = \"Email é obrigatório\";\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Email inválido\";\r\n    }\r\n\r\n    // Password is required only for new clients\r\n    if (!editMode) {\r\n      if (!formData.password) {\r\n        newErrors.password = \"Senha é obrigatória\";\r\n      } else if (formData.password.length < 6) {\r\n        newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\r\n      }\r\n\r\n      if (formData.password !== formData.confirmPassword) {\r\n        newErrors.confirmPassword = \"Senhas não conferem\";\r\n      }\r\n    } else if (formData.password && formData.password.length < 6) {\r\n      // If editing and entering a password, it must be at least 6 characters\r\n      newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\r\n    } else if (formData.password && formData.password !== formData.confirmPassword) {\r\n      // If entering a password during edit, it must be confirmed\r\n      newErrors.confirmPassword = \"Senhas não conferem\";\r\n    }\r\n\r\n    // Person validation\r\n    if (!formData.person.fullName) {\r\n      newErrors[\"person.fullName\"] = \"Nome completo é obrigatório\";\r\n    }\r\n\r\n    if (formData.person.cpf) {\r\n      const cleanCpf = formData.person.cpf.replace(/\\D/g, \"\");\r\n      if (cleanCpf.length !== 11) {\r\n        newErrors[\"person.cpf\"] = \"CPF deve ter 11 dígitos\";\r\n      }\r\n    }\r\n\r\n    if (formData.person.email && !/\\S+@\\S+\\.\\S+/.test(formData.person.email)) {\r\n      newErrors[\"person.email\"] = \"Email inválido\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Check if it's a person field or a client field\r\n    if (name.startsWith(\"person.\")) {\r\n      const personField = name.replace(\"person.\", \"\");\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        person: {\r\n          ...prev.person,\r\n          [personField]: value\r\n        }\r\n      }));\r\n    } else {\r\n      setFormData((prev) => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when user starts typing\r\n    if (errors[name]) {\r\n      setErrors((prev) => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  const togglePasswordVisibility = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Prepare person data\r\n      const personData = {\r\n        fullName: formData.person.fullName,\r\n        cpf: formData.person.cpf ? formData.person.cpf.replace(/\\D/g, \"\") : undefined,\r\n        birthDate: formData.person.birthDate || undefined,\r\n        phone: formData.person.phone ? formData.person.phone.replace(/\\D/g, \"\") : undefined,\r\n        email: formData.person.email || undefined,\r\n        gender: formData.person.gender || undefined,\r\n        address: formData.person.address || undefined,\r\n        neighborhood: formData.person.neighborhood || undefined,\r\n        city: formData.person.city || undefined,\r\n        state: formData.person.state || undefined,\r\n        postalCode: formData.person.postalCode || undefined,\r\n        notes: formData.person.notes || undefined,\r\n      };\r\n\r\n      console.log('Dados da pessoa a serem enviados:', personData);\r\n\r\n      if (editMode) {\r\n        // Update existing client account\r\n        const clientPayload = {\r\n          email: formData.email,\r\n        };\r\n\r\n        // Add password only if it's set\r\n        if (formData.password) {\r\n          clientPayload.password = formData.password;\r\n        }\r\n\r\n        await clientsService.updateClient(client.id, clientPayload);\r\n\r\n        // Update the associated person if we have a person ID\r\n        if (formData.person.id) {\r\n          try {\r\n            // Use personsService to update the person\r\n            await personsService.updatePerson(formData.person.id, personData);\r\n          } catch (personError) {\r\n            console.error(\"Erro ao atualizar pessoa:\", personError);\r\n            throw personError;\r\n          }\r\n        }\r\n      } else {\r\n        // Create new client with person\r\n        const payload = {\r\n          login: formData.login,\r\n          email: formData.email,\r\n          password: formData.password,\r\n          person: personData\r\n        };\r\n\r\n        await clientsService.createClient(payload);\r\n      }\r\n\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar cliente:\", error);\r\n\r\n      // Handle API validation errors\r\n      if (error.response?.data?.errors) {\r\n        const apiErrors = {};\r\n        error.response.data.errors.forEach(err => {\r\n          apiErrors[err.param] = err.msg;\r\n        });\r\n        setErrors(apiErrors);\r\n      } else {\r\n        setErrors({\r\n          submit: error.response?.data?.message || \"Erro ao salvar cliente\"\r\n        });\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isLoading}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"client-form\"\r\n        isLoading={isLoading}\r\n      >\r\n        {editMode ? \"Atualizar\" : \"Salvar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={editMode ? \"Editar Cliente\" : \"Novo Cliente\"}\r\n      icon={<User size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"lg\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"client-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6\">\r\n            {errors.submit && (\r\n              <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2\">\r\n                <AlertCircle size={16} />\r\n                <span>{errors.submit}</span>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-2\">\r\n              <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                Dados da Conta\r\n              </h3>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-10\">\r\n              {/* Login */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Login *\"\r\n                htmlFor=\"login\"\r\n                icon={<User size={16} />}\r\n                error={errors.login}\r\n                errorMessage={errors.login}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"login\"\r\n                  name=\"login\"\r\n                  value={formData.login}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Username\"\r\n                  disabled={isLoading || editMode} // Login remains non-editable for existing clients\r\n                  error={!!errors.login}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Email */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Email *\"\r\n                htmlFor=\"email\"\r\n                icon={<Mail size={16} />}\r\n                error={errors.email}\r\n                errorMessage={errors.email}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading}\r\n                  error={!!errors.email}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Password */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label={`Senha ${!editMode ? \"*\" : \"\"}`}\r\n                htmlFor=\"password\"\r\n                icon={<Lock size={16} />}\r\n                error={errors.password}\r\n                errorMessage={errors.password}\r\n                helpText={editMode ? \"Deixe em branco para manter a senha atual\" : \"Mínimo de 6 caracteres\"}\r\n              >\r\n                <div className=\"relative\">\r\n                  <ModuleInput\r\n                    moduleColor=\"people\"\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    value={formData.password}\r\n                    onChange={handleChange}\r\n                    placeholder={editMode ? \"••••••••\" : \"Senha\"}\r\n                    required={!editMode}\r\n                    disabled={isLoading}\r\n                    error={!!errors.password}\r\n                    className=\"pr-10\"\r\n                  />\r\n                  <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={togglePasswordVisibility}\r\n                      className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400\"\r\n                    >\r\n                      {showPassword ? (\r\n                        <EyeOff className=\"h-5 w-5\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Confirm Password */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label={`Confirmar Senha ${!editMode ? \"*\" : \"\"}`}\r\n                htmlFor=\"confirmPassword\"\r\n                icon={<Lock size={16} />}\r\n                error={errors.confirmPassword}\r\n                errorMessage={errors.confirmPassword}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  value={formData.confirmPassword}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Confirme a senha\"\r\n                  required={!editMode}\r\n                  disabled={isLoading}\r\n                  error={!!errors.confirmPassword}\r\n                />\r\n              </ModuleFormGroup>\r\n            </div>\r\n\r\n            <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-4\">\r\n              <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                Dados do Titular\r\n              </h3>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Full name */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Nome completo *\"\r\n                htmlFor=\"person.fullName\"\r\n                icon={<User size={16} />}\r\n                error={errors[\"person.fullName\"]}\r\n                errorMessage={errors[\"person.fullName\"]}\r\n                className=\"md:col-span-2\"\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"person.fullName\"\r\n                  name=\"person.fullName\"\r\n                  value={formData.person.fullName}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Nome completo\"\r\n                  disabled={isLoading}\r\n                  error={!!errors[\"person.fullName\"]}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* CPF */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"CPF\"\r\n                htmlFor=\"person.cpf\"\r\n                icon={<CreditCard size={16} />}\r\n                error={errors[\"person.cpf\"]}\r\n                errorMessage={errors[\"person.cpf\"]}\r\n              >\r\n                <div className=\"relative\">\r\n                  <MaskedInput\r\n                    type=\"cpf\"\r\n                    value={formData.person.cpf}\r\n                    onChange={(e) =>\r\n                      handleChange({\r\n                        target: { name: \"person.cpf\", value: e.target.value },\r\n                      })\r\n                    }\r\n                    placeholder=\"000.000.000-00\"\r\n                    className={`w-full rounded-md border ${errors[\"person.cpf\"] ? \"border-red-300 dark:border-red-700\" : \"border-neutral-300 dark:border-neutral-600\"} px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none`}\r\n                    disabled={isLoading}\r\n                  />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Birth date */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Data de Nascimento\"\r\n                htmlFor=\"person.birthDate\"\r\n                icon={<Calendar size={16} />}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"date\"\r\n                  id=\"person.birthDate\"\r\n                  name=\"person.birthDate\"\r\n                  value={formData.person.birthDate}\r\n                  onChange={handleChange}\r\n                  disabled={isLoading}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Gender */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Gênero\"\r\n                htmlFor=\"person.gender\"\r\n                icon={<User size={16} />}\r\n              >\r\n                <ModuleSelect\r\n                  moduleColor=\"people\"\r\n                  id=\"person.gender\"\r\n                  name=\"person.gender\"\r\n                  value={formData.person.gender}\r\n                  onChange={handleChange}\r\n                  disabled={isLoading}\r\n                >\r\n                  <option value=\"\">Selecione</option>\r\n                  <option value=\"M\">Masculino</option>\r\n                  <option value=\"F\">Feminino</option>\r\n                  <option value=\"O\">Outro</option>\r\n                </ModuleSelect>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Person Email */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Email da Pessoa\"\r\n                htmlFor=\"person.email\"\r\n                icon={<Mail size={16} />}\r\n                error={errors[\"person.email\"]}\r\n                errorMessage={errors[\"person.email\"]}\r\n                helpText=\"Deixe em branco para usar o mesmo email da conta\"\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"person.email\"\r\n                  name=\"person.email\"\r\n                  value={formData.person.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading}\r\n                  error={!!errors[\"person.email\"]}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Phone */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Telefone\"\r\n                htmlFor=\"person.phone\"\r\n                icon={<Phone size={16} />}\r\n              >\r\n                <div className=\"relative\">\r\n                  <MaskedInput\r\n                    type=\"phone\"\r\n                    value={formData.person.phone}\r\n                    onChange={(e) =>\r\n                      handleChange({\r\n                        target: { name: \"person.phone\", value: e.target.value },\r\n                      })\r\n                    }\r\n                    placeholder=\"(00) 00000-0000\"\r\n                    className=\"w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none\"\r\n                    disabled={isLoading}\r\n                  />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Address */}\r\n              <div className=\"md:col-span-2\">\r\n              <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-4\">\r\n                <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                  <MapPin className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                  Endereço\r\n                </h3>\r\n              </div>\r\n                {console.log('Estado atual do formulário antes de renderizar AddressForm:', formData)}\r\n                <AddressForm\r\n                  formData={formData}\r\n                  setFormData={(newFormData) => {\r\n                    console.log('Atualizando formData no ClientFormModal:', newFormData);\r\n                    setFormData(newFormData);\r\n                  }}\r\n                  errors={errors}\r\n                  isLoading={isLoading}\r\n                  prefix=\"person.\"\r\n                  fieldMapping={{\r\n                    // Mapeamento personalizado para os campos da API ViaCEP\r\n                    logradouro: \"person.address\",\r\n                    bairro: \"person.neighborhood\",\r\n                    localidade: \"person.city\",\r\n                    uf: \"person.state\",\r\n                    cep: \"person.postalCode\"\r\n                  }}\r\n                  classes={{\r\n                    label: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\r\n                    input: \"block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\r\n                    error: \"mt-1 text-xs text-red-600 dark:text-red-400\",\r\n                    iconContainer: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              {/* Notes */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Observações\"\r\n                htmlFor=\"person.notes\"\r\n                icon={<FileText size={16} />}\r\n                className=\"md:col-span-2\"\r\n              >\r\n                <ModuleTextarea\r\n                  moduleColor=\"people\"\r\n                  id=\"person.notes\"\r\n                  name=\"person.notes\"\r\n                  value={formData.person.notes}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Observações adicionais\"\r\n                  rows={3}\r\n                  disabled={isLoading}\r\n                />\r\n              </ModuleFormGroup>\r\n            </div>\r\n      </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default ClientFormModal;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAFA;AAHA;AAAA;AADA;AAAA;AACA;AAAA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AADA;AAAA;AAAA;AACA;AAJA;;;;;;;;;;AAWA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,QAAQ;YACN,IAAI;YACJ,UAAU;YACV,KAAK;YACL,WAAW;YACX,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,cAAc;YACd,MAAM;YACN,OAAO;YACP,YAAY;YACZ,OAAO;QACT;IACF;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,QAAQ;YACpB,YAAY;YAEZ,iCAAiC;YACjC,IAAI,qBAAqB;YACzB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE;gBACtE,IAAI;oBACF,qBAAqB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,OAAO,CAAC,EAAE,CAAC,SAAS,GAAG;gBACrE,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF;YAEA,eAAe;YACf,IAAI,eAAe;YACnB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE;gBAChE,MAAM,WAAW,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO;gBACtD,eAAe,SAAS,OAAO,CAAC,gCAAgC;YAClE;YAEA,oBAAoB;YACpB,IAAI,iBAAiB;YACrB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;gBAClE,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;gBAC1D,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;YAC/D;YAEA,eAAe;YACf,IAAI,sBAAsB;YAC1B,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE;gBACvE,MAAM,kBAAkB,OAAO,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO;gBACpE,sBAAsB,gBAAgB,OAAO,CAAC,kBAAkB;YAClE;YAEA,YAAY;gBACV,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU;gBACV,iBAAiB;gBACjB,QAAQ;oBACN,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK;oBACvE,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK;oBACnF,KAAK,gBAAgB;oBACrB,WAAW;oBACX,OAAO,kBAAkB;oBACzB,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;oBAC7E,QAAQ,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK;oBAC/E,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK;oBACjF,cAAc,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,YAAY,IAAI,KAAK;oBAC3F,MAAM,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK;oBAC3E,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;oBAC7E,YAAY,uBAAuB;oBACnC,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;gBAC/E;YACF;QACF,OAAO,IAAI,QAAQ;YACjB,yCAAyC;YACzC,YAAY;YACZ;QACF;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,YAAY;QAChB,YAAY;YACV,OAAO;YACP,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,cAAc;gBACd,MAAM;gBACN,OAAO;gBACP,YAAY;gBACZ,OAAO;YACT;QACF;QACA,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC;QAEnB,oBAAoB;QACpB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,4CAA4C;QAC5C,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,UAAU,QAAQ,GAAG;YACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACvC,UAAU,QAAQ,GAAG;YACvB;YAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;gBAClD,UAAU,eAAe,GAAG;YAC9B;QACF,OAAO,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC5D,uEAAuE;YACvE,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAC9E,2DAA2D;YAC3D,UAAU,eAAe,GAAG;QAC9B;QAEA,oBAAoB;QACpB,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,EAAE;YAC7B,SAAS,CAAC,kBAAkB,GAAG;QACjC;QAEA,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;YACvB,MAAM,WAAW,SAAS,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO;YACpD,IAAI,SAAS,MAAM,KAAK,IAAI;gBAC1B,SAAS,CAAC,aAAa,GAAG;YAC5B;QACF;QAEA,IAAI,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,GAAG;YACxE,SAAS,CAAC,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,iDAAiD;QACjD,IAAI,KAAK,UAAU,CAAC,YAAY;YAC9B,MAAM,cAAc,KAAK,OAAO,CAAC,WAAW;YAC5C,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ;wBACN,GAAG,KAAK,MAAM;wBACd,CAAC,YAAY,EAAE;oBACjB;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAM,CAAC;QACnD;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACrD;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QAEb,IAAI;YACF,sBAAsB;YACtB,MAAM,aAAa;gBACjB,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,KAAK,SAAS,MAAM,CAAC,GAAG,GAAG,SAAS,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM;gBACpE,WAAW,SAAS,MAAM,CAAC,SAAS,IAAI;gBACxC,OAAO,SAAS,MAAM,CAAC,KAAK,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC1E,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;gBAChC,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI;gBAClC,SAAS,SAAS,MAAM,CAAC,OAAO,IAAI;gBACpC,cAAc,SAAS,MAAM,CAAC,YAAY,IAAI;gBAC9C,MAAM,SAAS,MAAM,CAAC,IAAI,IAAI;gBAC9B,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;gBAChC,YAAY,SAAS,MAAM,CAAC,UAAU,IAAI;gBAC1C,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;YAClC;YAEA,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,IAAI,UAAU;gBACZ,iCAAiC;gBACjC,MAAM,gBAAgB;oBACpB,OAAO,SAAS,KAAK;gBACvB;gBAEA,gCAAgC;gBAChC,IAAI,SAAS,QAAQ,EAAE;oBACrB,cAAc,QAAQ,GAAG,SAAS,QAAQ;gBAC5C;gBAEA,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBAE7C,sDAAsD;gBACtD,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE;oBACtB,IAAI;wBACF,0CAA0C;wBAC1C,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE;oBACxD,EAAE,OAAO,aAAa;wBACpB,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,MAAM;oBACR;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChC,MAAM,UAAU;oBACd,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,QAAQ;gBACV;gBAEA,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YACpC;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,+BAA+B;YAC/B,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,MAAM,YAAY,CAAC;gBACnB,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBACjC,SAAS,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG;gBAChC;gBACA,UAAU;YACZ,OAAO;gBACL,UAAU;oBACR,QAAQ,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC3C;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,6EAA6E;IAE7E,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;0BAEV,WAAW,cAAc;;;;;;;;;;;;IAKhC,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,WAAW,mBAAmB;QACrC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAc,UAAU;YAAc,WAAU;;gBAClD,OAAO,MAAM,kBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;sCACnB,8OAAC;sCAAM,OAAO,MAAM;;;;;;;;;;;;8BAIxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAsE;;;;;;;;;;;;8BAK1F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,KAAK;4BACnB,cAAc,OAAO,KAAK;sCAE1B,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,aAAY;gCACZ,UAAU,aAAa;gCACvB,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;;;;;;sCAKzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,KAAK;4BACnB,cAAc,OAAO,KAAK;sCAE1B,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;;;;;;sCAKzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAO,CAAC,MAAM,EAAE,CAAC,WAAW,MAAM,IAAI;4BACtC,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,QAAQ;4BACtB,cAAc,OAAO,QAAQ;4BAC7B,UAAU,WAAW,8CAA8C;sCAEnE,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAM,eAAe,SAAS;wCAC9B,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,aAAa,WAAW,aAAa;wCACrC,UAAU,CAAC;wCACX,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;wCACxB,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,6BACC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAElB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAO,CAAC,gBAAgB,EAAE,CAAC,WAAW,MAAM,IAAI;4BAChD,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,eAAe;4BAC7B,cAAc,OAAO,eAAe;sCAEpC,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAM,eAAe,SAAS;gCAC9B,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,eAAe;gCAC/B,UAAU;gCACV,aAAY;gCACZ,UAAU,CAAC;gCACX,UAAU;gCACV,OAAO,CAAC,CAAC,OAAO,eAAe;;;;;;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAsE;;;;;;;;;;;;8BAK1F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,MAAM,CAAC,kBAAkB;4BAChC,cAAc,MAAM,CAAC,kBAAkB;4BACvC,WAAU;sCAEV,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,QAAQ;gCAC/B,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,MAAM,CAAC,kBAAkB;;;;;;;;;;;sCAKtC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;4BACxB,OAAO,MAAM,CAAC,aAAa;4BAC3B,cAAc,MAAM,CAAC,aAAa;sCAElC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,OAAO,SAAS,MAAM,CAAC,GAAG;oCAC1B,UAAU,CAAC,IACT,aAAa;4CACX,QAAQ;gDAAE,MAAM;gDAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACtD;oCAEF,aAAY;oCACZ,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,aAAa,GAAG,uCAAuC,6CAA6C,2GAA2G,CAAC;oCAC9P,UAAU;;;;;;;;;;;;;;;;sCAMhB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,SAAS;gCAChC,UAAU;gCACV,UAAU;;;;;;;;;;;sCAKd,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;sCAElB,cAAA,8OAAC,kLAAA,CAAA,eAAY;gCACX,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,MAAM;gCAC7B,UAAU;gCACV,UAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;kDAClB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;kDAClB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;;;;;;;;;;;;sCAKtB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,MAAM,CAAC,eAAe;4BAC7B,cAAc,MAAM,CAAC,eAAe;4BACpC,UAAS;sCAET,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,KAAK;gCAC5B,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,MAAM,CAAC,eAAe;;;;;;;;;;;sCAKnC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;sCAEnB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,OAAO,SAAS,MAAM,CAAC,KAAK;oCAC5B,UAAU,CAAC,IACT,aAAa;4CACX,QAAQ;gDAAE,MAAM;gDAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACxD;oCAEF,aAAY;oCACZ,WAAU;oCACV,UAAU;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAsE;;;;;;;;;;;;gCAIzF,QAAQ,GAAG,CAAC,+DAA+D;8CAC5E,8OAAC,0IAAA,CAAA,UAAW;oCACV,UAAU;oCACV,aAAa,CAAC;wCACZ,QAAQ,GAAG,CAAC,4CAA4C;wCACxD,YAAY;oCACd;oCACA,QAAQ;oCACR,WAAW;oCACX,QAAO;oCACP,cAAc;wCACZ,wDAAwD;wCACxD,YAAY;wCACZ,QAAQ;wCACR,YAAY;wCACZ,IAAI;wCACJ,KAAK;oCACP;oCACA,SAAS;wCACP,OAAO;wCACP,OAAO;wCACP,OAAO;wCACP,eAAe;oCACjB;;;;;;;;;;;;sCAKJ,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sLAAA,CAAA,iBAAc;gCACb,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,KAAK;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5B;uCAEe"}}, {"offset": {"line": 7844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7850, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/InsuranceLimitFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { CreditCard, FileText, CreditCard as CardIcon } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { insuranceServiceLimitService } from \"@/app/modules/people/services/insuranceServiceLimitService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { serviceTypeService } from \"@/app/modules/scheduler/services/serviceTypeService\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\n\r\nconst InsuranceLimitFormModal = ({ isOpen, onClose, limit = null, onSuccess, personId = null }) => {\r\n  const { toast_success, toast_error } = useToast();\r\n  const [formData, setFormData] = useState({\r\n    personId: personId || \"\",\r\n    insuranceId: \"\",\r\n    serviceTypeId: \"\",\r\n    monthlyLimit: 0,\r\n    notes: \"\"\r\n  });\r\n  const [persons, setPersons] = useState([]);\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [serviceTypes, setServiceTypes] = useState([]);\r\n  const [personInsurances, setPersonInsurances] = useState([]);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isLoadingOptions, setIsLoadingOptions] = useState(false);\r\n\r\n  // Carregar dados iniciais\r\n  useEffect(() => {\r\n    loadOptions();\r\n  }, []);\r\n\r\n  // Carregar opções para os selects\r\n  const loadOptions = async () => {\r\n    setIsLoadingOptions(true);\r\n    try {\r\n      // Carregar pessoas (se não tiver personId fixo)\r\n      if (!personId) {\r\n        const personsData = await personsService.getPersons({ limit: 100 });\r\n        const personsArray = personsData?.persons || personsData?.people || personsData?.data || [];\r\n        setPersons(personsArray);\r\n      }\r\n\r\n      // Carregar tipos de serviço\r\n      const serviceTypesData = await serviceTypeService.getServiceTypes();\r\n      const serviceTypesArray = serviceTypesData?.serviceTypes || serviceTypesData?.data || [];\r\n      setServiceTypes(serviceTypesArray);\r\n\r\n      // Carregar convênios (todos)\r\n      const insurancesData = await insurancesService.getInsurances();\r\n      const insurancesArray = insurancesData?.insurances || insurancesData?.data || [];\r\n      setInsurances(insurancesArray);\r\n\r\n      // Se tiver personId, carregar convênios da pessoa\r\n      if (formData.personId) {\r\n        loadPersonInsurances(formData.personId);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções:\", error);\r\n      toast_error(\"Erro ao carregar opções. Por favor, tente novamente.\");\r\n    } finally {\r\n      setIsLoadingOptions(false);\r\n    }\r\n  };\r\n\r\n  // Carregar convênios da pessoa selecionada\r\n  const loadPersonInsurances = async (personId) => {\r\n    if (!personId) return;\r\n\r\n    try {\r\n      const data = await insurancesService.listPersonInsurances(personId);\r\n      const personInsurancesArray = data?.insurances || data || [];\r\n      setPersonInsurances(personInsurancesArray);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar convênios da pessoa:\", error);\r\n      toast_error(\"Erro ao carregar convênios da pessoa.\");\r\n    }\r\n  };\r\n\r\n  // Preencher formulário quando editando\r\n  useEffect(() => {\r\n    if (limit) {\r\n      setFormData({\r\n        personId: limit.personId || \"\",\r\n        insuranceId: limit.insuranceId || \"\",\r\n        serviceTypeId: limit.serviceTypeId || \"\",\r\n        monthlyLimit: limit.monthlyLimit || 0,\r\n        notes: limit.notes || \"\"\r\n      });\r\n\r\n      // Carregar convênios da pessoa se tiver personId\r\n      if (limit.personId) {\r\n        loadPersonInsurances(limit.personId);\r\n      }\r\n    } else if (personId) {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        personId\r\n      }));\r\n      loadPersonInsurances(personId);\r\n    }\r\n  }, [limit, personId]);\r\n\r\n  // Atualizar convênios quando a pessoa mudar\r\n  useEffect(() => {\r\n    if (formData.personId) {\r\n      loadPersonInsurances(formData.personId);\r\n    }\r\n  }, [formData.personId]);\r\n\r\n  // Manipuladores de eventos\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Converter para número quando for monthlyLimit\r\n    if (name === \"monthlyLimit\") {\r\n      setFormData({\r\n        ...formData,\r\n        [name]: parseInt(value, 10) || 0\r\n      });\r\n    } else {\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n\r\n    // Validar dados\r\n    if (!formData.personId || !formData.insuranceId || !formData.serviceTypeId) {\r\n      toast_error(\"Por favor, preencha todos os campos obrigatórios.\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (limit) {\r\n        // Modo de edição\r\n        await insuranceServiceLimitService.updateLimit(limit.id, {\r\n          monthlyLimit: formData.monthlyLimit,\r\n          notes: formData.notes\r\n        });\r\n        toast_success(\"Limite de convênio atualizado com sucesso\");\r\n      } else {\r\n        // Modo de adição\r\n        await insuranceServiceLimitService.createLimit(formData);\r\n        toast_success(\"Limite de convênio criado com sucesso\");\r\n      }\r\n\r\n      onSuccess();\r\n      onClose();\r\n    } catch (err) {\r\n      console.error(\"Erro ao salvar limite de convênio:\", err);\r\n      toast_error(err.response?.data?.message || \"Ocorreu um erro ao salvar o limite de convênio.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Normalizar dados de convênios da pessoa\r\n  const normalizePersonInsurances = () => {\r\n    if (!personInsurances || !Array.isArray(personInsurances) || personInsurances.length === 0) return [];\r\n\r\n    return personInsurances.map(ins => {\r\n      if (!ins) return null;\r\n      // Se o objeto já tiver a estrutura correta\r\n      if (ins.insurance) {\r\n        return {\r\n          id: ins.insurance.id,\r\n          name: ins.insurance.name\r\n        };\r\n      }\r\n      // Se for um objeto de convênio direto\r\n      else if (ins.id && ins.name) {\r\n        return {\r\n          id: ins.id,\r\n          name: ins.name\r\n        };\r\n      }\r\n      // Se tiver apenas o ID do convênio\r\n      else if (ins.insuranceId) {\r\n        // Buscar o nome no array de todos os convênios\r\n        const insuranceDetails = Array.isArray(insurances) ? insurances.find(i => i?.id === ins.insuranceId) : null;\r\n        return {\r\n          id: ins.insuranceId,\r\n          name: insuranceDetails?.name || `Convênio ${ins.insuranceId}`\r\n        };\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isSubmitting}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"insurance-limit-form\"\r\n        isLoading={isSubmitting}\r\n      >\r\n        {limit ? \"Atualizar\" : \"Salvar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={limit ? \"Editar Limite de Convênio\" : \"Novo Limite de Convênio\"}\r\n      icon={<CreditCard size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"md\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"insurance-limit-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6\">\r\n          <div>\r\n            <h4 className=\"text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4\">\r\n              <CreditCard className=\"w-4 h-4\" />\r\n              Informações do Limite\r\n            </h4>\r\n          </div>\r\n\r\n          {/* Seleção de Pessoa (se não tiver personId fixo) */}\r\n          {!personId && (\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Paciente\"\r\n              htmlFor=\"personId\"\r\n              icon={<CardIcon size={16} />}\r\n              required\r\n            >\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                id=\"personId\"\r\n                name=\"personId\"\r\n                value={formData.personId}\r\n                onChange={handleChange}\r\n                disabled={isLoadingOptions || isSubmitting || !!limit}\r\n                required\r\n                placeholder=\"Selecione um paciente\"\r\n              >\r\n                {Array.isArray(persons) && persons.map((person) => (\r\n                  <option key={person?.id} value={person?.id}>\r\n                    {person?.fullName || 'Sem nome'}\r\n                  </option>\r\n                ))}\r\n              </ModuleSelect>\r\n            </ModuleFormGroup>\r\n          )}\r\n\r\n          {/* Seleção de Convênio */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Convênio\"\r\n            htmlFor=\"insuranceId\"\r\n            icon={<CardIcon size={16} />}\r\n            required\r\n            helpText={formData.personId && normalizePersonInsurances().length === 0 ? \"Este paciente não possui convênios associados.\" : \"\"}\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"insuranceId\"\r\n              name=\"insuranceId\"\r\n              value={formData.insuranceId}\r\n              onChange={handleChange}\r\n              disabled={isLoadingOptions || isSubmitting || !formData.personId || !!limit}\r\n              required\r\n              placeholder=\"Selecione um convênio\"\r\n            >\r\n              {Array.isArray(normalizePersonInsurances()) && normalizePersonInsurances().map((insurance) => (\r\n                <option key={insurance?.id} value={insurance?.id}>\r\n                  {insurance?.name || 'Sem nome'}\r\n                </option>\r\n              ))}\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n\r\n          {/* Seleção de Tipo de Serviço */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Tipo de Serviço\"\r\n            htmlFor=\"serviceTypeId\"\r\n            icon={<FileText size={16} />}\r\n            required\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"serviceTypeId\"\r\n              name=\"serviceTypeId\"\r\n              value={formData.serviceTypeId}\r\n              onChange={handleChange}\r\n              disabled={isLoadingOptions || isSubmitting || !!limit}\r\n              required\r\n              placeholder=\"Selecione um tipo de serviço\"\r\n            >\r\n              {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (\r\n                <option key={serviceType?.id} value={serviceType?.id}>\r\n                  {serviceType?.name || 'Sem nome'}\r\n                </option>\r\n              ))}\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n\r\n          {/* Limite Mensal */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Limite Mensal\"\r\n            htmlFor=\"monthlyLimit\"\r\n            icon={<CardIcon size={16} />}\r\n            helpText=\"0 = ilimitado\"\r\n          >\r\n            <ModuleInput\r\n              moduleColor=\"people\"\r\n              type=\"number\"\r\n              id=\"monthlyLimit\"\r\n              name=\"monthlyLimit\"\r\n              value={formData.monthlyLimit}\r\n              onChange={handleChange}\r\n              min=\"0\"\r\n              disabled={isSubmitting}\r\n            />\r\n          </ModuleFormGroup>\r\n\r\n          {/* Observações */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Observações\"\r\n            htmlFor=\"notes\"\r\n            icon={<FileText size={16} />}\r\n          >\r\n            <ModuleTextarea\r\n              moduleColor=\"people\"\r\n              id=\"notes\"\r\n              name=\"notes\"\r\n              value={formData.notes}\r\n              onChange={handleChange}\r\n              rows={3}\r\n              disabled={isSubmitting}\r\n            />\r\n          </ModuleFormGroup>\r\n\r\n        </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default InsuranceLimitFormModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AAAA;AADA;AACA;AAAA;AADA;AACA;AAAA;;;;;;;;;;AAOA,MAAM,0BAA0B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,SAAS,EAAE,WAAW,IAAI,EAAE;IAC5F,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU,YAAY;QACtB,aAAa;QACb,eAAe;QACf,cAAc;QACd,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,cAAc;QAClB,oBAAoB;QACpB,IAAI;YACF,gDAAgD;YAChD,IAAI,CAAC,UAAU;gBACb,MAAM,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAI;gBACjE,MAAM,eAAe,aAAa,WAAW,aAAa,UAAU,aAAa,QAAQ,EAAE;gBAC3F,WAAW;YACb;YAEA,4BAA4B;YAC5B,MAAM,mBAAmB,MAAM,oKAAA,CAAA,qBAAkB,CAAC,eAAe;YACjE,MAAM,oBAAoB,kBAAkB,gBAAgB,kBAAkB,QAAQ,EAAE;YACxF,gBAAgB;YAEhB,6BAA6B;YAC7B,MAAM,iBAAiB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,aAAa;YAC5D,MAAM,kBAAkB,gBAAgB,cAAc,gBAAgB,QAAQ,EAAE;YAChF,cAAc;YAEd,kDAAkD;YAClD,IAAI,SAAS,QAAQ,EAAE;gBACrB,qBAAqB,SAAS,QAAQ;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,2CAA2C;IAC3C,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,OAAO,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;YAC1D,MAAM,wBAAwB,MAAM,cAAc,QAAQ,EAAE;YAC5D,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,YAAY;gBACV,UAAU,MAAM,QAAQ,IAAI;gBAC5B,aAAa,MAAM,WAAW,IAAI;gBAClC,eAAe,MAAM,aAAa,IAAI;gBACtC,cAAc,MAAM,YAAY,IAAI;gBACpC,OAAO,MAAM,KAAK,IAAI;YACxB;YAEA,iDAAiD;YACjD,IAAI,MAAM,QAAQ,EAAE;gBAClB,qBAAqB,MAAM,QAAQ;YACrC;QACF,OAAO,IAAI,UAAU;YACnB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP;gBACF,CAAC;YACD,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ,EAAE;YACrB,qBAAqB,SAAS,QAAQ;QACxC;IACF,GAAG;QAAC,SAAS,QAAQ;KAAC;IAEtB,2BAA2B;IAC3B,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,gDAAgD;QAChD,IAAI,SAAS,gBAAgB;YAC3B,YAAY;gBACV,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE,SAAS,OAAO,OAAO;YACjC;QACF,OAAO;YACL,YAAY;gBACV,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,gBAAgB;QAChB,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,aAAa,EAAE;YAC1E,YAAY;YACZ,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,IAAI,OAAO;gBACT,iBAAiB;gBACjB,MAAM,2KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;oBACvD,cAAc,SAAS,YAAY;oBACnC,OAAO,SAAS,KAAK;gBACvB;gBACA,cAAc;YAChB,OAAO;gBACL,iBAAiB;gBACjB,MAAM,2KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC;gBAC/C,cAAc;YAChB;YAEA;YACA;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,YAAY,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0CAA0C;IAC1C,MAAM,4BAA4B;QAChC,IAAI,CAAC,oBAAoB,CAAC,MAAM,OAAO,CAAC,qBAAqB,iBAAiB,MAAM,KAAK,GAAG,OAAO,EAAE;QAErG,OAAO,iBAAiB,GAAG,CAAC,CAAA;YAC1B,IAAI,CAAC,KAAK,OAAO;YACjB,2CAA2C;YAC3C,IAAI,IAAI,SAAS,EAAE;gBACjB,OAAO;oBACL,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,IAAI;gBAC1B;YACF,OAEK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;gBAC3B,OAAO;oBACL,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;gBAChB;YACF,OAEK,IAAI,IAAI,WAAW,EAAE;gBACxB,+CAA+C;gBAC/C,MAAM,mBAAmB,MAAM,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC,CAAA,IAAK,GAAG,OAAO,IAAI,WAAW,IAAI;gBACvG,OAAO;oBACL,IAAI,IAAI,WAAW;oBACnB,MAAM,kBAAkB,QAAQ,CAAC,SAAS,EAAE,IAAI,WAAW,EAAE;gBAC/D;YACF;YACA,OAAO;QACT,GAAG,MAAM,CAAC;IACZ;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;0BAEV,QAAQ,cAAc;;;;;;;;;;;;IAK7B,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,QAAQ,8BAA8B;QAC7C,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAuB,UAAU;YAAc,WAAU;;8BAC9D,8OAAC;8BACC,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;gBAMrC,CAAC,0BACA,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;8BAER,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,QAAQ;wBACxB,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,CAAC;wBAChD,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,YAAY,QAAQ,GAAG,CAAC,CAAC,uBACtC,8OAAC;gCAAwB,OAAO,QAAQ;0CACrC,QAAQ,YAAY;+BADV,QAAQ;;;;;;;;;;;;;;;8BAS7B,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;oBACR,UAAU,SAAS,QAAQ,IAAI,4BAA4B,MAAM,KAAK,IAAI,mDAAmD;8BAE7H,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,SAAS,QAAQ,IAAI,CAAC,CAAC;wBACtE,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,gCAAgC,4BAA4B,GAAG,CAAC,CAAC,0BAC9E,8OAAC;gCAA2B,OAAO,WAAW;0CAC3C,WAAW,QAAQ;+BADT,WAAW;;;;;;;;;;;;;;;8BAQ9B,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;8BAER,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,aAAa;wBAC7B,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,CAAC;wBAChD,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,iBAAiB,aAAa,GAAG,CAAC,CAAC,4BAChD,8OAAC;gCAA6B,OAAO,aAAa;0CAC/C,aAAa,QAAQ;+BADX,aAAa;;;;;;;;;;;;;;;8BAQhC,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,UAAS;8BAET,cAAA,8OAAC,gLAAA,CAAA,cAAW;wBACV,aAAY;wBACZ,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,YAAY;wBAC5B,UAAU;wBACV,KAAI;wBACJ,UAAU;;;;;;;;;;;8BAKd,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;8BAEtB,cAAA,8OAAC,sLAAA,CAAA,iBAAc;wBACb,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,MAAM;wBACN,UAAU;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe"}}, {"offset": {"line": 8314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8320, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/permissions/Protected.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { usePermissions } from '@/hooks/usePermissions';\r\n\r\n// Componente que renderiza condicionalmente baseado em permissões\r\nexport function Protected({ \r\n  permission, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { can, canAll, canAny } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (!permission) return true;\r\n    \r\n    if (Array.isArray(permission)) {\r\n      return requireAll ? canAll(permission) : canAny(permission);\r\n    }\r\n    \r\n    return can(permission);\r\n  }, [permission, requireAll, can, canAll, canAny]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza condicionalmente baseado em módulos\r\nexport function ProtectedModule({ \r\n  module, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { hasModule, isAdmin } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (isAdmin()) return true;\r\n    \r\n    if (!module) return true;\r\n    \r\n    if (Array.isArray(module)) {\r\n      return requireAll \r\n        ? module.every(m => hasModule(m))\r\n        : module.some(m => hasModule(m));\r\n    }\r\n    \r\n    return hasModule(module);\r\n  }, [module, requireAll, hasModule, isAdmin]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza apenas para administradores\r\nexport function AdminOnly({ fallback = null, children }) {\r\n  const { isAdmin } = usePermissions();\r\n  \r\n  return isAdmin() ? children : fallback;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAMO,SAAS,UAAU,EACxB,UAAU,EACV,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE7C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,MAAM,OAAO,CAAC,aAAa;YAC7B,OAAO,aAAa,OAAO,cAAc,OAAO;QAClD;QAEA,OAAO,IAAI;IACb,GAAG;QAAC;QAAY;QAAY;QAAK;QAAQ;KAAO;IAEhD,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,gBAAgB,EAC9B,MAAM,EACN,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE5C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,WAAW,OAAO;QAEtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,OAAO,aACH,OAAO,KAAK,CAAC,CAAA,IAAK,UAAU,MAC5B,OAAO,IAAI,CAAC,CAAA,IAAK,UAAU;QACjC;QAEA,OAAO,UAAU;IACnB,GAAG;QAAC;QAAQ;QAAY;QAAW;KAAQ;IAE3C,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,UAAU,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACrD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,YAAY,WAAW;AAChC"}}, {"offset": {"line": 8368, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}