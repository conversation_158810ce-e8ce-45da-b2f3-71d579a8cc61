#!/usr/bin/env node

// scripts/show-seed-commands.js - Exibe todos os comandos de seed disponíveis
console.log(`
🌱 HIGH TIDE SYSTEMS - COMANDOS DE SEED DISPONÍVEIS

═══════════════════════════════════════════════════════════════════════════════

📋 COMANDO ÚNICO (RECOMENDADO)
═══════════════════════════════════════════════════════════════════════════════

  Execução Local:
  node seed-all.js
  node seed-all.js --local

  Execução no Docker:
  node seed-all.js --docker

  Ajuda:
  node seed-all.js --help

═══════════════════════════════════════════════════════════════════════════════

🔄 SCRIPTS DE EXECUÇÃO COMPLETA
═══════════════════════════════════════════════════════════════════════════════

  Local:
  node scripts/run-all-seeds.js

  Docker:
  node scripts/run-all-seeds-docker.js

═══════════════════════════════════════════════════════════════════════════════

🎯 SCRIPTS INDIVIDUAIS (ORDEM CORRETA)
═══════════════════════════════════════════════════════════════════════════════

  1. Base (Empresas + Admin):
     node scripts/run-base-seed.js
     node prisma/seed.js

  2. Profissões:
     node scripts/run-professions-seed.js
     node prisma/seed-professions.js

  3. Usuários:
     node scripts/run-users-seed.js
     node prisma/seed-users.js

  4. Convênios:
     node scripts/run-insurance-seed.js
     node prisma/seed-insurances.js

  5. Limites de Convênio:
     node scripts/run-insurance-limits-seed.js
     node prisma/seed-insurance-limits.js

  6. Tipos de Serviço:
     node scripts/run-service-types-seed.js
     node prisma/seed-service-types.js

  7. Localizações:
     node scripts/run-locations-seed.js
     node prisma/seed-locations.js

  8. Clientes:
     node scripts/run-clients-seed.js
     node prisma/seed-clients.js

  9. Pacientes:
     node scripts/run-patients-seed.js
     node prisma/seed-patients.js

  10. Horários de Trabalho:
      node scripts/run-working-hours-seed.js
      node prisma/seed-working-hours.js

═══════════════════════════════════════════════════════════════════════════════

🐳 COMANDOS DOCKER INDIVIDUAIS
═══════════════════════════════════════════════════════════════════════════════

  docker exec -it high-tide-systems-api node prisma/seed.js
  docker exec -it high-tide-systems-api node prisma/seed-professions.js
  docker exec -it high-tide-systems-api node prisma/seed-users.js
  docker exec -it high-tide-systems-api node prisma/seed-insurances.js
  docker exec -it high-tide-systems-api node prisma/seed-insurance-limits.js
  docker exec -it high-tide-systems-api node prisma/seed-service-types.js
  docker exec -it high-tide-systems-api node prisma/seed-locations.js
  docker exec -it high-tide-systems-api node prisma/seed-clients.js
  docker exec -it high-tide-systems-api node prisma/seed-patients.js
  docker exec -it high-tide-systems-api node prisma/seed-working-hours.js

═══════════════════════════════════════════════════════════════════════════════

📚 DOCUMENTAÇÃO
═══════════════════════════════════════════════════════════════════════════════

  README-SEEDS.md                          - Guia principal
  scripts/README-ALL-SEEDS.md              - Execução completa
  scripts/README-ALL-SEEDS-DOCKER.md       - Execução no Docker
  scripts/README-BASE-SEED.md              - Seed base
  scripts/README-PROFESSIONS.md            - Seed de profissões
  scripts/README-USERS.md                  - Seed de usuários
  scripts/README-INSURANCES.md             - Seed de convênios
  scripts/README-INSURANCE-LIMITS.md       - Seed de limites
  scripts/README-SERVICE-TYPES.md          - Seed de tipos de serviço
  scripts/README-LOCATIONS.md              - Seed de localizações
  scripts/README-CLIENTS.md                - Seed de clientes
  scripts/README-PATIENTS.md               - Seed de pacientes
  scripts/README-WORKING-HOURS.md          - Seed de horários

═══════════════════════════════════════════════════════════════════════════════

⚠️  IMPORTANTE
═══════════════════════════════════════════════════════════════════════════════

  • Execute os seeds na ordem correta para respeitar dependências
  • Senha padrão para todos os usuários: 123456
  • Para Docker: certifique-se que o container está rodando
  • Para mais detalhes: node seed-all.js --help

═══════════════════════════════════════════════════════════════════════════════
`);
