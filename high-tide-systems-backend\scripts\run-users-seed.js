// scripts/run-users-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de usuários...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-users.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de usuários executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de usuários:', error);
  process.exit(1);
}
