// scripts/run-locations-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de localizações...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-locations.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de localizações executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de localizações:', error);
  process.exit(1);
}
