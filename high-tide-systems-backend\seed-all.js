#!/usr/bin/env node

// seed-all.js - Script de comando único para executar todos os seeds
const { execSync } = require('child_process');
const path = require('path');

// Função para exibir ajuda
function showHelp() {
  console.log(`
🌱 High Tide Systems - Script de Seeds

Uso: node seed-all.js [opção]

Opções:
  --local     Executa os seeds localmente (padrão)
  --docker    Executa os seeds dentro do container Docker
  --status    Verifica o status dos dados no banco
  --commands  Mostra todos os comandos disponíveis
  --help      Exibe esta ajuda

Exemplos:
  node seed-all.js
  node seed-all.js --local
  node seed-all.js --docker
  node seed-all.js --status
  node seed-all.js --commands

Ordem de execução dos seeds:
  1. Empresas e usuário admin
  2. Profissões
  3. Usuários
  4. Convênios
  5. Limites de convênio
  6. Tipos de serviço
  7. Localizações
  8. Clientes
  9. Pacientes
  10. Hor<PERSON><PERSON>s de trabalho

Para mais informações, consulte:
  - scripts/README-ALL-SEEDS.md
  - scripts/README-ALL-SEEDS-DOCKER.md
`);
}

// Função principal
function main() {
  const args = process.argv.slice(2);
  const option = args[0] || '--local';

  if (option === '--help' || option === '-h') {
    showHelp();
    return;
  }

  console.log('🌱 High Tide Systems - Executando Seeds\n');

  try {
    if (option === '--docker') {
      console.log('📦 Executando seeds no Docker...\n');
      execSync('node scripts/run-all-seeds-docker.js', {
        stdio: 'inherit',
        cwd: __dirname
      });
    } else if (option === '--status') {
      console.log('🔍 Verificando status dos dados...\n');
      execSync('node scripts/check-seed-status.js', {
        stdio: 'inherit',
        cwd: __dirname
      });
    } else if (option === '--commands') {
      execSync('node scripts/show-seed-commands.js', {
        stdio: 'inherit',
        cwd: __dirname
      });
    } else if (option === '--local' || !option.startsWith('--')) {
      console.log('💻 Executando seeds localmente...\n');
      execSync('node scripts/run-all-seeds.js', {
        stdio: 'inherit',
        cwd: __dirname
      });
    } else {
      console.error(`❌ Opção inválida: ${option}`);
      console.log('Use --help para ver as opções disponíveis.');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Erro ao executar:', error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { main, showHelp };
