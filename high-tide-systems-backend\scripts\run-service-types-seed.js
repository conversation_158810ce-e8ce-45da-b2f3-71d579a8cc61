// scripts/run-service-types-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de tipos de serviço...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-service-types.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de tipos de serviço executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de tipos de serviço:', error);
  process.exit(1);
}
