# Script para Execução de Todos os Seeds no Docker

Este script executa todos os arquivos de seed do sistema dentro do container Docker em uma ordem específica, garantindo que as dependências entre os dados sejam respeitadas.

## Características

- Executa todos os seeds em sequência dentro do container Docker
- Respeita a ordem correta de dependências entre os dados
- Exibe um resumo ao final com os seeds executados com sucesso e os que falharam
- Continua a execução mesmo se um seed falhar
- Não requer configuração local do Node.js ou Prisma

## Como Executar

```bash
node scripts/run-all-seeds-docker.js
```

## Pré-requisitos

- Docker deve estar rodando
- O container `high-tide-systems-api` deve estar ativo
- O banco de dados deve estar configurado e acessível pelo container

## Ordem de Execução dos Seeds

Os seeds são executados na seguinte ordem dentro do container:

1. **seed.js** - Seed base (empresas e usuário admin)
2. **seed-professions.js** - Seed de profissões
3. **seed-users.js** - Seed de usuários
4. **seed-insurances.js** - Seed de convênios
5. **seed-insurance-limits.js** - Seed de limites de convênio
6. **seed-service-types.js** - Seed de tipos de serviço
7. **seed-locations.js** - Seed de localizações
8. **seed-clients.js** - Seed de clientes
9. **seed-patients.js** - Seed de pacientes
10. **seed-working-hours.js** - Seed de horários de trabalho

## Executando Seeds Individualmente no Docker

Se algum seed falhar, você pode executá-lo individualmente dentro do container:

```bash
docker exec -it high-tide-systems-api node prisma/seed.js
docker exec -it high-tide-systems-api node prisma/seed-professions.js
docker exec -it high-tide-systems-api node prisma/seed-users.js
docker exec -it high-tide-systems-api node prisma/seed-insurances.js
docker exec -it high-tide-systems-api node prisma/seed-insurance-limits.js
docker exec -it high-tide-systems-api node prisma/seed-service-types.js
docker exec -it high-tide-systems-api node prisma/seed-locations.js
docker exec -it high-tide-systems-api node prisma/seed-clients.js
docker exec -it high-tide-systems-api node prisma/seed-patients.js
docker exec -it high-tide-systems-api node prisma/seed-working-hours.js
```

## Notas Importantes

- Certifique-se de que o container Docker está rodando antes de executar o script
- O script assume que o container se chama `high-tide-systems-api`
- Se o nome do container for diferente, edite o script para usar o nome correto
- Os seeds devem ser executados na ordem correta para respeitar as dependências entre os dados
- Alguns seeds dependem de dados criados por seeds anteriores (ex: usuários dependem de empresas e profissões)

## Troubleshooting

### Container não encontrado
Se você receber um erro sobre o container não ser encontrado:
1. Verifique se o Docker está rodando: `docker ps`
2. Verifique o nome correto do container: `docker ps -a`
3. Inicie o container se necessário: `docker start high-tide-systems-api`

### Erro de conexão com banco de dados
Se houver erros de conexão:
1. Verifique se o banco de dados está rodando
2. Verifique as variáveis de ambiente no container
3. Verifique se as migrações foram aplicadas: `docker exec -it high-tide-systems-api npx prisma migrate deploy`

### Dados duplicados
Se você executar os seeds múltiplas vezes:
- A maioria dos seeds verifica duplicidade e pula registros existentes
- Para limpar os dados e recomeçar, você pode resetar o banco de dados
