module.exports = {

"[project]/src/components/tutorial/TutorialTriggerButton.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/TutorialContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/circle-help.js [app-ssr] (ecmascript) <export default as HelpCircle>");
"use client";
;
;
;
;
/**
 * Botão para iniciar um tutorial
 * 
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.steps - Etapas do tutorial
 * @param {string} props.tutorialName - Nome único do tutorial
 * @param {boolean} props.showIfCompleted - Se deve mostrar o botão mesmo se o tutorial já foi concluído
 * @param {string} props.size - Tamanho do botão ('sm', 'md', 'lg')
 * @param {Object} props.className - Classes adicionais para o botão
 */ const TutorialTriggerButton = ({ steps, tutorialName, showIfCompleted = true, size = 'md', className = '', children })=>{
    const { startTutorial, isTutorialCompleted } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$TutorialContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTutorial"])();
    // Não renderizar o botão se o tutorial já foi concluído e showIfCompleted for falso
    if (!showIfCompleted && isTutorialCompleted(tutorialName)) {
        return null;
    }
    // Determinar tamanho do botão
    const sizeClasses = {
        sm: 'p-1.5 text-xs',
        md: 'p-2 text-sm',
        lg: 'p-2.5 text-base'
    };
    // Determinar tamanho do ícone
    const iconSize = {
        sm: 14,
        md: 16,
        lg: 20
    };
    const isCompleted = isTutorialCompleted(tutorialName);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: ()=>startTutorial(steps, tutorialName),
        className: `
        relative flex items-center gap-1.5 rounded-full 
        ${isCompleted ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : 'bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/40 dark:text-primary-300 dark:hover:bg-primary-800/60'}
        transition-colors ${sizeClasses[size]} ${className}
      `,
        "aria-label": `Abrir tutorial: ${tutorialName}`,
        title: isCompleted ? "Ver tutorial novamente" : "Ver tutorial",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$help$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__HelpCircle$3e$__["HelpCircle"], {
                size: iconSize[size]
            }, void 0, false, {
                fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
                lineNumber: 59,
                columnNumber: 7
            }, this),
            children,
            !isCompleted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-primary-500 dark:bg-primary-400"
            }, void 0, false, {
                fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
                lineNumber: 64,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/tutorial/TutorialTriggerButton.js",
        lineNumber: 49,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = TutorialTriggerButton;
}}),
"[project]/src/app/modules/people/services/insuranceServiceLimitService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "insuranceServiceLimitService": (()=>insuranceServiceLimitService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/apiResponseAdapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/services/exportService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-ssr] (ecmascript)");
;
;
;
;
;
// Adaptador para processar os dados de limites de serviço
const processServiceLimits = (limits)=>{
    if (!limits || !Array.isArray(limits)) {
        return [];
    }
    return limits.map((limit)=>{
        // Garantir que os campos estejam disponíveis independentemente do formato da resposta
        return {
            ...limit,
            // Usar os campos com letra maiúscula se disponíveis, caso contrário usar os campos com letra minúscula
            insurance: limit.insurance || {},
            serviceType: limit.serviceType || {},
            person: limit.person || {},
            // Adicionar referências normalizadas
            Insurance: limit.Insurance || limit.insurance || {},
            ServiceType: limit.ServiceType || limit.serviceType || {},
            Person: limit.Person || limit.person || {}
        };
    });
};
const insuranceServiceLimitService = {
    // Obter todos os limites com suporte a filtros
    getAllLimits: async (filters = {})=>{
        try {
            const { search, personId, personIds, insuranceId, serviceTypeId, companyId, sortField, sortDirection } = filters;
            // Construir parâmetros de consulta
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (personId) params.append('personId', personId);
            if (insuranceId) params.append('insuranceId', insuranceId);
            if (serviceTypeId) params.append('serviceTypeId', serviceTypeId);
            if (companyId) params.append('companyId', companyId);
            if (sortField) params.append('sortField', sortField);
            if (sortDirection) params.append('sortDirection', sortDirection);
            // Adicionar personIds como parâmetros separados com notação de array
            if (personIds && personIds.length > 0) {
                // Garantir que personIds seja um array
                const personIdsArray = Array.isArray(personIds) ? personIds : [
                    personIds
                ];
                // Adicionar cada ID como um parâmetro separado
                personIdsArray.forEach((id)=>{
                    // Usar o mesmo nome de parâmetro para cada ID (sem índice)
                    params.append('personIds', id);
                });
                console.log("Filtrando por múltiplos IDs de pacientes:", personIdsArray);
            } else {
                console.log("Nenhum filtro de paciente aplicado");
            }
            // Log para depuração dos parâmetros de ordenação
            if (sortField) {
                // Garantir que a direção seja uma string válida
                const validDirection = sortDirection && [
                    'asc',
                    'desc'
                ].includes(sortDirection.toLowerCase()) ? sortDirection.toLowerCase() : 'asc';
                console.log(`Parâmetros de ordenação: campo=${sortField}, direção=${validDirection}`);
                // Atualizar o parâmetro de direção com o valor normalizado
                params.set('sortDirection', validDirection);
            }
            // Não enviamos parâmetros de paginação para o backend
            // para garantir que recebemos todos os limites de uma vez
            console.log(`Enviando requisição para: /insurance-service-limits?${params.toString()}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurance-service-limits?${params.toString()}`);
            console.log("Resposta da API:", response.data);
            // Usar o adaptador para extrair os dados de forma consistente
            // Primeiro tentamos extrair usando o formato padrão
            const extracted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractData"])(response.data, 'limits', [
                'data'
            ]);
            // Se não houver limites no formato padrão, processamos o array diretamente
            if (extracted.limits && extracted.limits.length > 0) {
                return {
                    limits: processServiceLimits(extracted.limits),
                    total: extracted.total,
                    pages: extracted.pages
                };
            } else {
                // Processar o array diretamente se a API retornar apenas um array
                const processedLimits = processServiceLimits(response.data);
                return {
                    limits: processedLimits,
                    total: processedLimits.length,
                    pages: Math.ceil(processedLimits.length / 10)
                };
            }
        } catch (error) {
            console.error("Erro ao buscar limites de serviço:", error);
            throw error;
        }
    },
    // Obter limites para uma pessoa específica
    getLimitsByPerson: async (personId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurance-service-limits/person/${personId}`);
            return processServiceLimits(response.data);
        } catch (error) {
            console.error("Erro ao buscar limites de serviço:", error);
            throw error;
        }
    },
    // Obter limites para uma combinação pessoa+convênio
    getLimitsByPersonInsurance: async (personId, insuranceId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurance-service-limits/person/${personId}/insurance/${insuranceId}`);
            return processServiceLimits(response.data);
        } catch (error) {
            console.error("Erro ao buscar limites de serviço por convênio:", error);
            throw error;
        }
    },
    // Criar um novo limite
    createLimit: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/insurance-service-limits', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao criar limite de serviço:", error);
            throw error;
        }
    },
    // Atualizar um limite existente
    updateLimit: async (id, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/insurance-service-limits/${id}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar limite de serviço ${id}:`, error);
            throw error;
        }
    },
    // Excluir um limite
    deleteLimit: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/insurance-service-limits/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir limite de serviço ${id}:`, error);
            throw error;
        }
    },
    // Verificar se um agendamento está dentro dos limites
    checkAppointmentLimit: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/insurance-service-limits/check', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao verificar limite de agendamento:", error);
            throw error;
        }
    },
    /**
   * Exporta a lista de limites de convênio com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, personIds, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */ exportInsuranceLimits: async (filters, exportFormat = "xlsx")=>{
        try {
            // Obter os dados filtrados da API
            const response = await insuranceServiceLimitService.getAllLimits({
                ...filters
            });
            // Extrair os dados dos limites
            const limitsArray = response.limits || [];
            // Log para debug da estrutura dos dados
            console.log("Estrutura dos limites para exportação:", limitsArray);
            if (limitsArray.length > 0) {
                console.log("Exemplo do primeiro limite:", JSON.stringify(limitsArray[0], null, 2));
                console.log("Propriedades disponíveis:", Object.keys(limitsArray[0]));
            }
            // Se os limites não tiverem as informações completas, vamos tentar enriquecê-los
            // Verificar se temos objetos aninhados ou apenas IDs
            const needsEnrichment = limitsArray.length > 0 && (!limitsArray[0].Person && !limitsArray[0].person && !limitsArray[0].personFullName || !limitsArray[0].Insurance && !limitsArray[0].insurance && !limitsArray[0].insuranceName || !limitsArray[0].ServiceType && !limitsArray[0].serviceType && !limitsArray[0].serviceTypeName);
            // Se precisarmos enriquecer os dados, vamos buscar as informações faltantes
            if (needsEnrichment) {
                console.log("Dados incompletos detectados, buscando informações adicionais...");
                // Coletar todos os IDs únicos
                const personIds = [
                    ...new Set(limitsArray.filter((l)=>l.personId).map((l)=>l.personId))
                ];
                const insuranceIds = [
                    ...new Set(limitsArray.filter((l)=>l.insuranceId).map((l)=>l.insuranceId))
                ];
                const serviceTypeIds = [
                    ...new Set(limitsArray.filter((l)=>l.serviceTypeId).map((l)=>l.serviceTypeId))
                ];
                console.log("IDs para enriquecimento:", {
                    personIds,
                    insuranceIds,
                    serviceTypeIds
                });
                try {
                    // Buscar informações de pacientes se necessário
                    if (personIds.length > 0) {
                        const personsResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/persons', {
                            params: {
                                ids: personIds.join(',')
                            }
                        });
                        const persons = personsResponse.data || [];
                        // Criar um mapa de ID para objeto completo
                        const personsMap = Array.isArray(persons) ? persons.reduce((map, person)=>{
                            map[person.id] = person;
                            return map;
                        }, {}) : {};
                        // Adicionar informações aos limites
                        limitsArray.forEach((limit)=>{
                            if (limit.personId && personsMap[limit.personId]) {
                                limit.Person = personsMap[limit.personId];
                            }
                        });
                    }
                    // Buscar informações de convênios se necessário
                    if (insuranceIds.length > 0) {
                        const insurancesResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/insurances', {
                            params: {
                                ids: insuranceIds.join(',')
                            }
                        });
                        const insurances = insurancesResponse.data || [];
                        // Criar um mapa de ID para objeto completo
                        const insurancesMap = Array.isArray(insurances) ? insurances.reduce((map, insurance)=>{
                            map[insurance.id] = insurance;
                            return map;
                        }, {}) : {};
                        // Adicionar informações aos limites
                        limitsArray.forEach((limit)=>{
                            if (limit.insuranceId && insurancesMap[limit.insuranceId]) {
                                limit.Insurance = insurancesMap[limit.insuranceId];
                            }
                        });
                    }
                    // Buscar informações de tipos de serviço se necessário
                    if (serviceTypeIds.length > 0) {
                        const serviceTypesResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/service-types', {
                            params: {
                                ids: serviceTypeIds.join(',')
                            }
                        });
                        const serviceTypes = serviceTypesResponse.data || [];
                        // Criar um mapa de ID para objeto completo
                        const serviceTypesMap = Array.isArray(serviceTypes) ? serviceTypes.reduce((map, serviceType)=>{
                            map[serviceType.id] = serviceType;
                            return map;
                        }, {}) : {};
                        // Adicionar informações aos limites
                        limitsArray.forEach((limit)=>{
                            if (limit.serviceTypeId && serviceTypesMap[limit.serviceTypeId]) {
                                limit.ServiceType = serviceTypesMap[limit.serviceTypeId];
                            }
                        });
                    }
                    console.log("Dados enriquecidos com sucesso");
                } catch (error) {
                    console.error("Erro ao enriquecer dados em lote:", error);
                    // Tentar buscar individualmente para cada limite
                    console.log("Tentando buscar dados individualmente...");
                    // Criar funções para buscar entidades individuais
                    const fetchPerson = async (id)=>{
                        try {
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/persons/${id}`);
                            return response.data;
                        } catch (e) {
                            console.error(`Erro ao buscar pessoa ${id}:`, e);
                            return null;
                        }
                    };
                    const fetchInsurance = async (id)=>{
                        try {
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances/${id}`);
                            return response.data;
                        } catch (e) {
                            console.error(`Erro ao buscar convênio ${id}:`, e);
                            return null;
                        }
                    };
                    const fetchServiceType = async (id)=>{
                        try {
                            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/service-types/${id}`);
                            return response.data;
                        } catch (e) {
                            console.error(`Erro ao buscar tipo de serviço ${id}:`, e);
                            return null;
                        }
                    };
                    // Buscar dados para cada limite individualmente
                    for (const limit of limitsArray){
                        // Buscar pessoa se necessário
                        if (limit.personId && !limit.Person && !limit.person && !limit.personFullName) {
                            const person = await fetchPerson(limit.personId);
                            if (person) {
                                limit.Person = person;
                                console.log(`Pessoa ${limit.personId} encontrada individualmente`);
                            }
                        }
                        // Buscar convênio se necessário
                        if (limit.insuranceId && !limit.Insurance && !limit.insurance && !limit.insuranceName) {
                            const insurance = await fetchInsurance(limit.insuranceId);
                            if (insurance) {
                                limit.Insurance = insurance;
                                console.log(`Convênio ${limit.insuranceId} encontrado individualmente`);
                            }
                        }
                        // Buscar tipo de serviço se necessário
                        if (limit.serviceTypeId && !limit.ServiceType && !limit.serviceType && !limit.serviceTypeName) {
                            const serviceType = await fetchServiceType(limit.serviceTypeId);
                            if (serviceType) {
                                limit.ServiceType = serviceType;
                                console.log(`Tipo de serviço ${limit.serviceTypeId} encontrado individualmente`);
                            }
                        }
                    }
                }
            }
            // Timestamp atual para o subtítulo
            const timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "dd/MM/yyyy 'às' HH:mm", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ptBR"]
            });
            // Definição das colunas com formatação
            const columns = [
                {
                    key: "patient",
                    header: "Paciente"
                },
                {
                    key: "insurance",
                    header: "Convênio"
                },
                {
                    key: "company",
                    header: "Empresa"
                },
                {
                    key: "serviceType",
                    header: "Tipo de Serviço"
                },
                {
                    key: "monthlyLimit",
                    header: "Limite Mensal",
                    format: (value)=>value > 0 ? `${value}x por mês` : "Ilimitado"
                },
                {
                    key: "notes",
                    header: "Observações"
                },
                {
                    key: "createdAt",
                    header: "Data de Cadastro",
                    type: "date"
                }
            ];
            // Preparar os dados para exportação
            const preparedData = limitsArray.map((limit)=>{
                // Extrair informações do paciente
                let patientName = "N/A";
                if (limit.Person && limit.Person.fullName) {
                    patientName = limit.Person.fullName;
                } else if (limit.person && limit.person.fullName) {
                    patientName = limit.person.fullName;
                } else if (limit.personFullName) {
                    patientName = limit.personFullName;
                } else if (limit.personId) {
                    // Se tivermos apenas o ID, podemos tentar buscar o nome em outro lugar
                    patientName = `ID: ${limit.personId}`;
                }
                // Extrair informações do convênio
                let insuranceName = "N/A";
                if (limit.Insurance && limit.Insurance.name) {
                    insuranceName = limit.Insurance.name;
                } else if (limit.insurance && limit.insurance.name) {
                    insuranceName = limit.insurance.name;
                } else if (limit.insuranceName) {
                    insuranceName = limit.insuranceName;
                } else if (limit.insuranceId) {
                    insuranceName = `ID: ${limit.insuranceId}`;
                }
                // Extrair informações do tipo de serviço
                let serviceTypeName = "N/A";
                if (limit.ServiceType && limit.ServiceType.name) {
                    serviceTypeName = limit.ServiceType.name;
                } else if (limit.serviceType && limit.serviceType.name) {
                    serviceTypeName = limit.serviceType.name;
                } else if (limit.serviceTypeName) {
                    serviceTypeName = limit.serviceTypeName;
                } else if (limit.serviceTypeId) {
                    serviceTypeName = `ID: ${limit.serviceTypeId}`;
                }
                // Extrair informações da empresa
                let companyName = "";
                if (limit.Insurance?.company?.name) {
                    companyName = limit.Insurance.company.name;
                } else if (limit.insurance?.company?.name) {
                    companyName = limit.insurance.company.name;
                }
                return {
                    patient: patientName,
                    insurance: insuranceName,
                    company: companyName,
                    serviceType: serviceTypeName,
                    monthlyLimit: limit.monthlyLimit || 0,
                    notes: limit.notes || "",
                    createdAt: limit.createdAt || ""
                };
            });
            // Filtros aplicados para subtítulo
            let subtitleParts = [];
            if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
            if (filters.personIds && filters.personIds.length > 0) {
                subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);
            }
            if (filters.insuranceId) {
                subtitleParts.push(`Convênio: ${filters.insuranceName || filters.insuranceId}`);
            }
            if (filters.serviceTypeId) {
                subtitleParts.push(`Tipo de Serviço: ${filters.serviceTypeName || filters.serviceTypeId}`);
            }
            if (filters.companyId) {
                // Tentar encontrar o nome da empresa nos dados
                const companyName = limitsArray.find((l)=>l.Insurance?.company?.id === filters.companyId || l.insurance?.company?.id === filters.companyId)?.Insurance?.company?.name || limitsArray.find((l)=>l.Insurance?.company?.id === filters.companyId || l.insurance?.company?.id === filters.companyId)?.insurance?.company?.name;
                subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);
            }
            // Construir o subtítulo
            let subtitle = `Exportado em: ${timestamp}`;
            if (subtitleParts.length > 0) {
                subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
            }
            // Exportar os dados
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportService"].exportData(preparedData, {
                format: exportFormat,
                filename: "limites-convenio",
                columns,
                title: "Lista de Limites de Convênio",
                subtitle
            });
        } catch (error) {
            console.error("Erro ao exportar limites de convênio:", error);
            return false;
        }
    }
};
const __TURBOPACK__default__export__ = insuranceServiceLimitService;
}}),
"[project]/src/app/modules/people/services/insurancesService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// app/modules/people/services/insurancesService.js
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "insurancesService": (()=>insurancesService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/apiResponseAdapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/services/exportService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-ssr] (ecmascript)");
;
;
;
;
;
const insurancesService = {
    // Obter lista de convênios (com suporte a filtragem)
    getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {})=>{
        try {
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (companyId) params.append('companyId', companyId);
            if (page) params.append('page', page);
            if (limit) params.append('limit', limit);
            // Adicionar insuranceIds como parâmetros separados com notação de array
            if (insuranceIds && insuranceIds.length > 0) {
                // Garantir que insuranceIds seja um array
                const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [
                    insuranceIds
                ];
                // Adicionar cada ID como um parâmetro separado
                insuranceIdsArray.forEach((id, index)=>{
                    // Usar a notação de array para compatibilidade com a API
                    params.append(`insuranceIds[${index}]`, id);
                });
                console.log("Filtrando por múltiplos IDs de convênios:", insuranceIdsArray);
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances?${params.toString()}`);
            // Usar o adaptador para extrair os dados de forma consistente
            // Primeiro tentamos extrair usando o formato padrão
            const extracted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiResponseAdapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractData"])(response.data, 'insurances', [
                'data'
            ]);
            // Se não houver insurances no formato padrão, processamos o array diretamente
            if (extracted.insurances && extracted.insurances.length > 0) {
                return extracted;
            } else {
                // Processar o array diretamente se a API retornar apenas um array
                const insurances = Array.isArray(response.data) ? response.data : [];
                return {
                    insurances,
                    total: insurances.length,
                    pages: Math.ceil(insurances.length / limit)
                };
            }
        } catch (error) {
            console.error("Erro ao buscar convênios:", error);
            throw error;
        }
    },
    // Obter um convênio específico
    getInsurance: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar convênio ${id}:`, error);
            throw error;
        }
    },
    // Obter um convênio pelo ID (versão simplificada que não lança erro)
    getInsuranceById: async (id)=>{
        try {
            console.log(`Buscando convênio com ID: ${id}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar convênio com ID ${id}:`, error);
            return null;
        }
    },
    // Criar um novo convênio
    createInsurance: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/insurances', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao criar convênio:", error);
            throw error;
        }
    },
    // Atualizar um convênio existente
    updateInsurance: async (id, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/insurances/${id}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar convênio ${id}:`, error);
            throw error;
        }
    },
    // Excluir um convênio
    deleteInsurance: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/insurances/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir convênio ${id}:`, error);
            throw error;
        }
    },
    // Adicionar um convênio a uma pessoa
    addPersonInsurance: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/insurances/person', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao adicionar convênio à pessoa:", error);
            throw error;
        }
    },
    // Remover um convênio de uma pessoa
    removePersonInsurance: async (personId, insuranceId)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/insurances/person/${personId}/${insuranceId}`);
            return true;
        } catch (error) {
            console.error("Erro ao remover convênio da pessoa:", error);
            throw error;
        }
    },
    // Listar convênios de uma pessoa - VERSÃO MELHORADA
    listPersonInsurances: async (personId)=>{
        try {
            console.log(`Buscando convênios para a pessoa ID: ${personId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances/person/${personId}`);
            // Detecta a estrutura retornada e normaliza
            const data = response.data;
            // Log para debug da estrutura de dados
            console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);
            // Se for array, retorna diretamente
            if (Array.isArray(data)) {
                console.log(`Encontrados ${data.length} convênios no formato de array`);
                return data;
            } else if (data && typeof data === 'object') {
                // Tenta encontrar um array dentro do objeto
                const possibleArrayProps = [
                    'personInsurances',
                    'insurances',
                    'data',
                    'items',
                    'results'
                ];
                // Primeiro procura nas propriedades comuns
                for (const prop of possibleArrayProps){
                    if (Array.isArray(data[prop])) {
                        console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
                        return data[prop];
                    }
                }
                // Se não encontrou nas propriedades comuns, verifica todas as propriedades
                for (const prop of Object.keys(data)){
                    if (Array.isArray(data[prop])) {
                        console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
                        return data[prop];
                    }
                }
                // Tenta extrair informações de convênios mesmo que não estejam em um array direto
                if (data.insurance || data.insuranceId) {
                    console.log(`Encontrado um único convênio em formato não-array`);
                    const insurance = {
                        id: data.insuranceId || data.insurance?.id,
                        name: data.insuranceName || data.insurance?.name,
                        policyNumber: data.policyNumber,
                        validUntil: data.validUntil
                    };
                    return [
                        insurance
                    ];
                }
            }
            // Se não conseguir encontrar nenhum array, retorna array vazio
            console.warn("Estrutura de resposta inesperada:", data);
            return [];
        } catch (error) {
            console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);
            // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX
            return [];
        }
    },
    /**
   * Exporta a lista de convênios com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */ exportInsurances: async (filters, exportFormat = "xlsx")=>{
        try {
            // Obter os dados filtrados da API
            const response = await insurancesService.getInsurances({
                ...filters,
                limit: 1000 // Aumentamos o limite para exportar mais dados
            });
            // Extrair os dados dos convênios
            const insurancesArray = response.insurances || [];
            // Timestamp atual para o subtítulo
            const timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "dd/MM/yyyy 'às' HH:mm", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ptBR"]
            });
            // Definição das colunas com formatação
            const columns = [
                {
                    key: "name",
                    header: "Nome"
                },
                {
                    key: "companyName",
                    header: "Empresa"
                },
                {
                    key: "createdAt",
                    header: "Data de Cadastro",
                    type: "date"
                }
            ];
            // Preparar os dados para exportação
            const preparedData = insurancesArray.map((insurance)=>{
                return {
                    name: insurance.name || "",
                    companyName: insurance.company && insurance.company.name ? insurance.company.name : "",
                    createdAt: insurance.createdAt || ""
                };
            });
            // Filtros aplicados para subtítulo
            let subtitleParts = [];
            if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
            if (filters.insuranceIds && filters.insuranceIds.length > 0) {
                subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);
            }
            if (filters.companyId) {
                // Tentar encontrar o nome da empresa nos dados
                const companyName = insurancesArray.find((i)=>i.company && i.company.id === filters.companyId)?.company?.name;
                subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);
            }
            // Construir o subtítulo
            let subtitle = `Exportado em: ${timestamp}`;
            if (subtitleParts.length > 0) {
                subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
            }
            // Exportar os dados
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportService"].exportData(preparedData, {
                format: exportFormat,
                filename: "convenios",
                columns,
                title: "Lista de Convênios",
                subtitle
            });
        } catch (error) {
            console.error("Erro ao exportar convênios:", error);
            return false;
        }
    },
    updatePersonInsurance: async (personId, insuranceId, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/insurances/person/${personId}/${insuranceId}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);
            throw error;
        }
    },
    // Obter detalhes de um convênio específico de uma pessoa
    getPersonInsurance: async (personId, insuranceId)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/insurances/person/${personId}/${insuranceId}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = insurancesService;
}}),
"[project]/src/app/modules/scheduler/services/serviceTypeService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__),
    "serviceTypeService": (()=>serviceTypeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/services/exportService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/date-fns/locale/pt-BR.js [app-ssr] (ecmascript)");
;
;
;
;
const serviceTypeService = {
    // Listar tipos de serviço com suporte a filtros
    getServiceTypes: async ({ search, companyId, serviceTypeIds } = {})=>{
        try {
            const params = new URLSearchParams();
            if (search) params.append('search', search);
            if (companyId) params.append('companyId', companyId);
            // Adicionar suporte para múltiplos IDs de tipos de serviço
            if (serviceTypeIds && Array.isArray(serviceTypeIds) && serviceTypeIds.length > 0) {
                serviceTypeIds.forEach((id)=>params.append('serviceTypeIds', id));
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/service-types?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error("Erro ao buscar tipos de serviço:", error);
            throw error;
        }
    },
    // Obter um tipo de serviço específico
    getServiceType: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/service-types/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar tipo de serviço ${id}:`, error);
            throw error;
        }
    },
    // Criar um novo tipo de serviço
    createServiceType: async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/service-types', data);
            return response.data;
        } catch (error) {
            console.error("Erro ao criar tipo de serviço:", error);
            throw error;
        }
    },
    // Atualizar um tipo de serviço existente
    updateServiceType: async (id, data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/service-types/${id}`, data);
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar tipo de serviço ${id}:`, error);
            throw error;
        }
    },
    // Excluir um tipo de serviço
    deleteServiceType: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/service-types/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir tipo de serviço ${id}:`, error);
            throw error;
        }
    },
    /**
 * Exporta a lista de tipos de serviço com os filtros aplicados
 * @param {Object} filters - Filtros atuais (busca, companyId, etc)
 * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
 * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
 */ exportServiceTypes: async (filters, exportFormat = "xlsx")=>{
        try {
            // Obter os dados filtrados da API
            const response = await serviceTypeService.getServiceTypes(filters);
            // Extrair os dados dos tipos de serviço
            const data = response?.serviceTypes || [];
            // Timestamp atual para o subtítulo
            const timestamp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "dd/MM/yyyy 'às' HH:mm", {
                locale: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$locale$2f$pt$2d$BR$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ptBR"]
            });
            // Definição das colunas com formatação
            const columns = [
                {
                    key: "name",
                    header: "Nome do Serviço"
                },
                {
                    key: "value",
                    header: "Valor",
                    format: (value)=>new Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL"
                        }).format(value),
                    align: "right",
                    width: 25
                },
                {
                    key: "companyName",
                    header: "Empresa",
                    format: (value, item)=>item.company ? item.company.name : "N/A"
                },
                {
                    key: "createdAt",
                    header: "Data de Cadastro",
                    type: "date"
                }
            ];
            // Preparar os dados para exportação
            const preparedData = data.map((serviceType)=>{
                return {
                    name: serviceType.name || "",
                    value: serviceType.value || 0,
                    companyName: serviceType.company ? serviceType.company.name : "N/A",
                    createdAt: serviceType.createdAt || ""
                };
            });
            // Filtros aplicados para subtítulo
            let subtitleParts = [];
            if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
            if (filters.companyId) {
                const companyName = data.length > 0 && data[0].company ? data[0].company.name : "Selecionada";
                subtitleParts.push(`Empresa: ${companyName}`);
            }
            if (filters.serviceTypeIds && Array.isArray(filters.serviceTypeIds) && filters.serviceTypeIds.length > 0) {
                subtitleParts.push(`Tipos de Serviço: ${filters.serviceTypeIds.length} selecionados`);
            }
            // Construir o subtítulo
            let subtitle = `Exportado em: ${timestamp}`;
            if (subtitleParts.length > 0) {
                subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
            }
            // Exportar os dados
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$services$2f$exportService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["exportService"].exportData(preparedData, {
                format: exportFormat,
                filename: "tipos-de-servico",
                columns,
                title: "Lista de Tipos de Serviço",
                subtitle
            });
        } catch (error) {
            console.error("Erro ao exportar tipos de serviço:", error);
            return false;
        }
    }
};
const __TURBOPACK__default__export__ = serviceTypeService;
}}),
"[project]/src/app/modules/admin/services/companyService.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "companyService": (()=>companyService),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
;
const companyService = {
    // Obter a empresa atual do usuário autenticado
    getCurrentCompany: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get('/companies/current');
            return response.data;
        } catch (error) {
            console.error("Erro ao buscar empresa atual:", error);
            throw error;
        }
    },
    // Listar empresas com suporte a paginação e filtros
    getCompanies: async ({ page = 1, limit = 10, search, active } = {})=>{
        try {
            const params = new URLSearchParams();
            if (page) params.append('page', page);
            if (limit) params.append('limit', limit);
            if (search) params.append('search', search);
            if (active !== undefined) params.append('active', active);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/companies?${params.toString()}`);
            return response.data;
        } catch (error) {
            console.error("Erro ao buscar empresas:", error);
            throw error;
        }
    },
    // Obter uma empresa específica
    getCompany: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/companies/${id}`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao buscar empresa ${id}:`, error);
            throw error;
        }
    },
    // Obter lista de empresas para formulários de seleção
    getCompaniesForSelect: async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get("/companies/select");
            return response.data.companies || [];
        } catch (error) {
            console.error("Erro ao buscar empresas para seleção:", error);
            // Retorna array vazio em caso de erro para facilitar o manuseio no frontend
            return [];
        }
    },
    // Criar uma nova empresa
    createCompany: async (formData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].post('/companies', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error("Erro ao criar empresa:", error);
            throw error;
        }
    },
    // Atualizar uma empresa existente
    updateCompany: async (id, formData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].put(`/companies/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });
            return response.data;
        } catch (error) {
            console.error(`Erro ao atualizar empresa ${id}:`, error);
            throw error;
        }
    },
    // Alternar o status de uma empresa (ativo/inativo)
    toggleCompanyStatus: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].patch(`/companies/${id}/status`);
            return response.data;
        } catch (error) {
            console.error(`Erro ao alterar status da empresa ${id}:`, error);
            throw error;
        }
    },
    // Excluir uma empresa
    deleteCompany: async (id)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].delete(`/companies/${id}`);
            return true;
        } catch (error) {
            console.error(`Erro ao excluir empresa ${id}:`, error);
            throw error;
        }
    },
    // Obter o logo de uma empresa
    getCompanyLogo: async (id)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["api"].get(`/companies/${id}/logo`, {
                responseType: 'blob'
            });
            return URL.createObjectURL(response.data);
        } catch (error) {
            console.error(`Erro ao obter logo da empresa ${id}:`, error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = companyService;
}}),
"[project]/src/components/ui/ModalButton.js [app-ssr] (ecmascript) <export default as ModalButton>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModalButton": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModalButton.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleFormGroup.js [app-ssr] (ecmascript) <export default as ModuleFormGroup>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleFormGroup": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleFormGroup.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/ui/ModuleTextarea.js [app-ssr] (ecmascript) <export default as ModuleTextarea>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleTextarea": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTextarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTextarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleTextarea.js [app-ssr] (ecmascript)");
}}),
"[project]/src/components/people/InsuranceLimitFormModal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insuranceServiceLimitService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/personsService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insurancesService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$scheduler$2f$services$2f$serviceTypeService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/scheduler/services/serviceTypeService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/ToastContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__ = __turbopack_import__("[project]/src/components/ui/ModalButton.js [app-ssr] (ecmascript) <export default as ModalButton>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleModal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleModal$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleModal.js [app-ssr] (ecmascript) <export default as ModuleModal>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-ssr] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleFormGroup.js [app-ssr] (ecmascript) <export default as ModuleFormGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleSelect.js [app-ssr] (ecmascript) <export default as ModuleSelect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleInput.js [app-ssr] (ecmascript) <export default as ModuleInput>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTextarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTextarea$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleTextarea.js [app-ssr] (ecmascript) <export default as ModuleTextarea>");
;
;
;
;
;
;
;
;
;
const InsuranceLimitFormModal = ({ isOpen, onClose, limit = null, onSuccess, personId = null })=>{
    const { toast_success, toast_error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        personId: personId || "",
        insuranceId: "",
        serviceTypeId: "",
        monthlyLimit: 0,
        notes: ""
    });
    const [persons, setPersons] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [insurances, setInsurances] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [serviceTypes, setServiceTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [personInsurances, setPersonInsurances] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingOptions, setIsLoadingOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Carregar dados iniciais
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadOptions();
    }, []);
    // Carregar opções para os selects
    const loadOptions = async ()=>{
        setIsLoadingOptions(true);
        try {
            // Carregar pessoas (se não tiver personId fixo)
            if (!personId) {
                const personsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["personsService"].getPersons({
                    limit: 100
                });
                const personsArray = personsData?.persons || personsData?.people || personsData?.data || [];
                setPersons(personsArray);
            }
            // Carregar tipos de serviço
            const serviceTypesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$scheduler$2f$services$2f$serviceTypeService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceTypeService"].getServiceTypes();
            const serviceTypesArray = serviceTypesData?.serviceTypes || serviceTypesData?.data || [];
            setServiceTypes(serviceTypesArray);
            // Carregar convênios (todos)
            const insurancesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insurancesService"].getInsurances();
            const insurancesArray = insurancesData?.insurances || insurancesData?.data || [];
            setInsurances(insurancesArray);
            // Se tiver personId, carregar convênios da pessoa
            if (formData.personId) {
                loadPersonInsurances(formData.personId);
            }
        } catch (error) {
            console.error("Erro ao carregar opções:", error);
            toast_error("Erro ao carregar opções. Por favor, tente novamente.");
        } finally{
            setIsLoadingOptions(false);
        }
    };
    // Carregar convênios da pessoa selecionada
    const loadPersonInsurances = async (personId)=>{
        if (!personId) return;
        try {
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insurancesService"].listPersonInsurances(personId);
            const personInsurancesArray = data?.insurances || data || [];
            setPersonInsurances(personInsurancesArray);
        } catch (error) {
            console.error("Erro ao carregar convênios da pessoa:", error);
            toast_error("Erro ao carregar convênios da pessoa.");
        }
    };
    // Preencher formulário quando editando
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (limit) {
            setFormData({
                personId: limit.personId || "",
                insuranceId: limit.insuranceId || "",
                serviceTypeId: limit.serviceTypeId || "",
                monthlyLimit: limit.monthlyLimit || 0,
                notes: limit.notes || ""
            });
            // Carregar convênios da pessoa se tiver personId
            if (limit.personId) {
                loadPersonInsurances(limit.personId);
            }
        } else if (personId) {
            setFormData((prev)=>({
                    ...prev,
                    personId
                }));
            loadPersonInsurances(personId);
        }
    }, [
        limit,
        personId
    ]);
    // Atualizar convênios quando a pessoa mudar
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (formData.personId) {
            loadPersonInsurances(formData.personId);
        }
    }, [
        formData.personId
    ]);
    // Manipuladores de eventos
    const handleChange = (e)=>{
        const { name, value } = e.target;
        // Converter para número quando for monthlyLimit
        if (name === "monthlyLimit") {
            setFormData({
                ...formData,
                [name]: parseInt(value, 10) || 0
            });
        } else {
            setFormData({
                ...formData,
                [name]: value
            });
        }
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        setIsSubmitting(true);
        // Validar dados
        if (!formData.personId || !formData.insuranceId || !formData.serviceTypeId) {
            toast_error("Por favor, preencha todos os campos obrigatórios.");
            setIsSubmitting(false);
            return;
        }
        try {
            if (limit) {
                // Modo de edição
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insuranceServiceLimitService"].updateLimit(limit.id, {
                    monthlyLimit: formData.monthlyLimit,
                    notes: formData.notes
                });
                toast_success("Limite de convênio atualizado com sucesso");
            } else {
                // Modo de adição
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insuranceServiceLimitService"].createLimit(formData);
                toast_success("Limite de convênio criado com sucesso");
            }
            onSuccess();
            onClose();
        } catch (err) {
            console.error("Erro ao salvar limite de convênio:", err);
            toast_error(err.response?.data?.message || "Ocorreu um erro ao salvar o limite de convênio.");
        } finally{
            setIsSubmitting(false);
        }
    };
    // Normalizar dados de convênios da pessoa
    const normalizePersonInsurances = ()=>{
        if (!personInsurances || !Array.isArray(personInsurances) || personInsurances.length === 0) return [];
        return personInsurances.map((ins)=>{
            if (!ins) return null;
            // Se o objeto já tiver a estrutura correta
            if (ins.insurance) {
                return {
                    id: ins.insurance.id,
                    name: ins.insurance.name
                };
            } else if (ins.id && ins.name) {
                return {
                    id: ins.id,
                    name: ins.name
                };
            } else if (ins.insuranceId) {
                // Buscar o nome no array de todos os convênios
                const insuranceDetails = Array.isArray(insurances) ? insurances.find((i)=>i?.id === ins.insuranceId) : null;
                return {
                    id: ins.insuranceId,
                    name: insuranceDetails?.name || `Convênio ${ins.insuranceId}`
                };
            }
            return null;
        }).filter(Boolean);
    };
    // Componente de rodapé com botões
    const modalFooter = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex justify-end gap-3",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__["ModalButton"], {
                variant: "secondary",
                moduleColor: "people",
                onClick: onClose,
                disabled: isSubmitting,
                children: "Cancelar"
            }, void 0, false, {
                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                lineNumber: 198,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModalButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModalButton$3e$__["ModalButton"], {
                variant: "primary",
                moduleColor: "people",
                type: "submit",
                form: "insurance-limit-form",
                isLoading: isSubmitting,
                children: limit ? "Atualizar" : "Salvar"
            }, void 0, false, {
                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                lineNumber: 207,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
        lineNumber: 197,
        columnNumber: 5
    }, this);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleModal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleModal$3e$__["ModuleModal"], {
        isOpen: isOpen,
        onClose: onClose,
        title: limit ? "Editar Limite de Convênio" : "Novo Limite de Convênio",
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
            size: 22
        }, void 0, false, {
            fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
            lineNumber: 224,
            columnNumber: 13
        }, void 0),
        moduleColor: "people",
        size: "md",
        animateExit: true,
        footer: modalFooter,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            id: "insurance-limit-form",
            onSubmit: handleSubmit,
            className: "overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                className: "w-4 h-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                                lineNumber: 233,
                                columnNumber: 15
                            }, this),
                            "Informações do Limite"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 232,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 231,
                    columnNumber: 11
                }, this),
                !personId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Paciente",
                    htmlFor: "personId",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 244,
                        columnNumber: 21
                    }, void 0),
                    required: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                        moduleColor: "people",
                        id: "personId",
                        name: "personId",
                        value: formData.personId,
                        onChange: handleChange,
                        disabled: isLoadingOptions || isSubmitting || !!limit,
                        required: true,
                        placeholder: "Selecione um paciente",
                        children: Array.isArray(persons) && persons.map((person)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: person?.id,
                                children: person?.fullName || 'Sem nome'
                            }, person?.id, false, {
                                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                                lineNumber: 258,
                                columnNumber: 19
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 247,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 240,
                    columnNumber: 13
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Convênio",
                    htmlFor: "insuranceId",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 271,
                        columnNumber: 19
                    }, void 0),
                    required: true,
                    helpText: formData.personId && normalizePersonInsurances().length === 0 ? "Este paciente não possui convênios associados." : "",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                        moduleColor: "people",
                        id: "insuranceId",
                        name: "insuranceId",
                        value: formData.insuranceId,
                        onChange: handleChange,
                        disabled: isLoadingOptions || isSubmitting || !formData.personId || !!limit,
                        required: true,
                        placeholder: "Selecione um convênio",
                        children: Array.isArray(normalizePersonInsurances()) && normalizePersonInsurances().map((insurance)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: insurance?.id,
                                children: insurance?.name || 'Sem nome'
                            }, insurance?.id, false, {
                                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                                lineNumber: 286,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 275,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 267,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Tipo de Serviço",
                    htmlFor: "serviceTypeId",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 298,
                        columnNumber: 19
                    }, void 0),
                    required: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                        moduleColor: "people",
                        id: "serviceTypeId",
                        name: "serviceTypeId",
                        value: formData.serviceTypeId,
                        onChange: handleChange,
                        disabled: isLoadingOptions || isSubmitting || !!limit,
                        required: true,
                        placeholder: "Selecione um tipo de serviço",
                        children: Array.isArray(serviceTypes) && serviceTypes.map((serviceType)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: serviceType?.id,
                                children: serviceType?.name || 'Sem nome'
                            }, serviceType?.id, false, {
                                fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                                lineNumber: 312,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 301,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 294,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Limite Mensal",
                    htmlFor: "monthlyLimit",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 324,
                        columnNumber: 19
                    }, void 0),
                    helpText: "0 = ilimitado",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                        moduleColor: "people",
                        type: "number",
                        id: "monthlyLimit",
                        name: "monthlyLimit",
                        value: formData.monthlyLimit,
                        onChange: handleChange,
                        min: "0",
                        disabled: isSubmitting
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 327,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 320,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleFormGroup$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleFormGroup$3e$__["ModuleFormGroup"], {
                    moduleColor: "people",
                    label: "Observações",
                    htmlFor: "notes",
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 344,
                        columnNumber: 19
                    }, void 0),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTextarea$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTextarea$3e$__["ModuleTextarea"], {
                        moduleColor: "people",
                        id: "notes",
                        name: "notes",
                        value: formData.notes,
                        onChange: handleChange,
                        rows: 3,
                        disabled: isSubmitting
                    }, void 0, false, {
                        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                        lineNumber: 346,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
                    lineNumber: 340,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
            lineNumber: 230,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/people/InsuranceLimitFormModal.js",
        lineNumber: 220,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InsuranceLimitFormModal;
}}),
"[project]/src/components/ui/ExportMenu.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useConstructionMessage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/hooks/useConstructionMessage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/construction/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-ssr] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/image.js [app-ssr] (ecmascript) <export default as Image>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js [app-ssr] (ecmascript) <export default as FileSpreadsheet>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__ = __turbopack_import__("[project]/src/components/construction/ConstructionButton.js [app-ssr] (ecmascript) <export default as ConstructionButton>");
"use client";
;
;
;
;
;
;
const ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' })=>{
    const [dropdownOpen, setDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dropdownPosition, setDropdownPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        top: 0,
        right: 0,
        width: 0
    });
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Montar o componente apenas no cliente
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setMounted(true);
        return ()=>setMounted(false);
    }, []);
    // Calcular a posição do dropdown quando aberto
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (dropdownOpen && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setDropdownPosition({
                top: rect.bottom + window.scrollY,
                right: window.innerWidth - rect.right,
                width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)
            });
        }
    }, [
        dropdownOpen
    ]);
    // Fecha o dropdown ao clicar fora dele
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (buttonRef.current && !buttonRef.current.contains(event.target) && dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setDropdownOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    const handleExport = (format)=>{
        onExport(format);
        setDropdownOpen(false);
    };
    // Se estiver em construção, mostrar o botão de construção
    if (underConstruction) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$construction$2f$ConstructionButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ConstructionButton$3e$__["ConstructionButton"], {
            className: "flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors",
            title: "Exportação em Construção",
            content: "A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.",
            icon: "FileText",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                    size: 16
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: "Exportar"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                    size: 14
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/ExportMenu.js",
                    lineNumber: 69,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/ui/ExportMenu.js",
            lineNumber: 61,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                ref: buttonRef,
                onClick: ()=>setDropdownOpen(!dropdownOpen),
                className: `flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`,
                disabled: isExporting || disabled && !underConstruction,
                title: disabled ? "Não há dados para exportar" : "Exportar dados",
                children: [
                    isExporting ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                        size: 16,
                        className: "animate-spin"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 84,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                        size: 16
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 86,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: isExporting ? "Exportando..." : "Exportar"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                        size: 14,
                        className: `transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 76,
                columnNumber: 7
            }, this),
            dropdownOpen && mounted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropdownRef,
                className: "fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700",
                style: {
                    top: `${dropdownPosition.top}px`,
                    right: `${dropdownPosition.right}px`,
                    width: `${dropdownPosition.width}px`
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-sm font-medium text-neutral-700 dark:text-gray-200",
                            children: "Formato de exportação"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ExportMenu.js",
                            lineNumber: 104,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 103,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('image'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Image$3e$__["Image"], {
                                        size: 16,
                                        className: "text-blue-500 dark:text-blue-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Imagem (PNG)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('pdf'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                        size: 16,
                                        className: "text-red-500 dark:text-red-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "PDF"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 119,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handleExport('xlsx'),
                                className: "w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$spreadsheet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileSpreadsheet$3e$__["FileSpreadsheet"], {
                                        size: 16,
                                        className: "text-green-500 dark:text-green-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Excel (XLSX)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ExportMenu.js",
                                        lineNumber: 126,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ExportMenu.js",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ExportMenu.js",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ExportMenu.js",
                lineNumber: 94,
                columnNumber: 9
            }, this), document.body)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/ExportMenu.js",
        lineNumber: 75,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ExportMenu;
}}),
"[project]/src/components/ui/ModuleTable.js [app-ssr] (ecmascript) <export default as ModuleTable>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModuleTable": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleTable.js [app-ssr] (ecmascript)");
}}),
"[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialManager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialTriggerButton$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/tutorial/TutorialTriggerButton.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ModuleHeader.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/multi-select.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insuranceServiceLimitService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/insurancesService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/services/personsService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$scheduler$2f$services$2f$serviceTypeService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/scheduler/services/serviceTypeService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/admin/services/companyService.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/AuthContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/contexts/ToastContext.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ConfirmationDialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ConfirmationDialog.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$people$2f$InsuranceLimitFormModal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/people/InsuranceLimitFormModal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ExportMenu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/ExportMenu.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-ssr] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/filter.js [app-ssr] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleInput.js [app-ssr] (ecmascript) <export default as ModuleInput>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleSelect.js [app-ssr] (ecmascript) <export default as ModuleSelect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTable$3e$__ = __turbopack_import__("[project]/src/components/ui/ModuleTable.js [app-ssr] (ecmascript) <export default as ModuleTable>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/shield.js [app-ssr] (ecmascript) <export default as Shield>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/user.js [app-ssr] (ecmascript) <export default as User>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/tag.js [app-ssr] (ecmascript) <export default as Tag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-ssr] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__ = __turbopack_import__("[project]/node_modules/lucide-react/dist/esm/icons/trash.js [app-ssr] (ecmascript) <export default as Trash>");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Tutorial steps para a página de limites de convênio
const insuranceLimitsTutorialSteps = [
    {
        title: "Limites de Convênio",
        content: "Esta tela permite configurar os limites de utilização de serviços por convênio para cada paciente.",
        selector: "h1",
        position: "bottom"
    },
    {
        title: "Adicionar Novo Limite",
        content: "Clique aqui para adicionar um novo limite de convênio.",
        selector: "button:has(span:contains('Novo Limite'))",
        position: "left"
    },
    {
        title: "Filtrar Limites",
        content: "Use esta barra de pesquisa para encontrar limites específicos pelo nome do paciente ou convênio.",
        selector: "input[placeholder*='Buscar']",
        position: "bottom"
    },
    {
        title: "Filtrar por Pacientes",
        content: "Selecione um ou mais pacientes para filtrar os limites.",
        selector: "div:has(> div > label:contains('Filtrar por Pacientes'))",
        position: "bottom"
    },
    {
        title: "Filtrar por Convênio",
        content: "Filtre os limites por convênio específico.",
        selector: "select:nth-of-type(1)",
        position: "bottom"
    },
    {
        title: "Filtrar por Tipo de Serviço",
        content: "Filtre os limites por tipo de serviço específico.",
        selector: "select:nth-of-type(2)",
        position: "bottom"
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de limites em diferentes formatos usando este botão.",
        selector: ".export-button",
        position: "bottom"
    },
    {
        title: "Gerenciar Limites",
        content: "Edite ou exclua limites existentes usando os botões de ação na tabela.",
        selector: "table",
        position: "top"
    }
];
const InsuranceLimitsPage = ()=>{
    const { user: currentUser } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuth"])();
    const { addToast, toast_success, toast_error } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$ToastContext$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const isAdmin = currentUser?.role === "ADMIN" || currentUser?.role === "SYSTEM_ADMIN";
    const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
    const [limits, setLimits] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [search, setSearch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [filters, setFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        insuranceId: "",
        serviceTypeId: "",
        companyId: ""
    });
    const [personsFilter, setPersonsFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [personOptions, setPersonOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoadingPersonOptions, setIsLoadingPersonOptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [persons, setPersons] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [insurances, setInsurances] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [serviceTypes, setServiceTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [companies, setCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoadingCompanies, setIsLoadingCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedLimit, setSelectedLimit] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [limitFormOpen, setLimitFormOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isExporting, setIsExporting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoadingFilters, setIsLoadingFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Estados para paginação
    const [currentPage, setCurrentPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(1);
    const [totalPages, setTotalPages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(1);
    const [totalItems, setTotalItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Estado para ordenação
    const [sortField, setSortField] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("patient");
    const [sortDirection, setSortDirection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("asc");
    // Função para carregar opções de pacientes para o multi-select
    const loadPersonOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        setIsLoadingPersonOptions(true);
        try {
            // Carregar todos os pacientes para o multi-select (com limite maior)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["personsService"].getPersons({
                limit: 100,
                active: true // Apenas pacientes ativos por padrão
            });
            // Extrair os pacientes da resposta, garantindo que temos um array válido
            const personsArray = response?.persons || response?.people || [];
            const options = personsArray.map((person)=>({
                    value: person.id,
                    label: person.fullName,
                    // Guardar o nome para ordenação
                    sortName: person.fullName ? person.fullName.toLowerCase() : ''
                })) || [];
            // Ordenar as opções alfabeticamente pelo nome
            const sortedOptions = options.sort((a, b)=>a.sortName.localeCompare(b.sortName, 'pt-BR', {
                    sensitivity: 'base'
                }));
            console.log('Opções de pacientes ordenadas:', sortedOptions);
            setPersonOptions(sortedOptions);
        } catch (error) {
            console.error("Erro ao carregar opções de pacientes:", error);
            setPersonOptions([]);
        } finally{
            setIsLoadingPersonOptions(false);
        }
    }, []);
    // Função para carregar empresas (apenas para system_admin)
    const loadCompanies = async ()=>{
        if (!isSystemAdmin) return;
        setIsLoadingCompanies(true);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$admin$2f$services$2f$companyService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["companyService"].getCompaniesForSelect();
            setCompanies(response);
        } catch (error) {
            console.error("Erro ao carregar empresas:", error);
        } finally{
            setIsLoadingCompanies(false);
        }
    };
    // Carregar dados iniciais
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        loadLimits();
        loadFilterOptions();
        loadPersonOptions();
        // Carregar empresas se o usuário for system_admin
        if (isSystemAdmin) {
            loadCompanies();
        }
    }, [
        loadPersonOptions,
        isSystemAdmin
    ]);
    // Constante para itens por página
    const ITEMS_PER_PAGE = 10;
    // Carregar limites de convênio
    const loadLimits = async (page = currentPage, searchQuery = search, personIds = personsFilter, filterOptions = filters, sort = sortField || 'patient', direction = sortDirection || 'asc')=>{
        setIsLoading(true);
        try {
            // Garantir que a direção seja uma string válida
            const validDirection = direction && [
                'asc',
                'desc'
            ].includes(direction.toLowerCase()) ? direction.toLowerCase() : 'asc';
            // Garantir que a página é um número
            const pageNumber = parseInt(page, 10);
            // Atualizar o estado da página atual
            setCurrentPage(pageNumber);
            // Buscar TODOS os limites de convênio (sem paginação no backend)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insuranceServiceLimitService"].getAllLimits({
                search: searchQuery || undefined,
                personIds: personIds.length > 0 ? personIds : undefined,
                ...filterOptions,
                sortField: sort || 'patient',
                sortDirection: validDirection
            });
            console.log('Parâmetros enviados para o serviço:', {
                search: searchQuery || undefined,
                personIds: personIds.length > 0 ? personIds : undefined,
                ...filterOptions,
                sortField: sort || 'patient',
                sortDirection: validDirection
            });
            // Verificar se temos os dados no formato esperado
            if (response && typeof response === 'object') {
                // Extrair todos os limites
                let allLimits = response.limits || [];
                // A ordenação agora é feita no backend, não precisamos ordenar manualmente aqui
                // Apenas registramos a ordenação para debug
                console.log(`Ordenação aplicada: campo=${sort || 'patient'}, direção=${validDirection}`);
                // Calcular o total de itens e páginas
                const total = allLimits.length;
                const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;
                // Aplicar paginação manual no lado do cliente
                const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;
                const endIndex = startIndex + ITEMS_PER_PAGE;
                const paginatedLimits = allLimits.slice(startIndex, endIndex);
                // Verificar se a página atual é válida
                if (pageNumber > 1 && paginatedLimits.length === 0 && allLimits.length > 0) {
                    // Se a página atual não tem itens, mas existem itens em outras páginas,
                    // voltar para a primeira página
                    console.log(`Página ${pageNumber} está vazia, voltando para a página 1`);
                    setCurrentPage(1);
                    const firstPageLimits = allLimits.slice(0, ITEMS_PER_PAGE);
                    setLimits(firstPageLimits);
                } else {
                    // Atualizar o estado com os dados paginados manualmente
                    setLimits(paginatedLimits); // Apenas os 10 itens da página atual
                }
                setTotalItems(total);
                setTotalPages(pages);
                console.log(`Paginação manual: Página ${pageNumber}, exibindo ${paginatedLimits.length} itens (${startIndex + 1}-${Math.min(endIndex, total)}) de ${total} total`);
            } else {
                console.error('Dados de limites inválidos:', response);
                setLimits([]);
                setTotalItems(0);
                setTotalPages(1);
            }
        } catch (error) {
            console.error("Erro ao carregar limites de convênio:", error);
            toast_error("Erro ao carregar limites de convênio");
            setLimits([]);
            setTotalItems(0);
            setTotalPages(1);
        } finally{
            setIsLoading(false);
        }
    };
    // Carregar opções para os filtros
    const loadFilterOptions = async ()=>{
        setIsLoadingFilters(true);
        try {
            // Carregar pessoas
            try {
                const personsData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$personsService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["personsService"].getPersons({
                    limit: 100
                });
                console.log('Dados de pessoas recebidos:', personsData);
                // Garantir que temos um array válido
                if (personsData && typeof personsData === 'object') {
                    // Verificar a estrutura dos dados retornados e extrair o array de pessoas
                    const personsArray = Array.isArray(personsData) ? personsData : personsData.persons ? personsData.persons : personsData.people ? personsData.people : personsData.data ? personsData.data : [];
                    console.log('Array de pessoas extraído:', personsArray);
                    // Garantir que estamos definindo um array
                    setPersons(Array.isArray(personsArray) ? personsArray : []);
                } else {
                    console.error('Dados de pessoas inválidos:', personsData);
                    setPersons([]);
                }
            } catch (personError) {
                console.error('Erro ao carregar pessoas:', personError);
                setPersons([]);
            }
            // Carregar convênios
            try {
                const insurancesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insurancesService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insurancesService"].getInsurances();
                console.log('Dados de convênios recebidos:', insurancesData);
                // Garantir que temos um array válido
                if (insurancesData && typeof insurancesData === 'object') {
                    // Verificar a estrutura dos dados retornados e extrair o array de convênios
                    const insurancesArray = Array.isArray(insurancesData) ? insurancesData : insurancesData.insurances ? insurancesData.insurances : insurancesData.data ? insurancesData.data : [];
                    console.log('Array de convênios extraído:', insurancesArray);
                    // Garantir que estamos definindo um array
                    setInsurances(Array.isArray(insurancesArray) ? insurancesArray : []);
                } else {
                    console.error('Dados de convênios inválidos:', insurancesData);
                    setInsurances([]);
                }
            } catch (insuranceError) {
                console.error('Erro ao carregar convênios:', insuranceError);
                setInsurances([]);
            }
            // Carregar tipos de serviço
            try {
                const serviceTypesData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$scheduler$2f$services$2f$serviceTypeService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceTypeService"].getServiceTypes();
                console.log('Dados de tipos de serviço recebidos:', serviceTypesData);
                // Garantir que temos um array válido
                if (serviceTypesData && typeof serviceTypesData === 'object') {
                    // Verificar a estrutura dos dados retornados e extrair o array de tipos de serviço
                    const serviceTypesArray = Array.isArray(serviceTypesData) ? serviceTypesData : serviceTypesData.serviceTypes ? serviceTypesData.serviceTypes : serviceTypesData.data ? serviceTypesData.data : [];
                    console.log('Array de tipos de serviço extraído:', serviceTypesArray);
                    // Garantir que estamos definindo um array
                    setServiceTypes(Array.isArray(serviceTypesArray) ? serviceTypesArray : []);
                } else {
                    console.error('Dados de tipos de serviço inválidos:', serviceTypesData);
                    setServiceTypes([]);
                }
            } catch (serviceTypeError) {
                console.error('Erro ao carregar tipos de serviço:', serviceTypeError);
                setServiceTypes([]);
            }
        } catch (error) {
            console.error("Erro ao carregar opções de filtro:", error);
            toast_error("Erro ao carregar opções de filtro");
        } finally{
            setIsLoadingFilters(false);
        }
    };
    // Manipuladores de eventos
    const handleSearch = (e)=>{
        e.preventDefault();
        loadLimits(1, search, personsFilter, filters, sortField, sortDirection);
    };
    const handleFilterChange = (field, value)=>{
        const newFilters = {
            ...filters,
            [field]: value
        };
        setFilters(newFilters);
        loadLimits(1, search, personsFilter, newFilters, sortField, sortDirection);
    };
    const handlePersonsFilterChange = (value)=>{
        setPersonsFilter(value);
        loadLimits(1, search, value, filters, sortField, sortDirection);
    };
    const handleResetFilters = ()=>{
        setSearch("");
        setPersonsFilter([]);
        setFilters({
            insuranceId: "",
            serviceTypeId: "",
            companyId: ""
        });
        loadLimits(1, "", [], {
            insuranceId: "",
            serviceTypeId: "",
            companyId: ""
        }, sortField, sortDirection);
    };
    // Função para lidar com a mudança de página
    const handlePageChange = (page)=>{
        console.log(`Mudando para a página ${page}`);
        // Chamamos loadLimits com a nova página
        loadLimits(page, search, personsFilter, filters, sortField, sortDirection);
    };
    // Função para lidar com a mudança de ordenação
    const handleSortChange = (field, direction)=>{
        console.log(`Alterando ordenação: campo=${field}, direção=${direction}`, {
            tipoField: typeof field,
            tipoDirection: typeof direction,
            valorField: field,
            valorDirection: direction
        });
        // Garantir que a direção seja uma string válida
        const validDirection = direction && [
            'asc',
            'desc'
        ].includes(direction.toLowerCase()) ? direction.toLowerCase() : 'asc';
        console.log(`Direção normalizada: ${validDirection}`);
        setSortField(field);
        setSortDirection(validDirection);
        loadLimits(currentPage, search, personsFilter, filters, field, validDirection);
    };
    const handleEditLimit = (limit)=>{
        setSelectedLimit(limit);
        setLimitFormOpen(true);
    };
    const handleDeleteLimit = (limit)=>{
        setSelectedLimit(limit);
        setConfirmationDialogOpen(true);
    };
    const confirmDeleteLimit = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insuranceServiceLimitService"].deleteLimit(selectedLimit.id);
            toast_success("Limite de convênio excluído com sucesso");
            loadLimits();
            setConfirmationDialogOpen(false);
        } catch (error) {
            console.error("Erro ao excluir limite de convênio:", error);
            toast_error("Erro ao excluir limite de convênio");
        }
    };
    const handleExport = async (format)=>{
        setIsExporting(true);
        try {
            // Encontrar os nomes dos filtros selecionados para o subtítulo da exportação
            let insuranceName, serviceTypeName;
            if (filters.insuranceId) {
                const selectedInsurance = insurances.find((i)=>i.id === filters.insuranceId);
                insuranceName = selectedInsurance ? selectedInsurance.name : undefined;
            }
            if (filters.serviceTypeId) {
                const selectedServiceType = serviceTypes.find((s)=>s.id === filters.serviceTypeId);
                serviceTypeName = selectedServiceType ? selectedServiceType.name : undefined;
            }
            // Exportar usando os mesmos filtros da tabela atual
            const success = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$services$2f$insuranceServiceLimitService$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["insuranceServiceLimitService"].exportInsuranceLimits({
                search: search || undefined,
                personIds: personsFilter.length > 0 ? personsFilter : undefined,
                insuranceId: filters.insuranceId || undefined,
                insuranceName,
                serviceTypeId: filters.serviceTypeId || undefined,
                serviceTypeName,
                companyId: filters.companyId || undefined,
                sortField,
                sortDirection
            }, format);
            if (success) {
                toast_success(`Dados exportados com sucesso no formato ${format.toUpperCase()}`);
            } else {
                toast_error("Erro ao exportar dados");
            }
        } catch (error) {
            console.error("Erro ao exportar dados:", error);
            toast_error("Erro ao exportar dados");
        } finally{
            setIsExporting(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-slate-800 dark:text-white flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                size: 24,
                                className: "mr-2 text-orange-600 dark:text-orange-400"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 487,
                                columnNumber: 11
                            }, this),
                            "Limites de Convênio"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                        lineNumber: 486,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ExportMenu$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                onExport: handleExport,
                                isExporting: isExporting,
                                disabled: isLoading || limits.length === 0,
                                className: "text-orange-700 dark:text-orange-300"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 493,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>{
                                    setSelectedLimit(null);
                                    setLimitFormOpen(true);
                                },
                                className: "flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        size: 18
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                        lineNumber: 508,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: "Novo Limite"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                        lineNumber: 509,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 501,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                        lineNumber: 491,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 485,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                title: "Filtros",
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                    size: 22,
                    className: "text-module-people-icon dark:text-module-people-icon-dark"
                }, void 0, false, {
                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                    lineNumber: 517,
                    columnNumber: 15
                }, void 0),
                description: "Configure os limites de utilização de serviços por convênio para cada paciente. Utilize os filtros abaixo para encontrar limites específicos.",
                tutorialSteps: insuranceLimitsTutorialSteps,
                tutorialName: "insurance-limits-overview",
                moduleColor: "people",
                filters: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    onSubmit: handleSearch,
                    className: "flex flex-col gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col md:flex-row gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1 relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                            className: "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 529,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleInput$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleInput$3e$__["ModuleInput"], {
                                            moduleColor: "people",
                                            type: "text",
                                            placeholder: "Buscar por nome do paciente ou convênio...",
                                            value: search,
                                            onChange: (e)=>setSearch(e.target.value),
                                            className: "w-full pl-10"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 530,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 528,
                                    columnNumber: 15
                                }, void 0),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col sm:flex-row gap-2",
                                    children: [
                                        isSystemAdmin && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full sm:w-48",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                                                moduleColor: "people",
                                                value: filters.companyId,
                                                onChange: (e)=>handleFilterChange("companyId", e.target.value),
                                                placeholder: "Empresa",
                                                disabled: isLoadingCompanies,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "",
                                                        children: "Todas as empresas"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                        lineNumber: 551,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    companies.map((company)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: company.id,
                                                            children: company.name
                                                        }, company.id, false, {
                                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                            lineNumber: 553,
                                                            columnNumber: 25
                                                        }, void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 544,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 543,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full sm:w-48",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                                                moduleColor: "people",
                                                value: filters.insuranceId,
                                                onChange: (e)=>handleFilterChange("insuranceId", e.target.value),
                                                placeholder: "Convênios",
                                                disabled: isLoadingFilters,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "",
                                                        children: "Todos os Convênios"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                        lineNumber: 570,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    Array.isArray(insurances) && insurances.map((insurance)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: insurance?.id,
                                                            children: insurance?.name || 'Sem nome'
                                                        }, insurance?.id, false, {
                                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                            lineNumber: 572,
                                                            columnNumber: 23
                                                        }, void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 563,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 562,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full sm:w-48",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleSelect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleSelect$3e$__["ModuleSelect"], {
                                                moduleColor: "people",
                                                value: filters.serviceTypeId,
                                                onChange: (e)=>handleFilterChange("serviceTypeId", e.target.value),
                                                placeholder: "Tipos de Serviço",
                                                disabled: isLoadingFilters,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "",
                                                        children: "Todos os Tipos de Serviço"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                        lineNumber: 588,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    Array.isArray(serviceTypes) && serviceTypes.map((serviceType)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: serviceType?.id,
                                                            children: serviceType?.name || 'Sem nome'
                                                        }, serviceType?.id, false, {
                                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                            lineNumber: 590,
                                                            columnNumber: 23
                                                        }, void 0))
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 581,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 580,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FilterButton"], {
                                            type: "submit",
                                            moduleColor: "people",
                                            variant: "primary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                                                    size: 16,
                                                    className: "sm:hidden"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                    lineNumber: 598,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "hidden sm:inline",
                                                    children: "Filtrar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                    lineNumber: 599,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 597,
                                            columnNumber: 17
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleHeader$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FilterButton"], {
                                            type: "button",
                                            onClick: handleResetFilters,
                                            moduleColor: "people",
                                            variant: "secondary",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                                                    size: 16,
                                                    className: "sm:hidden"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                    lineNumber: 608,
                                                    columnNumber: 19
                                                }, void 0),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "hidden sm:inline",
                                                    children: "Limpar"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                    lineNumber: 609,
                                                    columnNumber: 19
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 602,
                                            columnNumber: 17
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 540,
                                    columnNumber: 15
                                }, void 0)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                            lineNumber: 527,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$multi$2d$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                label: "Filtrar por Pacientes",
                                value: personsFilter,
                                onChange: handlePersonsFilterChange,
                                options: personOptions,
                                placeholder: "Selecione um ou mais pacientes pelo nome...",
                                loading: isLoadingPersonOptions,
                                moduleOverride: "people"
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 616,
                                columnNumber: 15
                            }, void 0)
                        }, void 0, false, {
                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                            lineNumber: 615,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                    lineNumber: 523,
                    columnNumber: 11
                }, void 0)
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 515,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ModuleTable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ModuleTable$3e$__["ModuleTable"], {
                moduleColor: "people",
                columns: [
                    {
                        header: 'Paciente',
                        field: 'patient',
                        width: '25%',
                        // Função de acesso personalizada para ordenação
                        accessor: (limit)=>limit?.Person?.fullName || limit?.person?.fullName || ''
                    },
                    {
                        header: 'Convênio',
                        field: 'insurance',
                        width: '20%',
                        accessor: (limit)=>limit?.Insurance?.name || limit?.insurance?.name || ''
                    },
                    {
                        header: 'Tipo de Serviço',
                        field: 'serviceType',
                        width: '25%',
                        accessor: (limit)=>limit?.ServiceType?.name || limit?.serviceType?.name || ''
                    },
                    {
                        header: 'Limite Mensal',
                        field: 'monthlyLimit',
                        width: '15%'
                    },
                    {
                        header: 'Ações',
                        field: 'actions',
                        className: 'text-right',
                        width: '15%',
                        sortable: false
                    }
                ],
                data: limits,
                isLoading: isLoading,
                emptyMessage: "Nenhum limite de convênio encontrado",
                emptyIcon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Shield$3e$__["Shield"], {
                    size: 24
                }, void 0, false, {
                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                    lineNumber: 659,
                    columnNumber: 20
                }, void 0),
                tableId: "people-insurance-limits-table",
                enableColumnToggle: true,
                defaultSortField: "patient",
                defaultSortDirection: "asc",
                currentPage: currentPage,
                totalPages: totalPages,
                totalItems: totalItems,
                onPageChange: handlePageChange,
                onSort: handleSortChange,
                sortField: sortField,
                sortDirection: sortDirection,
                showPagination: true,
                itemsPerPage: ITEMS_PER_PAGE,
                renderRow: (limit, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                        className: moduleColors.hoverBg,
                        children: [
                            visibleColumns.includes('patient') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$user$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__User$3e$__["User"], {
                                                size: 20
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 679,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 678,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "ml-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-sm font-medium text-neutral-900 dark:text-neutral-100",
                                                children: limit?.Person?.fullName || limit?.person?.fullName || "N/A"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 682,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 681,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 677,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 676,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('insurance') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                            className: "h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 693,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-neutral-700 dark:text-neutral-300 truncate",
                                            children: limit?.Insurance?.name || limit?.insurance?.name || "N/A"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 694,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 692,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 691,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('serviceType') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$tag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Tag$3e$__["Tag"], {
                                            className: "h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 704,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-neutral-700 dark:text-neutral-300 truncate",
                                            children: limit?.ServiceType?.name || limit?.serviceType?.name || "N/A"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 705,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 703,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 702,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('monthlyLimit') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                            className: "h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 715,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-neutral-700 dark:text-neutral-300",
                                            children: limit?.monthlyLimit > 0 ? `${limit.monthlyLimit}x por mês` : "Ilimitado"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 716,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 714,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 713,
                                columnNumber: 15
                            }, void 0),
                            visibleColumns.includes('actions') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                className: "px-4 py-4 text-right",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-end gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleEditLimit(limit),
                                            className: "p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors",
                                            title: "Editar",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 731,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 726,
                                            columnNumber: 19
                                        }, void 0),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleDeleteLimit(limit),
                                            className: "p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors",
                                            title: "Excluir",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash$3e$__["Trash"], {
                                                size: 16
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                                lineNumber: 738,
                                                columnNumber: 21
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                            lineNumber: 733,
                                            columnNumber: 19
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                    lineNumber: 725,
                                    columnNumber: 17
                                }, void 0)
                            }, void 0, false, {
                                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                                lineNumber: 724,
                                columnNumber: 15
                            }, void 0)
                        ]
                    }, limit.id, true, {
                        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                        lineNumber: 674,
                        columnNumber: 11
                    }, void 0)
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 631,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ConfirmationDialog$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: confirmationDialogOpen,
                onClose: ()=>setConfirmationDialogOpen(false),
                onConfirm: confirmDeleteLimit,
                title: "Excluir Limite de Convênio",
                message: `Tem certeza que deseja excluir o limite para ${selectedLimit?.Person?.fullName || selectedLimit?.person?.fullName || 'este paciente'} com o convênio ${selectedLimit?.Insurance?.name || selectedLimit?.insurance?.name || 'selecionado'}?`,
                variant: "danger",
                confirmText: "Excluir",
                cancelText: "Cancelar"
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 748,
                columnNumber: 7
            }, this),
            limitFormOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$people$2f$InsuranceLimitFormModal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                isOpen: limitFormOpen,
                onClose: ()=>setLimitFormOpen(false),
                limit: selectedLimit,
                onSuccess: ()=>{
                    setLimitFormOpen(false);
                    loadLimits();
                }
            }, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 761,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$tutorial$2f$TutorialManager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
                lineNumber: 773,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js",
        lineNumber: 483,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = InsuranceLimitsPage;
}}),
"[project]/src/app/dashboard/people/insurance-limits/page.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>InsuranceLimitsRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$InsuranceLimitsPage$2f$InsuranceLimitsPage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js [app-ssr] (ecmascript)");
"use client";
;
;
function InsuranceLimitsRoute() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$modules$2f$people$2f$InsuranceLimitsPage$2f$InsuranceLimitsPage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/app/dashboard/people/insurance-limits/page.js",
        lineNumber: 6,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/app/dashboard/people/insurance-limits/page.js [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_ffcaf4._.js.map