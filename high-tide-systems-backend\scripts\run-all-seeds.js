// scripts/run-all-seeds.js
const { execSync } = require('child_process');
const path = require('path');

// Função para executar um script de seed
function runSeed(seedFile, description) {
  console.log(`\n=== Executando seed de ${description}... ===\n`);

  try {
    // Executar o script de seed
    execSync(`node prisma/${seedFile}`, {
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });

    console.log(`\n✅ Seed de ${description} executado com sucesso!\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ Erro ao executar o seed de ${description}:`, error);
    return false;
  }
}

// Função principal para executar todos os seeds em sequência
async function runAllSeeds() {
  console.log('=== INICIANDO EXECUÇÃO DE TODOS OS SEEDS ===\n');

  // Ordem de execução dos seeds
  const seeds = [
    { file: 'seed.js', description: 'base (empresas e usuário admin)' },
    { file: 'seed-professions.js', description: 'profissões' },
    { file: 'seed-users.js', description: 'usuários' },
    { file: 'seed-insurances.js', description: 'convênios' },
    { file: 'seed-insurance-limits.js', description: 'limites de convênio' },
    { file: 'seed-service-types.js', description: 'tipos de serviço' },
    { file: 'seed-locations.js', description: 'localizações' },
    { file: 'seed-clients.js', description: 'clientes' },
    { file: 'seed-patients.js', description: 'pacientes' },
    { file: 'seed-working-hours.js', description: 'horários de trabalho' }
  ];

  let successCount = 0;
  let failCount = 0;

  // Executar cada seed em sequência
  for (const seed of seeds) {
    const success = runSeed(seed.file, seed.description);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  // Exibir resumo
  console.log('\n=== RESUMO DA EXECUÇÃO DOS SEEDS ===');
  console.log(`✅ Seeds executados com sucesso: ${successCount}`);
  console.log(`❌ Seeds com falha: ${failCount}`);
  console.log('=== FIM DA EXECUÇÃO DOS SEEDS ===\n');

  if (failCount > 0) {
    console.log('⚠️ Alguns seeds falharam. Verifique os erros acima e tente executar individualmente os seeds que falharam.');
    process.exit(1);
  } else {
    console.log('🎉 Todos os seeds foram executados com sucesso!');
  }
}

// Executar a função principal
runAllSeeds();
