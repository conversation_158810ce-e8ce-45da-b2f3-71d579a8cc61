const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const Papa = require('papaparse');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient();

// CONFIGURAÇÕES (adaptadas para seu projeto)
const DEFAULT_COMPANY_NAME = process.env.DEFAULT_COMPANY_NAME || 'ABA+ Migrada';
const DEFAULT_BRANCH_NAME = process.env.DEFAULT_BRANCH_NAME || 'Unidade Principal';
const DEFAULT_PASSWORD = process.env.DEFAULT_PASSWORD || 'TrocarSenha123!';

// Função para ler CSV
async function readCSV(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const results = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: false, // Manter como string para evitar problemas com CPF
      transformHeader: (header) => header.trim(),
      transform: (value, header) => {
        // Tratar valores vazios
        if (value === '' || value === 'null' || value === 'undefined') {
          return null;
        }
        
        // Campos que devem ser boolean
        if (['active', 'encarregado'].includes(header)) {
          return value === 'true' || value === true;
        }
        
        // Campos que devem ser números
        if (['failedLoginAttempts', 'codigoOriginal'].includes(header)) {
          const num = parseInt(value);
          return isNaN(num) ? 0 : num;
        }
        
        return value;
      }
    });
    
    if (results.errors.length > 0) {
      console.warn(`Avisos ao ler ${filePath}:`, results.errors);
    }
    
    return results.data;
  } catch (error) {
    console.error(`Erro ao ler arquivo ${filePath}:`, error.message);
    throw error;
  }
}

// Função para verificar se os arquivos CSV existem
function checkCSVFiles() {
  const requiredFiles = [
    'funcionarios_schema_user.csv',
    'profissoes_schema_profession.csv'
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.error('❌ Arquivos CSV não encontrados:');
    missingFiles.forEach(file => console.error(`   • ${file}`));
    console.error('\n💡 Execute primeiro: node index.js (para gerar os CSVs)');
    return false;
  }
  
  return true;
}

// Função para criar/buscar empresa padrão
async function createDefaultCompany() {
  console.log('🏢 Criando/buscando empresa padrão...');
  
  try {
    // Procurar se já existe uma empresa com o nome
    let company = await prisma.company.findFirst({
      where: { name: DEFAULT_COMPANY_NAME }
    });

    if (!company) {
      // Criar empresa padrão
      company = await prisma.company.create({
        data: {
          name: DEFAULT_COMPANY_NAME,
          cnpj: '00.000.000/0001-00', // CNPJ fictício - deve ser alterado
          active: true,
          defaultCurrency: 'BRL',
          timeZone: 'America/Sao_Paulo',
          contactEmail: '<EMAIL>', // Email fictício
          phone: '(11) 99999-9999' // Telefone fictício
        }
      });
      console.log(`✓ Empresa criada: ${company.name} (ID: ${company.id})`);
    } else {
      console.log(`✓ Empresa encontrada: ${company.name} (ID: ${company.id})`);
    }

    return company;
  } catch (error) {
    console.error('Erro ao criar/buscar empresa:', error.message);
    throw error;
  }
}

// Função para criar/buscar filial padrão
async function createDefaultBranch(companyId) {
  console.log('🏢 Criando/buscando filial padrão...');
  
  try {
    // Procurar se já existe uma filial
    let branch = await prisma.branch.findFirst({
      where: { 
        name: DEFAULT_BRANCH_NAME,
        companyId: companyId
      }
    });

    if (!branch) {
      // Criar filial padrão
      branch = await prisma.branch.create({
        data: {
          name: DEFAULT_BRANCH_NAME,
          address: 'Endereço a ser definido',
          city: 'Cidade a ser definida',
          state: 'Estado a ser definido',
          active: true,
          isHeadquarters: true,
          companyId: companyId
        }
      });
      console.log(`✓ Filial criada: ${branch.name} (ID: ${branch.id})`);
    } else {
      console.log(`✓ Filial encontrada: ${branch.name} (ID: ${branch.id})`);
    }

    return branch;
  } catch (error) {
    console.error('Erro ao criar/buscar filial:', error.message);
    throw error;
  }
}

// Função para migrar profissões
async function migrateProfessions(companyId) {
  console.log('\n📋 Migrando profissões...');
  
  try {
    const professionsData = await readCSV('profissoes_schema_profession.csv');
    let migratedCount = 0;
    let skippedCount = 0;

    for (const professionRow of professionsData) {
      try {
        // Verificar se a profissão já existe (pelo nome e empresa)
        const existingProfession = await prisma.profession.findFirst({
          where: {
            name: professionRow.name,
            companyId: companyId
          }
        });

        if (existingProfession) {
          console.log(`⚠️  Profissão já existe: ${professionRow.name}`);
          skippedCount++;
          continue;
        }

        // Criar nova profissão
        const profession = await prisma.profession.create({
          data: {
            id: professionRow.id,
            name: professionRow.name,
            description: professionRow.description,
            active: professionRow.active !== false,
            companyId: companyId,
            createdAt: professionRow.createdAt ? new Date(professionRow.createdAt) : new Date(),
            updatedAt: professionRow.updatedAt ? new Date(professionRow.updatedAt) : new Date()
          }
        });

        console.log(`✓ Profissão criada: ${profession.name}`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Erro ao criar profissão ${professionRow.name}:`, error.message);
      }
    }

    console.log(`\n📊 Profissões: ${migratedCount} criadas, ${skippedCount} puladas`);
    return migratedCount;

  } catch (error) {
    console.error('Erro na migração de profissões:', error.message);
    throw error;
  }
}

// Função para migrar usuários
async function migrateUsers(companyId, branchId) {
  console.log('\n👥 Migrando usuários...');
  
  try {
    const usersData = await readCSV('funcionarios_schema_user.csv');
    let migratedCount = 0;
    let skippedCount = 0;
    let errorsCount = 0;

    // Hash da senha padrão com salt rounds do seu projeto
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, saltRounds);

    for (const userRow of usersData) {
      try {
        // Normalizar dados antes da verificação
        const normalizedEmail = userRow.email?.toString().trim() || null;
        const normalizedCpf = userRow.cpf ? userRow.cpf.toString().trim() : null;
        const normalizedLogin = userRow.login ? userRow.login.toString().trim() : null;

        // Verificar se o usuário já existe (por email ou CPF)
        const whereConditions = [];
        
        if (normalizedEmail) {
          whereConditions.push({ email: normalizedEmail });
        }
        
        if (normalizedCpf) {
          whereConditions.push({ cpf: normalizedCpf });
        }
        
        if (normalizedLogin) {
          whereConditions.push({ login: normalizedLogin });
        }

        const existingUser = whereConditions.length > 0 ? await prisma.user.findFirst({
          where: { OR: whereConditions }
        }) : null;

        if (existingUser) {
          console.log(`⚠️  Usuário já existe: ${normalizedEmail || normalizedCpf || normalizedLogin}`);
          skippedCount++;
          continue;
        }

        // Buscar profissão correspondente
        let professionId = null;
        if (userRow.cargoDescricao) {
          const profession = await prisma.profession.findFirst({
            where: {
              name: userRow.cargoDescricao.toString().trim(),
              companyId: companyId
            }
          });
          professionId = profession?.id || null;
        }

        // Preparar dados do usuário (normalizando todos os campos)
        const userData = {
          id: userRow.id,
          email: normalizedEmail,
          password: hashedPassword,
          active: userRow.active !== false,
          createdAt: userRow.createdAt ? new Date(userRow.createdAt) : new Date(),
          updatedAt: userRow.updatedAt ? new Date(userRow.updatedAt) : new Date(),
          address: userRow.address ? userRow.address.toString().trim() : null,
          birthDate: userRow.birthDate ? new Date(userRow.birthDate) : null,
          cpf: normalizedCpf,
          fullName: userRow.fullName ? userRow.fullName.toString().trim() : '',
          login: normalizedLogin || userRow.codigoOriginal?.toString(),
          modules: ['BASIC'], // Módulo padrão
          permissions: [], // Permissões vazias
          phone: userRow.phone ? userRow.phone.toString().trim() : null,
          companyId: companyId,
          deletedAt: userRow.deletedAt ? new Date(userRow.deletedAt) : null,
          failedLoginAttempts: parseInt(userRow.failedLoginAttempts) || 0,
          passwordChangedAt: userRow.passwordChangedAt ? new Date(userRow.passwordChangedAt) : new Date(),
          role: userRow.role || 'EMPLOYEE',
          professionId: professionId,
          profileImageUrl: userRow.profileImageUrl ? userRow.profileImageUrl.toString().trim() : null,
          city: userRow.city ? userRow.city.toString().trim() : null,
          postalCode: userRow.postalCode ? userRow.postalCode.toString().trim() : null,
          state: userRow.state ? userRow.state.toString().trim() : null,
          branchId: branchId,
          neighborhood: userRow.neighborhood ? userRow.neighborhood.toString().trim() : null
        };

        // Criar usuário
        const user = await prisma.user.create({
          data: userData
        });

        console.log(`✓ Usuário criado: ${user.fullName} (${user.email})`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Erro ao criar usuário ${userRow.fullName || userRow.email || 'sem nome'}:`);
        console.error(`   Dados: ${JSON.stringify({
          email: normalizedEmail,
          cpf: normalizedCpf,
          login: normalizedLogin,
          fullName: userRow.fullName
        }, null, 2)}`);
        console.error(`   Erro: ${error.message}`);
        errorsCount++;
        
        // Continuar com próximo usuário ao invés de parar
        continue;
      }
    }

    console.log(`\n📊 Usuários: ${migratedCount} criados, ${skippedCount} pulados, ${errorsCount} erros`);
    return migratedCount;

  } catch (error) {
    console.error('Erro na migração de usuários:', error.message);
    throw error;
  }
}

// Função para gerar relatório da migração
async function generateMigrationReport(companyId, branchId) {
  console.log('\n📄 Gerando relatório da migração...');
  
  try {
    const stats = {
      company: await prisma.company.findUnique({ where: { id: companyId } }),
      branch: await prisma.branch.findUnique({ where: { id: branchId } }),
      totalProfessions: await prisma.profession.count({ where: { companyId } }),
      totalUsers: await prisma.user.count({ where: { companyId } }),
      activeUsers: await prisma.user.count({ where: { companyId, active: true } }),
      inactiveUsers: await prisma.user.count({ where: { companyId, active: false } }),
      admins: await prisma.user.count({ where: { companyId, role: 'COMPANY_ADMIN' } }),
      employees: await prisma.user.count({ where: { companyId, role: 'EMPLOYEE' } }),
      usersWithProfession: await prisma.user.count({ 
        where: { companyId, professionId: { not: null } } 
      }),
      usersWithoutProfession: await prisma.user.count({ 
        where: { companyId, professionId: null } 
      })
    };

    const report = {
      timestamp: new Date().toISOString(),
      migration: {
        companyId: companyId,
        branchId: branchId,
        defaultPassword: DEFAULT_PASSWORD
      },
      statistics: stats,
      warnings: [
        'TODOS OS USUÁRIOS FORAM CRIADOS COM A SENHA PADRÃO: ' + DEFAULT_PASSWORD,
        'As senhas DEVEM ser alteradas antes do uso em produção',
        'Usuários sem profissão mapeada precisam ser revisados',
        'Dados da empresa e filial são fictícios e precisam ser atualizados'
      ],
      nextSteps: [
        'Alterar senhas de todos os usuários',
        'Atualizar dados reais da empresa (CNPJ, endereço, contato)',
        'Atualizar dados reais da filial',
        'Revisar usuários sem profissão',
        'Configurar módulos e permissões específicas',
        'Testar login de alguns usuários',
        'Configurar grupos de profissão se necessário'
      ]
    };

    fs.writeFileSync('relatorio_migracao_banco.json', JSON.stringify(report, null, 2));
    console.log('✓ Relatório salvo: relatorio_migracao_banco.json');
    
    return report;
  } catch (error) {
    console.error('Erro ao gerar relatório:', error.message);
    throw error;
  }
}

// Função principal
async function main() {
  try {
    console.log('🚀 Iniciando migração ABA+ para o banco existente...\n');

    // Verificar se os CSVs existem
    if (!checkCSVFiles()) {
      process.exit(1);
    }

    // 1. Criar/buscar empresa padrão
    const company = await createDefaultCompany();
    
    // 2. Criar/buscar filial padrão
    const branch = await createDefaultBranch(company.id);

    // 3. Migrar profissões
    await migrateProfessions(company.id);

    // 4. Migrar usuários
    await migrateUsers(company.id, branch.id);

    // 5. Gerar relatório
    const report = await generateMigrationReport(company.id, branch.id);

    console.log('\n🎉 Migração concluída com sucesso!');
    console.log('\n📊 Resumo:');
    console.log(`   • Empresa: ${report.statistics.company.name}`);
    console.log(`   • Filial: ${report.statistics.branch.name}`);
    console.log(`   • Profissões migradas: ${report.statistics.totalProfessions}`);
    console.log(`   • Usuários migrados: ${report.statistics.totalUsers}`);
    console.log(`   • Usuários ativos: ${report.statistics.activeUsers}`);
    console.log(`   • Administradores: ${report.statistics.admins}`);
    console.log(`   • Funcionários: ${report.statistics.employees}`);
    
    console.log('\n⚠️  IMPORTANTE:');
    console.log(`   • Senha padrão para todos: ${DEFAULT_PASSWORD}`);
    console.log(`   • ALTERE AS SENHAS ANTES DE USAR EM PRODUÇÃO!`);
    console.log(`   • Atualize dados da empresa e filial`);

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Função para limpar dados da migração
async function cleanMigrationData() {
  console.log('🧹 Limpando dados da migração ABA+...');
  
  try {
    // Buscar empresa da migração
    const company = await prisma.company.findFirst({
      where: { name: DEFAULT_COMPANY_NAME }
    });

    if (!company) {
      console.log('❌ Empresa da migração não encontrada');
      return;
    }

    // Deletar usuários
    const deletedUsers = await prisma.user.deleteMany({
      where: { companyId: company.id }
    });
    console.log(`✓ ${deletedUsers.count} usuários deletados`);

    // Deletar profissões
    const deletedProfessions = await prisma.profession.deleteMany({
      where: { companyId: company.id }
    });
    console.log(`✓ ${deletedProfessions.count} profissões deletadas`);

    // Deletar filiais
    const deletedBranches = await prisma.branch.deleteMany({
      where: { companyId: company.id }
    });
    console.log(`✓ ${deletedBranches.count} filiais deletadas`);

    // Deletar empresa
    await prisma.company.delete({
      where: { id: company.id }
    });
    console.log(`✓ Empresa "${company.name}" deletada`);

    console.log('🧹 Limpeza concluída!');
  } catch (error) {
    console.error('Erro na limpeza:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'clean') {
    cleanMigrationData().catch(console.error);
  } else {
    main().catch(console.error);
  }
}

module.exports = { 
  main, 
  migrateProfessions, 
  migrateUsers, 
  createDefaultCompany, 
  createDefaultBranch,
  cleanMigrationData 
};