# 🌱 COMANDOS DE SEED - RESUMO EXECUTIVO

## 🚀 COMANDO PRINCIPAL (USE ESTE!)

```bash
# Executar todos os seeds na ordem correta
node seed-all.js

# Executar no Docker
node seed-all.js --docker

# Verificar status dos dados
node seed-all.js --status

# Ver todos os comandos disponíveis
node seed-all.js --commands

# Ajuda
node seed-all.js --help
```

## 📊 VERIFICAÇÃO DE STATUS

```bash
# Verificar quantos dados existem no banco
node seed-all.js --status
```

## 🔄 EXECUÇÃO COMPLETA ALTERNATIVA

```bash
# Local
node scripts/run-all-seeds.js

# Docker
node scripts/run-all-seeds-docker.js
```

## 🎯 EXECUÇÃO INDIVIDUAL (SE NECESSÁRIO)

```bash
# 1. Base
node scripts/run-base-seed.js

# 2. Profissões
node scripts/run-professions-seed.js

# 3. Usuários
node scripts/run-users-seed.js

# 4. Convênios
node scripts/run-insurance-seed.js

# 5. Limites de Convênio
node scripts/run-insurance-limits-seed.js

# 6. Tipos de Serviço
node scripts/run-service-types-seed.js

# 7. Localizações
node scripts/run-locations-seed.js

# 8. Clientes
node scripts/run-clients-seed.js

# 9. Pacientes
node scripts/run-patients-seed.js

# 10. Horários de Trabalho
node scripts/run-working-hours-seed.js
```

## 🐳 DOCKER INDIVIDUAL

```bash
docker exec -it high-tide-systems-api node prisma/seed.js
docker exec -it high-tide-systems-api node prisma/seed-professions.js
docker exec -it high-tide-systems-api node prisma/seed-users.js
docker exec -it high-tide-systems-api node prisma/seed-insurances.js
docker exec -it high-tide-systems-api node prisma/seed-insurance-limits.js
docker exec -it high-tide-systems-api node prisma/seed-service-types.js
docker exec -it high-tide-systems-api node prisma/seed-locations.js
docker exec -it high-tide-systems-api node prisma/seed-clients.js
docker exec -it high-tide-systems-api node prisma/seed-patients.js
docker exec -it high-tide-systems-api node prisma/seed-working-hours.js
```

## 📚 DOCUMENTAÇÃO

- `README-SEEDS.md` - Guia completo
- `scripts/README-ALL-SEEDS.md` - Execução completa
- `scripts/README-ALL-SEEDS-DOCKER.md` - Docker

## ⚠️ IMPORTANTE

- **Ordem**: Execute na ordem correta (use `node seed-all.js`)
- **Senha**: Todos os usuários têm senha `123456`
- **Docker**: Container `high-tide-systems-api` deve estar rodando
- **Status**: Use `node seed-all.js --status` para verificar progresso
