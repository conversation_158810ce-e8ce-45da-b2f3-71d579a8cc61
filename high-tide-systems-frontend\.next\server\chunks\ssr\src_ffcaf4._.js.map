{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/tutorial/TutorialTriggerButton.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { HelpCircle } from 'lucide-react';\r\nimport { useTutorial } from '@/contexts/TutorialContext';\r\n\r\n/**\r\n * <PERSON>t<PERSON> para iniciar um tutorial\r\n * \r\n * @param {Object} props - Propriedades do componente\r\n * @param {Array} props.steps - Etapas do tutorial\r\n * @param {string} props.tutorialName - Nome único do tutorial\r\n * @param {boolean} props.showIfCompleted - Se deve mostrar o botão mesmo se o tutorial já foi concluído\r\n * @param {string} props.size - Tamanho do botão ('sm', 'md', 'lg')\r\n * @param {Object} props.className - Classes adicionais para o botão\r\n */\r\nconst TutorialTriggerButton = ({\r\n  steps,\r\n  tutorialName,\r\n  showIfCompleted = true,\r\n  size = 'md',\r\n  className = '',\r\n  children\r\n}) => {\r\n  const { startTutorial, isTutorialCompleted } = useTutorial();\r\n\r\n  // Não renderizar o botão se o tutorial já foi concluído e showIfCompleted for falso\r\n  if (!showIfCompleted && isTutorialCompleted(tutorialName)) {\r\n    return null;\r\n  }\r\n\r\n  // Determinar tamanho do botão\r\n  const sizeClasses = {\r\n    sm: 'p-1.5 text-xs',\r\n    md: 'p-2 text-sm',\r\n    lg: 'p-2.5 text-base'\r\n  };\r\n\r\n  // Determinar tamanho do ícone\r\n  const iconSize = {\r\n    sm: 14,\r\n    md: 16,\r\n    lg: 20\r\n  };\r\n\r\n  const isCompleted = isTutorialCompleted(tutorialName);\r\n\r\n  return (\r\n    <button\r\n      onClick={() => startTutorial(steps, tutorialName)}\r\n      className={`\r\n        relative flex items-center gap-1.5 rounded-full \r\n        ${isCompleted ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : 'bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/40 dark:text-primary-300 dark:hover:bg-primary-800/60'}\r\n        transition-colors ${sizeClasses[size]} ${className}\r\n      `}\r\n      aria-label={`Abrir tutorial: ${tutorialName}`}\r\n      title={isCompleted ? \"Ver tutorial novamente\" : \"Ver tutorial\"}\r\n    >\r\n      <HelpCircle size={iconSize[size]} />\r\n      {children}\r\n      \r\n      {/* Indicador de novo para tutoriais não completados */}\r\n      {!isCompleted && (\r\n        <span className=\"absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-primary-500 dark:bg-primary-400\" />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default TutorialTriggerButton;"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAHA;;;;;AAMA;;;;;;;;;CASC,GACD,MAAM,wBAAwB,CAAC,EAC7B,KAAK,EACL,YAAY,EACZ,kBAAkB,IAAI,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,QAAQ,EACT;IACC,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAEzD,oFAAoF;IACpF,IAAI,CAAC,mBAAmB,oBAAoB,eAAe;QACzD,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,8BAA8B;IAC9B,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,oBAAoB;IAExC,qBACE,8OAAC;QACC,SAAS,IAAM,cAAc,OAAO;QACpC,WAAW,CAAC;;QAEV,EAAE,cAAc,2GAA2G,iIAAiI;0BAC1O,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU;MACrD,CAAC;QACD,cAAY,CAAC,gBAAgB,EAAE,cAAc;QAC7C,OAAO,cAAc,2BAA2B;;0BAEhD,8OAAC,kNAAA,CAAA,aAAU;gBAAC,MAAM,QAAQ,CAAC,KAAK;;;;;;YAC/B;YAGA,CAAC,6BACA,8OAAC;gBAAK,WAAU;;;;;;;;;;;;AAIxB;uCAEe"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/people/services/insuranceServiceLimitService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\n// Adaptador para processar os dados de limites de serviço\r\nconst processServiceLimits = (limits) => {\r\n  if (!limits || !Array.isArray(limits)) {\r\n    return [];\r\n  }\r\n\r\n  return limits.map(limit => {\r\n    // Garantir que os campos estejam disponíveis independentemente do formato da resposta\r\n    return {\r\n      ...limit,\r\n      // Usar os campos com letra maiúscula se disponíveis, caso contrário usar os campos com letra minúscula\r\n      insurance: limit.insurance || {},\r\n      serviceType: limit.serviceType || {},\r\n      person: limit.person || {},\r\n      // Adicionar referências normalizadas\r\n      Insurance: limit.Insurance || limit.insurance || {},\r\n      ServiceType: limit.ServiceType || limit.serviceType || {},\r\n      Person: limit.Person || limit.person || {}\r\n    };\r\n  });\r\n};\r\n\r\nexport const insuranceServiceLimitService = {\r\n  // Obter todos os limites com suporte a filtros\r\n  getAllLimits: async (filters = {}) => {\r\n    try {\r\n      const { search, personId, personIds, insuranceId, serviceTypeId, companyId, sortField, sortDirection } = filters;\r\n\r\n      // Construir parâmetros de consulta\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (personId) params.append('personId', personId);\r\n      if (insuranceId) params.append('insuranceId', insuranceId);\r\n      if (serviceTypeId) params.append('serviceTypeId', serviceTypeId);\r\n      if (companyId) params.append('companyId', companyId);\r\n      if (sortField) params.append('sortField', sortField);\r\n      if (sortDirection) params.append('sortDirection', sortDirection);\r\n\r\n      // Adicionar personIds como parâmetros separados com notação de array\r\n      if (personIds && personIds.length > 0) {\r\n        // Garantir que personIds seja um array\r\n        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        personIdsArray.forEach((id) => {\r\n          // Usar o mesmo nome de parâmetro para cada ID (sem índice)\r\n          params.append('personIds', id);\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de pacientes:\", personIdsArray);\r\n      } else {\r\n        console.log(\"Nenhum filtro de paciente aplicado\");\r\n      }\r\n\r\n      // Log para depuração dos parâmetros de ordenação\r\n      if (sortField) {\r\n        // Garantir que a direção seja uma string válida\r\n        const validDirection = sortDirection && ['asc', 'desc'].includes(sortDirection.toLowerCase())\r\n          ? sortDirection.toLowerCase()\r\n          : 'asc';\r\n\r\n        console.log(`Parâmetros de ordenação: campo=${sortField}, direção=${validDirection}`);\r\n\r\n        // Atualizar o parâmetro de direção com o valor normalizado\r\n        params.set('sortDirection', validDirection);\r\n      }\r\n\r\n      // Não enviamos parâmetros de paginação para o backend\r\n      // para garantir que recebemos todos os limites de uma vez\r\n\r\n      console.log(`Enviando requisição para: /insurance-service-limits?${params.toString()}`);\r\n      const response = await api.get(`/insurance-service-limits?${params.toString()}`);\r\n\r\n      console.log(\"Resposta da API:\", response.data);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      // Primeiro tentamos extrair usando o formato padrão\r\n      const extracted = extractData(response.data, 'limits', ['data']);\r\n\r\n      // Se não houver limites no formato padrão, processamos o array diretamente\r\n      if (extracted.limits && extracted.limits.length > 0) {\r\n        return {\r\n          limits: processServiceLimits(extracted.limits),\r\n          total: extracted.total,\r\n          pages: extracted.pages\r\n        };\r\n      } else {\r\n        // Processar o array diretamente se a API retornar apenas um array\r\n        const processedLimits = processServiceLimits(response.data);\r\n\r\n        return {\r\n          limits: processedLimits,\r\n          total: processedLimits.length,\r\n          pages: Math.ceil(processedLimits.length / 10)\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter limites para uma pessoa específica\r\n  getLimitsByPerson: async (personId) => {\r\n    try {\r\n      const response = await api.get(`/insurance-service-limits/person/${personId}`);\r\n      return processServiceLimits(response.data);\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter limites para uma combinação pessoa+convênio\r\n  getLimitsByPersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      const response = await api.get(`/insurance-service-limits/person/${personId}/insurance/${insuranceId}`);\r\n      return processServiceLimits(response.data);\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço por convênio:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Criar um novo limite\r\n  createLimit: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurance-service-limits', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar limite de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um limite existente\r\n  updateLimit: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/insurance-service-limits/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar limite de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um limite\r\n  deleteLimit: async (id) => {\r\n    try {\r\n      await api.delete(`/insurance-service-limits/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir limite de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Verificar se um agendamento está dentro dos limites\r\n  checkAppointmentLimit: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurance-service-limits/check', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao verificar limite de agendamento:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de limites de convênio com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, personIds, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportInsuranceLimits: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await insuranceServiceLimitService.getAllLimits({\r\n        ...filters,\r\n        // Não precisamos de paginação para exportação\r\n      });\r\n\r\n      // Extrair os dados dos limites\r\n      const limitsArray = response.limits || [];\r\n\r\n      // Log para debug da estrutura dos dados\r\n      console.log(\"Estrutura dos limites para exportação:\", limitsArray);\r\n      if (limitsArray.length > 0) {\r\n        console.log(\"Exemplo do primeiro limite:\", JSON.stringify(limitsArray[0], null, 2));\r\n        console.log(\"Propriedades disponíveis:\", Object.keys(limitsArray[0]));\r\n      }\r\n\r\n      // Se os limites não tiverem as informações completas, vamos tentar enriquecê-los\r\n      // Verificar se temos objetos aninhados ou apenas IDs\r\n      const needsEnrichment = limitsArray.length > 0 && (\r\n        (!limitsArray[0].Person && !limitsArray[0].person && !limitsArray[0].personFullName) ||\r\n        (!limitsArray[0].Insurance && !limitsArray[0].insurance && !limitsArray[0].insuranceName) ||\r\n        (!limitsArray[0].ServiceType && !limitsArray[0].serviceType && !limitsArray[0].serviceTypeName)\r\n      );\r\n\r\n      // Se precisarmos enriquecer os dados, vamos buscar as informações faltantes\r\n      if (needsEnrichment) {\r\n        console.log(\"Dados incompletos detectados, buscando informações adicionais...\");\r\n\r\n        // Coletar todos os IDs únicos\r\n        const personIds = [...new Set(limitsArray.filter(l => l.personId).map(l => l.personId))];\r\n        const insuranceIds = [...new Set(limitsArray.filter(l => l.insuranceId).map(l => l.insuranceId))];\r\n        const serviceTypeIds = [...new Set(limitsArray.filter(l => l.serviceTypeId).map(l => l.serviceTypeId))];\r\n\r\n        console.log(\"IDs para enriquecimento:\", { personIds, insuranceIds, serviceTypeIds });\r\n\r\n        try {\r\n          // Buscar informações de pacientes se necessário\r\n          if (personIds.length > 0) {\r\n            const personsResponse = await api.get('/persons', {\r\n              params: { ids: personIds.join(',') }\r\n            });\r\n            const persons = personsResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const personsMap = Array.isArray(persons)\r\n              ? persons.reduce((map, person) => {\r\n                  map[person.id] = person;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.personId && personsMap[limit.personId]) {\r\n                limit.Person = personsMap[limit.personId];\r\n              }\r\n            });\r\n          }\r\n\r\n          // Buscar informações de convênios se necessário\r\n          if (insuranceIds.length > 0) {\r\n            const insurancesResponse = await api.get('/insurances', {\r\n              params: { ids: insuranceIds.join(',') }\r\n            });\r\n            const insurances = insurancesResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const insurancesMap = Array.isArray(insurances)\r\n              ? insurances.reduce((map, insurance) => {\r\n                  map[insurance.id] = insurance;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.insuranceId && insurancesMap[limit.insuranceId]) {\r\n                limit.Insurance = insurancesMap[limit.insuranceId];\r\n              }\r\n            });\r\n          }\r\n\r\n          // Buscar informações de tipos de serviço se necessário\r\n          if (serviceTypeIds.length > 0) {\r\n            const serviceTypesResponse = await api.get('/service-types', {\r\n              params: { ids: serviceTypeIds.join(',') }\r\n            });\r\n            const serviceTypes = serviceTypesResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const serviceTypesMap = Array.isArray(serviceTypes)\r\n              ? serviceTypes.reduce((map, serviceType) => {\r\n                  map[serviceType.id] = serviceType;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.serviceTypeId && serviceTypesMap[limit.serviceTypeId]) {\r\n                limit.ServiceType = serviceTypesMap[limit.serviceTypeId];\r\n              }\r\n            });\r\n          }\r\n\r\n          console.log(\"Dados enriquecidos com sucesso\");\r\n        } catch (error) {\r\n          console.error(\"Erro ao enriquecer dados em lote:\", error);\r\n\r\n          // Tentar buscar individualmente para cada limite\r\n          console.log(\"Tentando buscar dados individualmente...\");\r\n\r\n          // Criar funções para buscar entidades individuais\r\n          const fetchPerson = async (id) => {\r\n            try {\r\n              const response = await api.get(`/persons/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar pessoa ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          const fetchInsurance = async (id) => {\r\n            try {\r\n              const response = await api.get(`/insurances/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar convênio ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          const fetchServiceType = async (id) => {\r\n            try {\r\n              const response = await api.get(`/service-types/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar tipo de serviço ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          // Buscar dados para cada limite individualmente\r\n          for (const limit of limitsArray) {\r\n            // Buscar pessoa se necessário\r\n            if (limit.personId && !limit.Person && !limit.person && !limit.personFullName) {\r\n              const person = await fetchPerson(limit.personId);\r\n              if (person) {\r\n                limit.Person = person;\r\n                console.log(`Pessoa ${limit.personId} encontrada individualmente`);\r\n              }\r\n            }\r\n\r\n            // Buscar convênio se necessário\r\n            if (limit.insuranceId && !limit.Insurance && !limit.insurance && !limit.insuranceName) {\r\n              const insurance = await fetchInsurance(limit.insuranceId);\r\n              if (insurance) {\r\n                limit.Insurance = insurance;\r\n                console.log(`Convênio ${limit.insuranceId} encontrado individualmente`);\r\n              }\r\n            }\r\n\r\n            // Buscar tipo de serviço se necessário\r\n            if (limit.serviceTypeId && !limit.ServiceType && !limit.serviceType && !limit.serviceTypeName) {\r\n              const serviceType = await fetchServiceType(limit.serviceTypeId);\r\n              if (serviceType) {\r\n                limit.ServiceType = serviceType;\r\n                console.log(`Tipo de serviço ${limit.serviceTypeId} encontrado individualmente`);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"patient\", header: \"Paciente\" },\r\n        { key: \"insurance\", header: \"Convênio\" },\r\n        { key: \"company\", header: \"Empresa\" },\r\n        { key: \"serviceType\", header: \"Tipo de Serviço\" },\r\n        {\r\n          key: \"monthlyLimit\",\r\n          header: \"Limite Mensal\",\r\n          format: (value) => value > 0 ? `${value}x por mês` : \"Ilimitado\"\r\n        },\r\n        { key: \"notes\", header: \"Observações\" },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = limitsArray.map(limit => {\r\n        // Extrair informações do paciente\r\n        let patientName = \"N/A\";\r\n        if (limit.Person && limit.Person.fullName) {\r\n          patientName = limit.Person.fullName;\r\n        } else if (limit.person && limit.person.fullName) {\r\n          patientName = limit.person.fullName;\r\n        } else if (limit.personFullName) {\r\n          patientName = limit.personFullName;\r\n        } else if (limit.personId) {\r\n          // Se tivermos apenas o ID, podemos tentar buscar o nome em outro lugar\r\n          patientName = `ID: ${limit.personId}`;\r\n        }\r\n\r\n        // Extrair informações do convênio\r\n        let insuranceName = \"N/A\";\r\n        if (limit.Insurance && limit.Insurance.name) {\r\n          insuranceName = limit.Insurance.name;\r\n        } else if (limit.insurance && limit.insurance.name) {\r\n          insuranceName = limit.insurance.name;\r\n        } else if (limit.insuranceName) {\r\n          insuranceName = limit.insuranceName;\r\n        } else if (limit.insuranceId) {\r\n          insuranceName = `ID: ${limit.insuranceId}`;\r\n        }\r\n\r\n        // Extrair informações do tipo de serviço\r\n        let serviceTypeName = \"N/A\";\r\n        if (limit.ServiceType && limit.ServiceType.name) {\r\n          serviceTypeName = limit.ServiceType.name;\r\n        } else if (limit.serviceType && limit.serviceType.name) {\r\n          serviceTypeName = limit.serviceType.name;\r\n        } else if (limit.serviceTypeName) {\r\n          serviceTypeName = limit.serviceTypeName;\r\n        } else if (limit.serviceTypeId) {\r\n          serviceTypeName = `ID: ${limit.serviceTypeId}`;\r\n        }\r\n\r\n        // Extrair informações da empresa\r\n        let companyName = \"\";\r\n        if (limit.Insurance?.company?.name) {\r\n          companyName = limit.Insurance.company.name;\r\n        } else if (limit.insurance?.company?.name) {\r\n          companyName = limit.insurance.company.name;\r\n        }\r\n\r\n        return {\r\n          patient: patientName,\r\n          insurance: insuranceName,\r\n          company: companyName,\r\n          serviceType: serviceTypeName,\r\n          monthlyLimit: limit.monthlyLimit || 0,\r\n          notes: limit.notes || \"\",\r\n          createdAt: limit.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.personIds && filters.personIds.length > 0) {\r\n        subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);\r\n      }\r\n      if (filters.insuranceId) {\r\n        subtitleParts.push(`Convênio: ${filters.insuranceName || filters.insuranceId}`);\r\n      }\r\n      if (filters.serviceTypeId) {\r\n        subtitleParts.push(`Tipo de Serviço: ${filters.serviceTypeName || filters.serviceTypeId}`);\r\n      }\r\n      if (filters.companyId) {\r\n        // Tentar encontrar o nome da empresa nos dados\r\n        const companyName = limitsArray.find(l =>\r\n          l.Insurance?.company?.id === filters.companyId ||\r\n          l.insurance?.company?.id === filters.companyId\r\n        )?.Insurance?.company?.name ||\r\n        limitsArray.find(l =>\r\n          l.Insurance?.company?.id === filters.companyId ||\r\n          l.insurance?.company?.id === filters.companyId\r\n        )?.insurance?.company?.name;\r\n\r\n        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"limites-convenio\",\r\n        columns,\r\n        title: \"Lista de Limites de Convênio\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar limites de convênio:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default insuranceServiceLimitService;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,0DAA0D;AAC1D,MAAM,uBAAuB,CAAC;IAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,SAAS;QACrC,OAAO,EAAE;IACX;IAEA,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,sFAAsF;QACtF,OAAO;YACL,GAAG,KAAK;YACR,uGAAuG;YACvG,WAAW,MAAM,SAAS,IAAI,CAAC;YAC/B,aAAa,MAAM,WAAW,IAAI,CAAC;YACnC,QAAQ,MAAM,MAAM,IAAI,CAAC;YACzB,qCAAqC;YACrC,WAAW,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI,CAAC;YAClD,aAAa,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,CAAC;YACxD,QAAQ,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,CAAC;QAC3C;IACF;AACF;AAEO,MAAM,+BAA+B;IAC1C,+CAA+C;IAC/C,cAAc,OAAO,UAAU,CAAC,CAAC;QAC/B,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;YAEzG,mCAAmC;YACnC,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;YACxC,IAAI,aAAa,OAAO,MAAM,CAAC,eAAe;YAC9C,IAAI,eAAe,OAAO,MAAM,CAAC,iBAAiB;YAClD,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,eAAe,OAAO,MAAM,CAAC,iBAAiB;YAElD,qEAAqE;YACrE,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,YAAY;oBAAC;iBAAU;gBAEzE,+CAA+C;gBAC/C,eAAe,OAAO,CAAC,CAAC;oBACtB,2DAA2D;oBAC3D,OAAO,MAAM,CAAC,aAAa;gBAC7B;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,iDAAiD;YACjD,IAAI,WAAW;gBACb,gDAAgD;gBAChD,MAAM,iBAAiB,iBAAiB;oBAAC;oBAAO;iBAAO,CAAC,QAAQ,CAAC,cAAc,WAAW,MACtF,cAAc,WAAW,KACzB;gBAEJ,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,UAAU,UAAU,EAAE,gBAAgB;gBAEpF,2DAA2D;gBAC3D,OAAO,GAAG,CAAC,iBAAiB;YAC9B;YAEA,sDAAsD;YACtD,0DAA0D;YAE1D,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,OAAO,QAAQ,IAAI;YACtF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,QAAQ,IAAI;YAE/E,QAAQ,GAAG,CAAC,oBAAoB,SAAS,IAAI;YAE7C,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM,YAAY,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,UAAU;gBAAC;aAAO;YAE/D,2EAA2E;YAC3E,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,GAAG,GAAG;gBACnD,OAAO;oBACL,QAAQ,qBAAqB,UAAU,MAAM;oBAC7C,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,KAAK;gBACxB;YACF,OAAO;gBACL,kEAAkE;gBAClE,MAAM,kBAAkB,qBAAqB,SAAS,IAAI;gBAE1D,OAAO;oBACL,QAAQ;oBACR,OAAO,gBAAgB,MAAM;oBAC7B,OAAO,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;gBAC5C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU;YAC7E,OAAO,qBAAqB,SAAS,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,4BAA4B,OAAO,UAAU;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,WAAW,EAAE,aAAa;YACtG,OAAO,qBAAqB,SAAS,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,6BAA6B;YAC7D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,aAAa,OAAO,IAAI;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,EAAE;YAClE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5D,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,0BAA0B,EAAE,IAAI;YAClD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,sDAAsD;IACtD,uBAAuB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,mCAAmC;YACnE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,uBAAuB,OAAO,SAAS,eAAe,MAAM;QAC1D,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,6BAA6B,YAAY,CAAC;gBAC/D,GAAG,OAAO;YAEZ;YAEA,+BAA+B;YAC/B,MAAM,cAAc,SAAS,MAAM,IAAI,EAAE;YAEzC,wCAAwC;YACxC,QAAQ,GAAG,CAAC,0CAA0C;YACtD,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,QAAQ,GAAG,CAAC,+BAA+B,KAAK,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;gBAChF,QAAQ,GAAG,CAAC,6BAA6B,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE;YACrE;YAEA,iFAAiF;YACjF,qDAAqD;YACrD,MAAM,kBAAkB,YAAY,MAAM,GAAG,KAAK,CAChD,AAAC,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,cAAc,IAClF,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,aAAa,IACvF,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,AAChG;YAEA,4EAA4E;YAC5E,IAAI,iBAAiB;gBACnB,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,YAAY;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;iBAAG;gBACxF,MAAM,eAAe;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;iBAAG;gBACjG,MAAM,iBAAiB;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;iBAAG;gBAEvG,QAAQ,GAAG,CAAC,4BAA4B;oBAAE;oBAAW;oBAAc;gBAAe;gBAElF,IAAI;oBACF,gDAAgD;oBAChD,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,MAAM,kBAAkB,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;4BAChD,QAAQ;gCAAE,KAAK,UAAU,IAAI,CAAC;4BAAK;wBACrC;wBACA,MAAM,UAAU,gBAAgB,IAAI,IAAI,EAAE;wBAE1C,2CAA2C;wBAC3C,MAAM,aAAa,MAAM,OAAO,CAAC,WAC7B,QAAQ,MAAM,CAAC,CAAC,KAAK;4BACnB,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG;4BACjB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,QAAQ,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,EAAE;gCAChD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ,CAAC;4BAC3C;wBACF;oBACF;oBAEA,gDAAgD;oBAChD,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,MAAM,qBAAqB,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,eAAe;4BACtD,QAAQ;gCAAE,KAAK,aAAa,IAAI,CAAC;4BAAK;wBACxC;wBACA,MAAM,aAAa,mBAAmB,IAAI,IAAI,EAAE;wBAEhD,2CAA2C;wBAC3C,MAAM,gBAAgB,MAAM,OAAO,CAAC,cAChC,WAAW,MAAM,CAAC,CAAC,KAAK;4BACtB,GAAG,CAAC,UAAU,EAAE,CAAC,GAAG;4BACpB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,WAAW,IAAI,aAAa,CAAC,MAAM,WAAW,CAAC,EAAE;gCACzD,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,WAAW,CAAC;4BACpD;wBACF;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,MAAM,uBAAuB,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,kBAAkB;4BAC3D,QAAQ;gCAAE,KAAK,eAAe,IAAI,CAAC;4BAAK;wBAC1C;wBACA,MAAM,eAAe,qBAAqB,IAAI,IAAI,EAAE;wBAEpD,2CAA2C;wBAC3C,MAAM,kBAAkB,MAAM,OAAO,CAAC,gBAClC,aAAa,MAAM,CAAC,CAAC,KAAK;4BACxB,GAAG,CAAC,YAAY,EAAE,CAAC,GAAG;4BACtB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,aAAa,IAAI,eAAe,CAAC,MAAM,aAAa,CAAC,EAAE;gCAC/D,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,aAAa,CAAC;4BAC1D;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBAEnD,iDAAiD;oBACjD,QAAQ,GAAG,CAAC;oBAEZ,kDAAkD;oBAClD,MAAM,cAAc,OAAO;wBACzB,IAAI;4BACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;4BAC/C,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;4BAC9C,OAAO;wBACT;oBACF;oBAEA,MAAM,iBAAiB,OAAO;wBAC5B,IAAI;4BACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;4BAClD,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;4BAChD,OAAO;wBACT;oBACF;oBAEA,MAAM,mBAAmB,OAAO;wBAC9B,IAAI;4BACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;4BACrD,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;4BACvD,OAAO;wBACT;oBACF;oBAEA,gDAAgD;oBAChD,KAAK,MAAM,SAAS,YAAa;wBAC/B,8BAA8B;wBAC9B,IAAI,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,cAAc,EAAE;4BAC7E,MAAM,SAAS,MAAM,YAAY,MAAM,QAAQ;4BAC/C,IAAI,QAAQ;gCACV,MAAM,MAAM,GAAG;gCACf,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,2BAA2B,CAAC;4BACnE;wBACF;wBAEA,gCAAgC;wBAChC,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,EAAE;4BACrF,MAAM,YAAY,MAAM,eAAe,MAAM,WAAW;4BACxD,IAAI,WAAW;gCACb,MAAM,SAAS,GAAG;gCAClB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,2BAA2B,CAAC;4BACxE;wBACF;wBAEA,uCAAuC;wBACvC,IAAI,MAAM,aAAa,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,eAAe,EAAE;4BAC7F,MAAM,cAAc,MAAM,iBAAiB,MAAM,aAAa;4BAC9D,IAAI,aAAa;gCACf,MAAM,WAAW,GAAG;gCACpB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,aAAa,CAAC,2BAA2B,CAAC;4BACjF;wBACF;oBACF;gBACF;YACF;YAEA,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAW,QAAQ;gBAAW;gBACrC;oBAAE,KAAK;oBAAa,QAAQ;gBAAW;gBACvC;oBAAE,KAAK;oBAAW,QAAQ;gBAAU;gBACpC;oBAAE,KAAK;oBAAe,QAAQ;gBAAkB;gBAChD;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG;gBACvD;gBACA;oBAAE,KAAK;oBAAS,QAAQ;gBAAc;gBACtC;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA;gBACnC,kCAAkC;gBAClC,IAAI,cAAc;gBAClB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,EAAE;oBACzC,cAAc,MAAM,MAAM,CAAC,QAAQ;gBACrC,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,EAAE;oBAChD,cAAc,MAAM,MAAM,CAAC,QAAQ;gBACrC,OAAO,IAAI,MAAM,cAAc,EAAE;oBAC/B,cAAc,MAAM,cAAc;gBACpC,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,uEAAuE;oBACvE,cAAc,CAAC,IAAI,EAAE,MAAM,QAAQ,EAAE;gBACvC;gBAEA,kCAAkC;gBAClC,IAAI,gBAAgB;gBACpB,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,EAAE;oBAC3C,gBAAgB,MAAM,SAAS,CAAC,IAAI;gBACtC,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,EAAE;oBAClD,gBAAgB,MAAM,SAAS,CAAC,IAAI;gBACtC,OAAO,IAAI,MAAM,aAAa,EAAE;oBAC9B,gBAAgB,MAAM,aAAa;gBACrC,OAAO,IAAI,MAAM,WAAW,EAAE;oBAC5B,gBAAgB,CAAC,IAAI,EAAE,MAAM,WAAW,EAAE;gBAC5C;gBAEA,yCAAyC;gBACzC,IAAI,kBAAkB;gBACtB,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;oBAC/C,kBAAkB,MAAM,WAAW,CAAC,IAAI;gBAC1C,OAAO,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;oBACtD,kBAAkB,MAAM,WAAW,CAAC,IAAI;gBAC1C,OAAO,IAAI,MAAM,eAAe,EAAE;oBAChC,kBAAkB,MAAM,eAAe;gBACzC,OAAO,IAAI,MAAM,aAAa,EAAE;oBAC9B,kBAAkB,CAAC,IAAI,EAAE,MAAM,aAAa,EAAE;gBAChD;gBAEA,iCAAiC;gBACjC,IAAI,cAAc;gBAClB,IAAI,MAAM,SAAS,EAAE,SAAS,MAAM;oBAClC,cAAc,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;gBAC5C,OAAO,IAAI,MAAM,SAAS,EAAE,SAAS,MAAM;oBACzC,cAAc,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;gBAC5C;gBAEA,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,cAAc,MAAM,YAAY,IAAI;oBACpC,OAAO,MAAM,KAAK,IAAI;oBACtB,WAAW,MAAM,SAAS,IAAI;gBAChC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACtF;YACA,IAAI,QAAQ,WAAW,EAAE;gBACvB,cAAc,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,aAAa,IAAI,QAAQ,WAAW,EAAE;YAChF;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,cAAc,IAAI,CAAC,CAAC,iBAAiB,EAAE,QAAQ,eAAe,IAAI,QAAQ,aAAa,EAAE;YAC3F;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,+CAA+C;gBAC/C,MAAM,cAAc,YAAY,IAAI,CAAC,CAAA,IACnC,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,IAC9C,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,GAC7C,WAAW,SAAS,QACvB,YAAY,IAAI,CAAC,CAAA,IACf,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,IAC9C,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,GAC7C,WAAW,SAAS;gBAEvB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE;YACnE;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/people/services/insurancesService.js"], "sourcesContent": ["// app/modules/people/services/insurancesService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\nexport const insurancesService = {\r\n  // Obter lista de convênios (com suporte a filtragem)\r\n  getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (companyId) params.append('companyId', companyId);\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n\r\n      // Adicionar insuranceIds como parâmetros separados com notação de array\r\n      if (insuranceIds && insuranceIds.length > 0) {\r\n        // Garantir que insuranceIds seja um array\r\n        const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [insuranceIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        insuranceIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params.append(`insuranceIds[${index}]`, id);\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de convênios:\", insuranceIdsArray);\r\n      }\r\n\r\n      const response = await api.get(`/insurances?${params.toString()}`);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      // Primeiro tentamos extrair usando o formato padrão\r\n      const extracted = extractData(response.data, 'insurances', ['data']);\r\n\r\n      // Se não houver insurances no formato padrão, processamos o array diretamente\r\n      if (extracted.insurances && extracted.insurances.length > 0) {\r\n        return extracted;\r\n      } else {\r\n        // Processar o array diretamente se a API retornar apenas um array\r\n        const insurances = Array.isArray(response.data) ? response.data : [];\r\n        return {\r\n          insurances,\r\n          total: insurances.length,\r\n          pages: Math.ceil(insurances.length / limit)\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar convênios:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio específico\r\n  getInsurance: async (id) => {\r\n    try {\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio pelo ID (versão simplificada que não lança erro)\r\n  getInsuranceById: async (id) => {\r\n    try {\r\n      console.log(`Buscando convênio com ID: ${id}`);\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio com ID ${id}:`, error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Criar um novo convênio\r\n  createInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar convênio:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um convênio existente\r\n  updateInsurance: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um convênio\r\n  deleteInsurance: async (id) => {\r\n    try {\r\n      await api.delete(`/insurances/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Adicionar um convênio a uma pessoa\r\n  addPersonInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances/person', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao adicionar convênio à pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remover um convênio de uma pessoa\r\n  removePersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      await api.delete(`/insurances/person/${personId}/${insuranceId}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erro ao remover convênio da pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar convênios de uma pessoa - VERSÃO MELHORADA\r\n  listPersonInsurances: async (personId) => {\r\n    try {\r\n      console.log(`Buscando convênios para a pessoa ID: ${personId}`);\r\n      const response = await api.get(`/insurances/person/${personId}`);\r\n\r\n      // Detecta a estrutura retornada e normaliza\r\n      const data = response.data;\r\n\r\n      // Log para debug da estrutura de dados\r\n      console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);\r\n\r\n      // Se for array, retorna diretamente\r\n      if (Array.isArray(data)) {\r\n        console.log(`Encontrados ${data.length} convênios no formato de array`);\r\n        return data;\r\n      }\r\n      // Se for objeto, procura por arrays\r\n      else if (data && typeof data === 'object') {\r\n        // Tenta encontrar um array dentro do objeto\r\n        const possibleArrayProps = ['personInsurances', 'insurances', 'data', 'items', 'results'];\r\n\r\n        // Primeiro procura nas propriedades comuns\r\n        for (const prop of possibleArrayProps) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Se não encontrou nas propriedades comuns, verifica todas as propriedades\r\n        for (const prop of Object.keys(data)) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Tenta extrair informações de convênios mesmo que não estejam em um array direto\r\n        if (data.insurance || data.insuranceId) {\r\n          console.log(`Encontrado um único convênio em formato não-array`);\r\n          const insurance = {\r\n            id: data.insuranceId || data.insurance?.id,\r\n            name: data.insuranceName || data.insurance?.name,\r\n            policyNumber: data.policyNumber,\r\n            validUntil: data.validUntil\r\n          };\r\n\r\n          return [insurance];\r\n        }\r\n      }\r\n\r\n      // Se não conseguir encontrar nenhum array, retorna array vazio\r\n      console.warn(\"Estrutura de resposta inesperada:\", data);\r\n      return [];\r\n    } catch (error) {\r\n      console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);\r\n      // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de convênios com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportInsurances: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await insurancesService.getInsurances({\r\n        ...filters,\r\n        limit: 1000 // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados dos convênios\r\n      const insurancesArray = response.insurances || [];\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome\" },\r\n        {\r\n          key: \"companyName\",\r\n          header: \"Empresa\"\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = insurancesArray.map(insurance => {\r\n        return {\r\n          name: insurance.name || \"\",\r\n          companyName: insurance.company && insurance.company.name ? insurance.company.name : \"\",\r\n          createdAt: insurance.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.insuranceIds && filters.insuranceIds.length > 0) {\r\n        subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);\r\n      }\r\n      if (filters.companyId) {\r\n        // Tentar encontrar o nome da empresa nos dados\r\n        const companyName = insurancesArray.find(i => i.company && i.company.id === filters.companyId)?.company?.name;\r\n        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"convenios\",\r\n        columns,\r\n        title: \"Lista de Convênios\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar convênios:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  updatePersonInsurance: async (personId, insuranceId, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/person/${personId}/${insuranceId}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter detalhes de um convênio específico de uma pessoa\r\n  getPersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      const response = await api.get(`/insurances/person/${personId}/${insuranceId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default insurancesService;"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AACnD;AAGA;AACA;AAHA;AACA;;;;;;AAIO,MAAM,oBAAoB;IAC/B,qDAAqD;IACrD,eAAe,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;QAClF,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAElC,wEAAwE;YACxE,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;gBAC3C,0CAA0C;gBAC1C,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe;oBAAC;iBAAa;gBAErF,+CAA+C;gBAC/C,kBAAkB,OAAO,CAAC,CAAC,IAAI;oBAC7B,yDAAyD;oBACzD,OAAO,MAAM,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,EAAE;gBAC1C;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;YAEjE,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM,YAAY,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,cAAc;gBAAC;aAAO;YAEnE,8EAA8E;YAC9E,IAAI,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,MAAM,GAAG,GAAG;gBAC3D,OAAO;YACT,OAAO;gBACL,kEAAkE;gBAClE,MAAM,aAAa,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,GAAG,EAAE;gBACpE,OAAO;oBACL;oBACA,OAAO,WAAW,MAAM;oBACxB,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,qEAAqE;IACrE,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI;YAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe;YAC/C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,iBAAiB,OAAO,IAAI;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YACpD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,EAAE;YACnD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YACpC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,EAAE;YACjD,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,sBAAsB;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,uBAAuB,OAAO,UAAU;QACtC,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAChE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,sBAAsB,OAAO;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAC9D,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAE/D,4CAA4C;YAC5C,MAAM,OAAO,SAAS,IAAI;YAE1B,uCAAuC;YACvC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,SAAS,EAAE,CAAC,EAAE;YAExE,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,8BAA8B,CAAC;gBACtE,OAAO;YACT,OAEK,IAAI,QAAQ,OAAO,SAAS,UAAU;gBACzC,4CAA4C;gBAC5C,MAAM,qBAAqB;oBAAC;oBAAoB;oBAAc;oBAAQ;oBAAS;iBAAU;gBAEzF,2CAA2C;gBAC3C,KAAK,MAAM,QAAQ,mBAAoB;oBACrC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,2EAA2E;gBAC3E,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAO;oBACpC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,kFAAkF;gBAClF,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,EAAE;oBACtC,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;oBAC/D,MAAM,YAAY;wBAChB,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS,EAAE;wBACxC,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,EAAE;wBAC5C,cAAc,KAAK,YAAY;wBAC/B,YAAY,KAAK,UAAU;oBAC7B;oBAEA,OAAO;wBAAC;qBAAU;gBACpB;YACF;YAEA,+DAA+D;YAC/D,QAAQ,IAAI,CAAC,qCAAqC;YAClD,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE,+EAA+E;YAC/E,OAAO,EAAE;QACX;IACF;IAEA;;;;;GAKC,GACD,kBAAkB,OAAO,SAAS,eAAe,MAAM;QACrD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,kBAAkB,aAAa,CAAC;gBACrD,GAAG,OAAO;gBACV,OAAO,KAAK,+CAA+C;YAC7D;YAEA,iCAAiC;YACjC,MAAM,kBAAkB,SAAS,UAAU,IAAI,EAAE;YAEjD,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAO;gBAC9B;oBACE,KAAK;oBACL,QAAQ;gBACV;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAA;gBACvC,OAAO;oBACL,MAAM,UAAU,IAAI,IAAI;oBACxB,aAAa,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,IAAI,GAAG,UAAU,OAAO,CAAC,IAAI,GAAG;oBACpF,WAAW,UAAU,SAAS,IAAI;gBACpC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC3D,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;YACzF;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,+CAA+C;gBAC/C,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,EAAE,KAAK,QAAQ,SAAS,GAAG,SAAS;gBACzG,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE;YACnE;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,uBAAuB,OAAO,UAAU,aAAa;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE;YAChF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yDAAyD;IACzD,oBAAoB,OAAO,UAAU;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAC9E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAC3F,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/scheduler/services/serviceTypeService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\n\r\nexport const serviceTypeService = {\r\n  // Listar tipos de serviço com suporte a filtros\r\n  getServiceTypes: async ({ search, companyId, serviceTypeIds } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (companyId) params.append('companyId', companyId);\r\n\r\n      // Adicionar suporte para múltiplos IDs de tipos de serviço\r\n      if (serviceTypeIds && Array.isArray(serviceTypeIds) && serviceTypeIds.length > 0) {\r\n        serviceTypeIds.forEach(id => params.append('serviceTypeIds', id));\r\n      }\r\n\r\n      const response = await api.get(`/service-types?${params.toString()}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar tipos de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um tipo de serviço específico\r\n  getServiceType: async (id) => {\r\n    try {\r\n      const response = await api.get(`/service-types/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Criar um novo tipo de serviço\r\n  createServiceType: async (data) => {\r\n    try {\r\n      const response = await api.post('/service-types', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar tipo de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um tipo de serviço existente\r\n  updateServiceType: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/service-types/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um tipo de serviço\r\n  deleteServiceType: async (id) => {\r\n    try {\r\n      await api.delete(`/service-types/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  /**\r\n * Exporta a lista de tipos de serviço com os filtros aplicados\r\n * @param {Object} filters - Filtros atuais (busca, companyId, etc)\r\n * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n */\r\n  exportServiceTypes: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await serviceTypeService.getServiceTypes(filters);\r\n\r\n      // Extrair os dados dos tipos de serviço\r\n      const data = response?.serviceTypes || [];\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome do Serviço\" },\r\n        {\r\n          key: \"value\",\r\n          header: \"Valor\",\r\n          format: (value) => new Intl.NumberFormat(\"pt-BR\", {\r\n            style: \"currency\",\r\n            currency: \"BRL\",\r\n          }).format(value),\r\n          align: \"right\",\r\n          width: 25\r\n        },\r\n        {\r\n          key: \"companyName\",\r\n          header: \"Empresa\",\r\n          format: (value, item) => item.company ? item.company.name : \"N/A\"\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(serviceType => {\r\n        return {\r\n          name: serviceType.name || \"\",\r\n          value: serviceType.value || 0,\r\n          companyName: serviceType.company ? serviceType.company.name : \"N/A\",\r\n          createdAt: serviceType.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.companyId) {\r\n        const companyName = data.length > 0 && data[0].company ? data[0].company.name : \"Selecionada\";\r\n        subtitleParts.push(`Empresa: ${companyName}`);\r\n      }\r\n      if (filters.serviceTypeIds && Array.isArray(filters.serviceTypeIds) && filters.serviceTypeIds.length > 0) {\r\n        subtitleParts.push(`Tipos de Serviço: ${filters.serviceTypeIds.length} selecionados`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"tipos-de-servico\",\r\n        columns,\r\n        title: \"Lista de Tipos de Serviço\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar tipos de serviço:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default serviceTypeService;"], "names": [], "mappings": ";;;;AAAA;AAGA;AAFA;AACA;;;;;AAIO,MAAM,qBAAqB;IAChC,gDAAgD;IAChD,iBAAiB,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAE1C,2DAA2D;YAC3D,IAAI,kBAAkB,MAAM,OAAO,CAAC,mBAAmB,eAAe,MAAM,GAAG,GAAG;gBAChF,eAAe,OAAO,CAAC,CAAA,KAAM,OAAO,MAAM,CAAC,kBAAkB;YAC/D;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;YACpE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;YACrD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,kBAAkB;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,mBAAmB,OAAO,IAAI;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;YACvD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IACA;;;;;CAKD,GACC,oBAAoB,OAAO,SAAS,eAAe,MAAM;QACvD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,mBAAmB,eAAe,CAAC;YAE1D,wCAAwC;YACxC,MAAM,OAAO,UAAU,gBAAgB,EAAE;YAEzC,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAkB;gBACzC;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,IAAI,KAAK,YAAY,CAAC,SAAS;4BAChD,OAAO;4BACP,UAAU;wBACZ,GAAG,MAAM,CAAC;oBACV,OAAO;oBACP,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,OAAO,OAAS,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,IAAI,GAAG;gBAC9D;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,OAAO;oBACL,MAAM,YAAY,IAAI,IAAI;oBAC1B,OAAO,YAAY,KAAK,IAAI;oBAC5B,aAAa,YAAY,OAAO,GAAG,YAAY,OAAO,CAAC,IAAI,GAAG;oBAC9D,WAAW,YAAY,SAAS,IAAI;gBACtC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,cAAc,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,GAAG;gBAChF,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,aAAa;YAC9C;YACA,IAAI,QAAQ,cAAc,IAAI,MAAM,OAAO,CAAC,QAAQ,cAAc,KAAK,QAAQ,cAAc,CAAC,MAAM,GAAG,GAAG;gBACxG,cAAc,IAAI,CAAC,CAAC,kBAAkB,EAAE,QAAQ,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC;YACtF;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/admin/services/companyService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\n\r\nexport const companyService = {\r\n  // Obter a empresa atual do usuário autenticado\r\n  getCurrentCompany: async () => {\r\n    try {\r\n      const response = await api.get('/companies/current');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresa atual:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar empresas com suporte a paginação e filtros\r\n  getCompanies: async ({ page = 1, limit = 10, search, active } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n      if (search) params.append('search', search);\r\n      if (active !== undefined) params.append('active', active);\r\n\r\n      const response = await api.get(`/companies?${params.toString()}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter uma empresa específica\r\n  getCompany: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter lista de empresas para formulários de seleção\r\n  getCompaniesForSelect: async () => {\r\n    try {\r\n      const response = await api.get(\"/companies/select\");\r\n      return response.data.companies || [];\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas para seleção:\", error);\r\n      // Retorna array vazio em caso de erro para facilitar o manuseio no frontend\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Criar uma nova empresa\r\n  createCompany: async (formData) => {\r\n    try {\r\n      const response = await api.post('/companies', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar empresa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar uma empresa existente\r\n  updateCompany: async (id, formData) => {\r\n    try {\r\n      const response = await api.put(`/companies/${id}`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Alternar o status de uma empresa (ativo/inativo)\r\n  toggleCompanyStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/companies/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao alterar status da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir uma empresa\r\n  deleteCompany: async (id) => {\r\n    try {\r\n      await api.delete(`/companies/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter o logo de uma empresa\r\n  getCompanyLogo: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}/logo`, {\r\n        responseType: 'blob'\r\n      });\r\n      return URL.createObjectURL(response.data);\r\n    } catch (error) {\r\n      console.error(`Erro ao obter logo da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default companyService;"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,iBAAiB;IAC5B,+CAA+C;IAC/C,mBAAmB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,cAAc,OAAO,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAClC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,WAAW,OAAO,MAAM,CAAC,UAAU;YAElD,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,sDAAsD;IACtD,uBAAuB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,4EAA4E;YAC5E,OAAO,EAAE;QACX;IACF;IAEA,yBAAyB;IACzB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,cAAc,UAAU;gBACtD,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,eAAe,OAAO,IAAI;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU;gBAC3D,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,EAAE;YAClD,MAAM;QACR;IACF;IAEA,mDAAmD;IACnD,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC;YAC1D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,EAAE;gBACtD,cAAc;YAChB;YACA,OAAO,IAAI,eAAe,CAAC,SAAS,IAAI;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/people/InsuranceLimitFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { CreditCard, FileText, CreditCard as CardIcon } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { insuranceServiceLimitService } from \"@/app/modules/people/services/insuranceServiceLimitService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { serviceTypeService } from \"@/app/modules/scheduler/services/serviceTypeService\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\n\r\nconst InsuranceLimitFormModal = ({ isOpen, onClose, limit = null, onSuccess, personId = null }) => {\r\n  const { toast_success, toast_error } = useToast();\r\n  const [formData, setFormData] = useState({\r\n    personId: personId || \"\",\r\n    insuranceId: \"\",\r\n    serviceTypeId: \"\",\r\n    monthlyLimit: 0,\r\n    notes: \"\"\r\n  });\r\n  const [persons, setPersons] = useState([]);\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [serviceTypes, setServiceTypes] = useState([]);\r\n  const [personInsurances, setPersonInsurances] = useState([]);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isLoadingOptions, setIsLoadingOptions] = useState(false);\r\n\r\n  // Carregar dados iniciais\r\n  useEffect(() => {\r\n    loadOptions();\r\n  }, []);\r\n\r\n  // Carregar opções para os selects\r\n  const loadOptions = async () => {\r\n    setIsLoadingOptions(true);\r\n    try {\r\n      // Carregar pessoas (se não tiver personId fixo)\r\n      if (!personId) {\r\n        const personsData = await personsService.getPersons({ limit: 100 });\r\n        const personsArray = personsData?.persons || personsData?.people || personsData?.data || [];\r\n        setPersons(personsArray);\r\n      }\r\n\r\n      // Carregar tipos de serviço\r\n      const serviceTypesData = await serviceTypeService.getServiceTypes();\r\n      const serviceTypesArray = serviceTypesData?.serviceTypes || serviceTypesData?.data || [];\r\n      setServiceTypes(serviceTypesArray);\r\n\r\n      // Carregar convênios (todos)\r\n      const insurancesData = await insurancesService.getInsurances();\r\n      const insurancesArray = insurancesData?.insurances || insurancesData?.data || [];\r\n      setInsurances(insurancesArray);\r\n\r\n      // Se tiver personId, carregar convênios da pessoa\r\n      if (formData.personId) {\r\n        loadPersonInsurances(formData.personId);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções:\", error);\r\n      toast_error(\"Erro ao carregar opções. Por favor, tente novamente.\");\r\n    } finally {\r\n      setIsLoadingOptions(false);\r\n    }\r\n  };\r\n\r\n  // Carregar convênios da pessoa selecionada\r\n  const loadPersonInsurances = async (personId) => {\r\n    if (!personId) return;\r\n\r\n    try {\r\n      const data = await insurancesService.listPersonInsurances(personId);\r\n      const personInsurancesArray = data?.insurances || data || [];\r\n      setPersonInsurances(personInsurancesArray);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar convênios da pessoa:\", error);\r\n      toast_error(\"Erro ao carregar convênios da pessoa.\");\r\n    }\r\n  };\r\n\r\n  // Preencher formulário quando editando\r\n  useEffect(() => {\r\n    if (limit) {\r\n      setFormData({\r\n        personId: limit.personId || \"\",\r\n        insuranceId: limit.insuranceId || \"\",\r\n        serviceTypeId: limit.serviceTypeId || \"\",\r\n        monthlyLimit: limit.monthlyLimit || 0,\r\n        notes: limit.notes || \"\"\r\n      });\r\n\r\n      // Carregar convênios da pessoa se tiver personId\r\n      if (limit.personId) {\r\n        loadPersonInsurances(limit.personId);\r\n      }\r\n    } else if (personId) {\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        personId\r\n      }));\r\n      loadPersonInsurances(personId);\r\n    }\r\n  }, [limit, personId]);\r\n\r\n  // Atualizar convênios quando a pessoa mudar\r\n  useEffect(() => {\r\n    if (formData.personId) {\r\n      loadPersonInsurances(formData.personId);\r\n    }\r\n  }, [formData.personId]);\r\n\r\n  // Manipuladores de eventos\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Converter para número quando for monthlyLimit\r\n    if (name === \"monthlyLimit\") {\r\n      setFormData({\r\n        ...formData,\r\n        [name]: parseInt(value, 10) || 0\r\n      });\r\n    } else {\r\n      setFormData({\r\n        ...formData,\r\n        [name]: value\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n\r\n    // Validar dados\r\n    if (!formData.personId || !formData.insuranceId || !formData.serviceTypeId) {\r\n      toast_error(\"Por favor, preencha todos os campos obrigatórios.\");\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (limit) {\r\n        // Modo de edição\r\n        await insuranceServiceLimitService.updateLimit(limit.id, {\r\n          monthlyLimit: formData.monthlyLimit,\r\n          notes: formData.notes\r\n        });\r\n        toast_success(\"Limite de convênio atualizado com sucesso\");\r\n      } else {\r\n        // Modo de adição\r\n        await insuranceServiceLimitService.createLimit(formData);\r\n        toast_success(\"Limite de convênio criado com sucesso\");\r\n      }\r\n\r\n      onSuccess();\r\n      onClose();\r\n    } catch (err) {\r\n      console.error(\"Erro ao salvar limite de convênio:\", err);\r\n      toast_error(err.response?.data?.message || \"Ocorreu um erro ao salvar o limite de convênio.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Normalizar dados de convênios da pessoa\r\n  const normalizePersonInsurances = () => {\r\n    if (!personInsurances || !Array.isArray(personInsurances) || personInsurances.length === 0) return [];\r\n\r\n    return personInsurances.map(ins => {\r\n      if (!ins) return null;\r\n      // Se o objeto já tiver a estrutura correta\r\n      if (ins.insurance) {\r\n        return {\r\n          id: ins.insurance.id,\r\n          name: ins.insurance.name\r\n        };\r\n      }\r\n      // Se for um objeto de convênio direto\r\n      else if (ins.id && ins.name) {\r\n        return {\r\n          id: ins.id,\r\n          name: ins.name\r\n        };\r\n      }\r\n      // Se tiver apenas o ID do convênio\r\n      else if (ins.insuranceId) {\r\n        // Buscar o nome no array de todos os convênios\r\n        const insuranceDetails = Array.isArray(insurances) ? insurances.find(i => i?.id === ins.insuranceId) : null;\r\n        return {\r\n          id: ins.insuranceId,\r\n          name: insuranceDetails?.name || `Convênio ${ins.insuranceId}`\r\n        };\r\n      }\r\n      return null;\r\n    }).filter(Boolean);\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isSubmitting}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"insurance-limit-form\"\r\n        isLoading={isSubmitting}\r\n      >\r\n        {limit ? \"Atualizar\" : \"Salvar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={limit ? \"Editar Limite de Convênio\" : \"Novo Limite de Convênio\"}\r\n      icon={<CreditCard size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"md\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"insurance-limit-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6\">\r\n          <div>\r\n            <h4 className=\"text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4\">\r\n              <CreditCard className=\"w-4 h-4\" />\r\n              Informações do Limite\r\n            </h4>\r\n          </div>\r\n\r\n          {/* Seleção de Pessoa (se não tiver personId fixo) */}\r\n          {!personId && (\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Paciente\"\r\n              htmlFor=\"personId\"\r\n              icon={<CardIcon size={16} />}\r\n              required\r\n            >\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                id=\"personId\"\r\n                name=\"personId\"\r\n                value={formData.personId}\r\n                onChange={handleChange}\r\n                disabled={isLoadingOptions || isSubmitting || !!limit}\r\n                required\r\n                placeholder=\"Selecione um paciente\"\r\n              >\r\n                {Array.isArray(persons) && persons.map((person) => (\r\n                  <option key={person?.id} value={person?.id}>\r\n                    {person?.fullName || 'Sem nome'}\r\n                  </option>\r\n                ))}\r\n              </ModuleSelect>\r\n            </ModuleFormGroup>\r\n          )}\r\n\r\n          {/* Seleção de Convênio */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Convênio\"\r\n            htmlFor=\"insuranceId\"\r\n            icon={<CardIcon size={16} />}\r\n            required\r\n            helpText={formData.personId && normalizePersonInsurances().length === 0 ? \"Este paciente não possui convênios associados.\" : \"\"}\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"insuranceId\"\r\n              name=\"insuranceId\"\r\n              value={formData.insuranceId}\r\n              onChange={handleChange}\r\n              disabled={isLoadingOptions || isSubmitting || !formData.personId || !!limit}\r\n              required\r\n              placeholder=\"Selecione um convênio\"\r\n            >\r\n              {Array.isArray(normalizePersonInsurances()) && normalizePersonInsurances().map((insurance) => (\r\n                <option key={insurance?.id} value={insurance?.id}>\r\n                  {insurance?.name || 'Sem nome'}\r\n                </option>\r\n              ))}\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n\r\n          {/* Seleção de Tipo de Serviço */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Tipo de Serviço\"\r\n            htmlFor=\"serviceTypeId\"\r\n            icon={<FileText size={16} />}\r\n            required\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"serviceTypeId\"\r\n              name=\"serviceTypeId\"\r\n              value={formData.serviceTypeId}\r\n              onChange={handleChange}\r\n              disabled={isLoadingOptions || isSubmitting || !!limit}\r\n              required\r\n              placeholder=\"Selecione um tipo de serviço\"\r\n            >\r\n              {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (\r\n                <option key={serviceType?.id} value={serviceType?.id}>\r\n                  {serviceType?.name || 'Sem nome'}\r\n                </option>\r\n              ))}\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n\r\n          {/* Limite Mensal */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Limite Mensal\"\r\n            htmlFor=\"monthlyLimit\"\r\n            icon={<CardIcon size={16} />}\r\n            helpText=\"0 = ilimitado\"\r\n          >\r\n            <ModuleInput\r\n              moduleColor=\"people\"\r\n              type=\"number\"\r\n              id=\"monthlyLimit\"\r\n              name=\"monthlyLimit\"\r\n              value={formData.monthlyLimit}\r\n              onChange={handleChange}\r\n              min=\"0\"\r\n              disabled={isSubmitting}\r\n            />\r\n          </ModuleFormGroup>\r\n\r\n          {/* Observações */}\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Observações\"\r\n            htmlFor=\"notes\"\r\n            icon={<FileText size={16} />}\r\n          >\r\n            <ModuleTextarea\r\n              moduleColor=\"people\"\r\n              id=\"notes\"\r\n              name=\"notes\"\r\n              value={formData.notes}\r\n              onChange={handleChange}\r\n              rows={3}\r\n              disabled={isSubmitting}\r\n            />\r\n          </ModuleFormGroup>\r\n\r\n        </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default InsuranceLimitFormModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AAAA;AADA;AACA;AAAA;AADA;AACA;AAAA;;;;;;;;;;AAOA,MAAM,0BAA0B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,SAAS,EAAE,WAAW,IAAI,EAAE;IAC5F,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU,YAAY;QACtB,aAAa;QACb,eAAe;QACf,cAAc;QACd,OAAO;IACT;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,cAAc;QAClB,oBAAoB;QACpB,IAAI;YACF,gDAAgD;YAChD,IAAI,CAAC,UAAU;gBACb,MAAM,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAI;gBACjE,MAAM,eAAe,aAAa,WAAW,aAAa,UAAU,aAAa,QAAQ,EAAE;gBAC3F,WAAW;YACb;YAEA,4BAA4B;YAC5B,MAAM,mBAAmB,MAAM,oKAAA,CAAA,qBAAkB,CAAC,eAAe;YACjE,MAAM,oBAAoB,kBAAkB,gBAAgB,kBAAkB,QAAQ,EAAE;YACxF,gBAAgB;YAEhB,6BAA6B;YAC7B,MAAM,iBAAiB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,aAAa;YAC5D,MAAM,kBAAkB,gBAAgB,cAAc,gBAAgB,QAAQ,EAAE;YAChF,cAAc;YAEd,kDAAkD;YAClD,IAAI,SAAS,QAAQ,EAAE;gBACrB,qBAAqB,SAAS,QAAQ;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,2CAA2C;IAC3C,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,OAAO,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;YAC1D,MAAM,wBAAwB,MAAM,cAAc,QAAQ,EAAE;YAC5D,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,YAAY;gBACV,UAAU,MAAM,QAAQ,IAAI;gBAC5B,aAAa,MAAM,WAAW,IAAI;gBAClC,eAAe,MAAM,aAAa,IAAI;gBACtC,cAAc,MAAM,YAAY,IAAI;gBACpC,OAAO,MAAM,KAAK,IAAI;YACxB;YAEA,iDAAiD;YACjD,IAAI,MAAM,QAAQ,EAAE;gBAClB,qBAAqB,MAAM,QAAQ;YACrC;QACF,OAAO,IAAI,UAAU;YACnB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP;gBACF,CAAC;YACD,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ,EAAE;YACrB,qBAAqB,SAAS,QAAQ;QACxC;IACF,GAAG;QAAC,SAAS,QAAQ;KAAC;IAEtB,2BAA2B;IAC3B,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,gDAAgD;QAChD,IAAI,SAAS,gBAAgB;YAC3B,YAAY;gBACV,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE,SAAS,OAAO,OAAO;YACjC;QACF,OAAO;YACL,YAAY;gBACV,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,gBAAgB;QAChB,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,aAAa,EAAE;YAC1E,YAAY;YACZ,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,IAAI,OAAO;gBACT,iBAAiB;gBACjB,MAAM,2KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE;oBACvD,cAAc,SAAS,YAAY;oBACnC,OAAO,SAAS,KAAK;gBACvB;gBACA,cAAc;YAChB,OAAO;gBACL,iBAAiB;gBACjB,MAAM,2KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC;gBAC/C,cAAc;YAChB;YAEA;YACA;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;YACpD,YAAY,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0CAA0C;IAC1C,MAAM,4BAA4B;QAChC,IAAI,CAAC,oBAAoB,CAAC,MAAM,OAAO,CAAC,qBAAqB,iBAAiB,MAAM,KAAK,GAAG,OAAO,EAAE;QAErG,OAAO,iBAAiB,GAAG,CAAC,CAAA;YAC1B,IAAI,CAAC,KAAK,OAAO;YACjB,2CAA2C;YAC3C,IAAI,IAAI,SAAS,EAAE;gBACjB,OAAO;oBACL,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,IAAI;gBAC1B;YACF,OAEK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;gBAC3B,OAAO;oBACL,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;gBAChB;YACF,OAEK,IAAI,IAAI,WAAW,EAAE;gBACxB,+CAA+C;gBAC/C,MAAM,mBAAmB,MAAM,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC,CAAA,IAAK,GAAG,OAAO,IAAI,WAAW,IAAI;gBACvG,OAAO;oBACL,IAAI,IAAI,WAAW;oBACnB,MAAM,kBAAkB,QAAQ,CAAC,SAAS,EAAE,IAAI,WAAW,EAAE;gBAC/D;YACF;YACA,OAAO;QACT,GAAG,MAAM,CAAC;IACZ;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;0BAEV,QAAQ,cAAc;;;;;;;;;;;;IAK7B,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,QAAQ,8BAA8B;QAC7C,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAuB,UAAU;YAAc,WAAU;;8BAC9D,8OAAC;8BACC,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;gBAMrC,CAAC,0BACA,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;8BAER,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,QAAQ;wBACxB,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,CAAC;wBAChD,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,YAAY,QAAQ,GAAG,CAAC,CAAC,uBACtC,8OAAC;gCAAwB,OAAO,QAAQ;0CACrC,QAAQ,YAAY;+BADV,QAAQ;;;;;;;;;;;;;;;8BAS7B,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;oBACR,UAAU,SAAS,QAAQ,IAAI,4BAA4B,MAAM,KAAK,IAAI,mDAAmD;8BAE7H,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,WAAW;wBAC3B,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,SAAS,QAAQ,IAAI,CAAC,CAAC;wBACtE,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,gCAAgC,4BAA4B,GAAG,CAAC,CAAC,0BAC9E,8OAAC;gCAA2B,OAAO,WAAW;0CAC3C,WAAW,QAAQ;+BADT,WAAW;;;;;;;;;;;;;;;8BAQ9B,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;oBACtB,QAAQ;8BAER,cAAA,8OAAC,kLAAA,CAAA,eAAY;wBACX,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,aAAa;wBAC7B,UAAU;wBACV,UAAU,oBAAoB,gBAAgB,CAAC,CAAC;wBAChD,QAAQ;wBACR,aAAY;kCAEX,MAAM,OAAO,CAAC,iBAAiB,aAAa,GAAG,CAAC,CAAC,4BAChD,8OAAC;gCAA6B,OAAO,aAAa;0CAC/C,aAAa,QAAQ;+BADX,aAAa;;;;;;;;;;;;;;;8BAQhC,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,kNAAA,CAAA,aAAQ;wBAAC,MAAM;;;;;;oBACtB,UAAS;8BAET,cAAA,8OAAC,gLAAA,CAAA,cAAW;wBACV,aAAY;wBACZ,MAAK;wBACL,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,YAAY;wBAC5B,UAAU;wBACV,KAAI;wBACJ,UAAU;;;;;;;;;;;8BAKd,8OAAC,wLAAA,CAAA,kBAAe;oBACd,aAAY;oBACZ,OAAM;oBACN,SAAQ;oBACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;8BAEtB,cAAA,8OAAC,sLAAA,CAAA,iBAAc;wBACb,aAAY;wBACZ,IAAG;wBACH,MAAK;wBACL,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,MAAM;wBACN,UAAU;;;;;;;;;;;;;;;;;;;;;;AAOxB;uCAEe"}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ui/ExportMenu.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, Construction, Image } from \"lucide-react\";\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\n\r\nconst ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' }) => {\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n  const buttonRef = useRef(null);\r\n  const dropdownRef = useRef(null);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Fecha o dropdown ao clicar fora dele\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleExport = (format) => {\r\n    onExport(format);\r\n    setDropdownOpen(false);\r\n  };\r\n\r\n  // Se estiver em construção, mostrar o botão de construção\r\n  if (underConstruction) {\r\n    return (\r\n      <ConstructionButton\r\n        className=\"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors\"\r\n        title=\"Exportação em Construção\"\r\n        content=\"A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.\"\r\n        icon=\"FileText\"\r\n      >\r\n        <Download size={16} />\r\n        <span>Exportar</span>\r\n        <ChevronDown size={14} />\r\n      </ConstructionButton>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        ref={buttonRef}\r\n        onClick={() => setDropdownOpen(!dropdownOpen)}\r\n        className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`}\r\n        disabled={isExporting || (disabled && !underConstruction)}\r\n        title={disabled ? \"Não há dados para exportar\" : \"Exportar dados\"}\r\n      >\r\n        {isExporting ? (\r\n          <Loader2 size={16} className=\"animate-spin\" />\r\n        ) : (\r\n          <Download size={16} />\r\n        )}\r\n        <span>{isExporting ? \"Exportando...\" : \"Exportar\"}</span>\r\n        <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n      {dropdownOpen && mounted && createPortal(\r\n        <div\r\n          ref={dropdownRef}\r\n          className=\"fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n          style={{\r\n            top: `${dropdownPosition.top}px`,\r\n            right: `${dropdownPosition.right}px`,\r\n            width: `${dropdownPosition.width}px`,\r\n          }}\r\n        >\r\n          <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n            <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Formato de exportação</h4>\r\n          </div>\r\n          <div className=\"p-1\">\r\n            <button\r\n              onClick={() => handleExport('image')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <Image size={16} className=\"text-blue-500 dark:text-blue-400\" />\r\n              <span>Imagem (PNG)</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('pdf')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileText size={16} className=\"text-red-500 dark:text-red-400\" />\r\n              <span>PDF</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('xlsx')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileSpreadsheet size={16} className=\"text-green-500 dark:text-green-400\" />\r\n              <span>Excel (XLSX)</span>\r\n            </button>\r\n          </div>\r\n        </div>,\r\n        document.body\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportMenu;"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;;AAQA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,oBAAoB,KAAK,EAAE,YAAY,EAAE,EAAE;IAChH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IACtF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,gBAAgB;IAClB;IAEA,0DAA0D;IAC1D,IAAI,mBAAmB;QACrB,qBACE,8OAAC,wMAAA,CAAA,qBAAkB;YACjB,WAAU;YACV,OAAM;YACN,SAAQ;YACR,MAAK;;8BAEL,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;8BAChB,8OAAC;8BAAK;;;;;;8BACN,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAW,CAAC,+GAA+G,EAAE,UAAU,QAAQ,CAAC,gBAAgB,6CAA6C,4FAA4F,CAAC,EAAE,WAAW;gBACvT,UAAU,eAAgB,YAAY,CAAC;gBACvC,OAAO,WAAW,+BAA+B;;oBAEhD,4BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;kCAElB,8OAAC;kCAAM,cAAc,kBAAkB;;;;;;kCACvC,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;YAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oBACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBACtC;;kCAEA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAE1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;sBAIZ,SAAS,IAAI;;;;;;;AAIrB;uCAEe"}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport TutorialManager from \"@/components/tutorial/TutorialManager\";\r\nimport TutorialTriggerButton from \"@/components/tutorial/TutorialTriggerButton\";\r\nimport ModuleHeader, { FilterButton } from \"@/components/ui/ModuleHeader\";\r\nimport { ModuleSelect, ModuleInput, ModuleTable } from \"@/components/ui\";\r\nimport MultiSelect from \"@/components/ui/multi-select\";\r\nimport {\r\n  Plus,\r\n  Search,\r\n  Filter,\r\n  RefreshCw,\r\n  Edit,\r\n  Trash,\r\n  Shield,\r\n  CreditCard,\r\n  User,\r\n  Tag,\r\n  Clock\r\n} from \"lucide-react\";\r\nimport { insuranceServiceLimitService } from \"@/app/modules/people/services/insuranceServiceLimitService\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { serviceTypeService } from \"@/app/modules/scheduler/services/serviceTypeService\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport InsuranceLimitFormModal from \"@/components/people/InsuranceLimitFormModal\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\n\r\n// Tutorial steps para a página de limites de convênio\r\nconst insuranceLimitsTutorialSteps = [\r\n  {\r\n    title: \"Limites de Convênio\",\r\n    content: \"Esta tela permite configurar os limites de utilização de serviços por convênio para cada paciente.\",\r\n    selector: \"h1\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Adicionar Novo Limite\",\r\n    content: \"Clique aqui para adicionar um novo limite de convênio.\",\r\n    selector: \"button:has(span:contains('Novo Limite'))\",\r\n    position: \"left\"\r\n  },\r\n  {\r\n    title: \"Filtrar Limites\",\r\n    content: \"Use esta barra de pesquisa para encontrar limites específicos pelo nome do paciente ou convênio.\",\r\n    selector: \"input[placeholder*='Buscar']\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Pacientes\",\r\n    content: \"Selecione um ou mais pacientes para filtrar os limites.\",\r\n    selector: \"div:has(> div > label:contains('Filtrar por Pacientes'))\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Convênio\",\r\n    content: \"Filtre os limites por convênio específico.\",\r\n    selector: \"select:nth-of-type(1)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Tipo de Serviço\",\r\n    content: \"Filtre os limites por tipo de serviço específico.\",\r\n    selector: \"select:nth-of-type(2)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Exportar Dados\",\r\n    content: \"Exporte a lista de limites em diferentes formatos usando este botão.\",\r\n    selector: \".export-button\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Gerenciar Limites\",\r\n    content: \"Edite ou exclua limites existentes usando os botões de ação na tabela.\",\r\n    selector: \"table\",\r\n    position: \"top\"\r\n  }\r\n];\r\n\r\nconst InsuranceLimitsPage = () => {\r\n  const { user: currentUser } = useAuth();\r\n  const { addToast, toast_success, toast_error } = useToast();\r\n  const isAdmin = currentUser?.role === \"ADMIN\" || currentUser?.role === \"SYSTEM_ADMIN\";\r\n  const isSystemAdmin = currentUser?.role === \"SYSTEM_ADMIN\";\r\n  const [limits, setLimits] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [filters, setFilters] = useState({\r\n    insuranceId: \"\",\r\n    serviceTypeId: \"\",\r\n    companyId: \"\"\r\n  });\r\n  const [personsFilter, setPersonsFilter] = useState([]);\r\n  const [personOptions, setPersonOptions] = useState([]);\r\n  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);\r\n  const [persons, setPersons] = useState([]);\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [serviceTypes, setServiceTypes] = useState([]);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);\r\n  const [selectedLimit, setSelectedLimit] = useState(null);\r\n  const [limitFormOpen, setLimitFormOpen] = useState(false);\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [isLoadingFilters, setIsLoadingFilters] = useState(false);\r\n  // Estados para paginação\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [totalItems, setTotalItems] = useState(0);\r\n  // Estado para ordenação\r\n  const [sortField, setSortField] = useState(\"patient\");\r\n  const [sortDirection, setSortDirection] = useState(\"asc\");\r\n\r\n  // Função para carregar opções de pacientes para o multi-select\r\n  const loadPersonOptions = useCallback(async () => {\r\n    setIsLoadingPersonOptions(true);\r\n    try {\r\n      // Carregar todos os pacientes para o multi-select (com limite maior)\r\n      const response = await personsService.getPersons({\r\n        limit: 100, // Limite maior para ter mais opções\r\n        active: true // Apenas pacientes ativos por padrão\r\n      });\r\n\r\n      // Extrair os pacientes da resposta, garantindo que temos um array válido\r\n      const personsArray = response?.persons || response?.people || [];\r\n\r\n      const options = personsArray.map(person => ({\r\n        value: person.id,\r\n        label: person.fullName,\r\n        // Guardar o nome para ordenação\r\n        sortName: person.fullName ? person.fullName.toLowerCase() : ''\r\n      })) || [];\r\n\r\n      // Ordenar as opções alfabeticamente pelo nome\r\n      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName, 'pt-BR', { sensitivity: 'base' }));\r\n\r\n      console.log('Opções de pacientes ordenadas:', sortedOptions);\r\n      setPersonOptions(sortedOptions);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de pacientes:\", error);\r\n      setPersonOptions([]);\r\n    } finally {\r\n      setIsLoadingPersonOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  // Função para carregar empresas (apenas para system_admin)\r\n  const loadCompanies = async () => {\r\n    if (!isSystemAdmin) return;\r\n\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const response = await companyService.getCompaniesForSelect();\r\n      setCompanies(response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  // Carregar dados iniciais\r\n  useEffect(() => {\r\n    loadLimits();\r\n    loadFilterOptions();\r\n    loadPersonOptions();\r\n    // Carregar empresas se o usuário for system_admin\r\n    if (isSystemAdmin) {\r\n      loadCompanies();\r\n    }\r\n  }, [loadPersonOptions, isSystemAdmin]);\r\n\r\n  // Constante para itens por página\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Carregar limites de convênio\r\n  const loadLimits = async (\r\n    page = currentPage,\r\n    searchQuery = search,\r\n    personIds = personsFilter,\r\n    filterOptions = filters,\r\n    sort = sortField || 'patient',\r\n    direction = sortDirection || 'asc'\r\n  ) => {\r\n    setIsLoading(true);\r\n    try {\r\n      // Garantir que a direção seja uma string válida\r\n      const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())\r\n        ? direction.toLowerCase()\r\n        : 'asc';\r\n\r\n      // Garantir que a página é um número\r\n      const pageNumber = parseInt(page, 10);\r\n\r\n      // Atualizar o estado da página atual\r\n      setCurrentPage(pageNumber);\r\n\r\n      // Buscar TODOS os limites de convênio (sem paginação no backend)\r\n      const response = await insuranceServiceLimitService.getAllLimits({\r\n        search: searchQuery || undefined,\r\n        personIds: personIds.length > 0 ? personIds : undefined,\r\n        ...filterOptions,\r\n        sortField: sort || 'patient',\r\n        sortDirection: validDirection\r\n      });\r\n\r\n      console.log('Parâmetros enviados para o serviço:', {\r\n        search: searchQuery || undefined,\r\n        personIds: personIds.length > 0 ? personIds : undefined,\r\n        ...filterOptions,\r\n        sortField: sort || 'patient',\r\n        sortDirection: validDirection\r\n      });\r\n\r\n      // Verificar se temos os dados no formato esperado\r\n      if (response && typeof response === 'object') {\r\n        // Extrair todos os limites\r\n        let allLimits = response.limits || [];\r\n\r\n        // A ordenação agora é feita no backend, não precisamos ordenar manualmente aqui\r\n        // Apenas registramos a ordenação para debug\r\n        console.log(`Ordenação aplicada: campo=${sort || 'patient'}, direção=${validDirection}`);\r\n\r\n        // Calcular o total de itens e páginas\r\n        const total = allLimits.length;\r\n        const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;\r\n\r\n        // Aplicar paginação manual no lado do cliente\r\n        const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;\r\n        const endIndex = startIndex + ITEMS_PER_PAGE;\r\n        const paginatedLimits = allLimits.slice(startIndex, endIndex);\r\n\r\n        // Verificar se a página atual é válida\r\n        if (pageNumber > 1 && paginatedLimits.length === 0 && allLimits.length > 0) {\r\n          // Se a página atual não tem itens, mas existem itens em outras páginas,\r\n          // voltar para a primeira página\r\n          console.log(`Página ${pageNumber} está vazia, voltando para a página 1`);\r\n          setCurrentPage(1);\r\n          const firstPageLimits = allLimits.slice(0, ITEMS_PER_PAGE);\r\n          setLimits(firstPageLimits);\r\n        } else {\r\n          // Atualizar o estado com os dados paginados manualmente\r\n          setLimits(paginatedLimits); // Apenas os 10 itens da página atual\r\n        }\r\n\r\n        setTotalItems(total);\r\n        setTotalPages(pages);\r\n\r\n        console.log(`Paginação manual: Página ${pageNumber}, exibindo ${paginatedLimits.length} itens (${startIndex+1}-${Math.min(endIndex, total)}) de ${total} total`);\r\n      } else {\r\n        console.error('Dados de limites inválidos:', response);\r\n        setLimits([]);\r\n        setTotalItems(0);\r\n        setTotalPages(1);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar limites de convênio:\", error);\r\n      toast_error(\"Erro ao carregar limites de convênio\");\r\n      setLimits([]);\r\n      setTotalItems(0);\r\n      setTotalPages(1);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Carregar opções para os filtros\r\n  const loadFilterOptions = async () => {\r\n    setIsLoadingFilters(true);\r\n    try {\r\n      // Carregar pessoas\r\n      try {\r\n        const personsData = await personsService.getPersons({ limit: 100 });\r\n        console.log('Dados de pessoas recebidos:', personsData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (personsData && typeof personsData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de pessoas\r\n          const personsArray = Array.isArray(personsData) ? personsData :\r\n                              personsData.persons ? personsData.persons :\r\n                              personsData.people ? personsData.people :\r\n                              personsData.data ? personsData.data : [];\r\n\r\n          console.log('Array de pessoas extraído:', personsArray);\r\n          // Garantir que estamos definindo um array\r\n          setPersons(Array.isArray(personsArray) ? personsArray : []);\r\n        } else {\r\n          console.error('Dados de pessoas inválidos:', personsData);\r\n          setPersons([]);\r\n        }\r\n      } catch (personError) {\r\n        console.error('Erro ao carregar pessoas:', personError);\r\n        setPersons([]);\r\n      }\r\n\r\n      // Carregar convênios\r\n      try {\r\n        const insurancesData = await insurancesService.getInsurances();\r\n        console.log('Dados de convênios recebidos:', insurancesData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (insurancesData && typeof insurancesData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de convênios\r\n          const insurancesArray = Array.isArray(insurancesData) ? insurancesData :\r\n                                 insurancesData.insurances ? insurancesData.insurances :\r\n                                 insurancesData.data ? insurancesData.data : [];\r\n\r\n          console.log('Array de convênios extraído:', insurancesArray);\r\n          // Garantir que estamos definindo um array\r\n          setInsurances(Array.isArray(insurancesArray) ? insurancesArray : []);\r\n        } else {\r\n          console.error('Dados de convênios inválidos:', insurancesData);\r\n          setInsurances([]);\r\n        }\r\n      } catch (insuranceError) {\r\n        console.error('Erro ao carregar convênios:', insuranceError);\r\n        setInsurances([]);\r\n      }\r\n\r\n      // Carregar tipos de serviço\r\n      try {\r\n        const serviceTypesData = await serviceTypeService.getServiceTypes();\r\n        console.log('Dados de tipos de serviço recebidos:', serviceTypesData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (serviceTypesData && typeof serviceTypesData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de tipos de serviço\r\n          const serviceTypesArray = Array.isArray(serviceTypesData) ? serviceTypesData :\r\n                                   serviceTypesData.serviceTypes ? serviceTypesData.serviceTypes :\r\n                                   serviceTypesData.data ? serviceTypesData.data : [];\r\n\r\n          console.log('Array de tipos de serviço extraído:', serviceTypesArray);\r\n          // Garantir que estamos definindo um array\r\n          setServiceTypes(Array.isArray(serviceTypesArray) ? serviceTypesArray : []);\r\n        } else {\r\n          console.error('Dados de tipos de serviço inválidos:', serviceTypesData);\r\n          setServiceTypes([]);\r\n        }\r\n      } catch (serviceTypeError) {\r\n        console.error('Erro ao carregar tipos de serviço:', serviceTypeError);\r\n        setServiceTypes([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de filtro:\", error);\r\n      toast_error(\"Erro ao carregar opções de filtro\");\r\n    } finally {\r\n      setIsLoadingFilters(false);\r\n    }\r\n  };\r\n\r\n  // Manipuladores de eventos\r\n  const handleSearch = (e) => {\r\n    e.preventDefault();\r\n    loadLimits(1, search, personsFilter, filters, sortField, sortDirection);\r\n  };\r\n\r\n  const handleFilterChange = (field, value) => {\r\n    const newFilters = { ...filters, [field]: value };\r\n    setFilters(newFilters);\r\n    loadLimits(1, search, personsFilter, newFilters, sortField, sortDirection);\r\n  };\r\n\r\n  const handlePersonsFilterChange = (value) => {\r\n    setPersonsFilter(value);\r\n    loadLimits(1, search, value, filters, sortField, sortDirection);\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setPersonsFilter([]);\r\n    setFilters({\r\n      insuranceId: \"\",\r\n      serviceTypeId: \"\",\r\n      companyId: \"\"\r\n    });\r\n    loadLimits(1, \"\", [], {\r\n      insuranceId: \"\",\r\n      serviceTypeId: \"\",\r\n      companyId: \"\"\r\n    }, sortField, sortDirection);\r\n  };\r\n\r\n  // Função para lidar com a mudança de página\r\n  const handlePageChange = (page) => {\r\n    console.log(`Mudando para a página ${page}`);\r\n    // Chamamos loadLimits com a nova página\r\n    loadLimits(page, search, personsFilter, filters, sortField, sortDirection);\r\n  };\r\n\r\n  // Função para lidar com a mudança de ordenação\r\n  const handleSortChange = (field, direction) => {\r\n    console.log(`Alterando ordenação: campo=${field}, direção=${direction}`, {\r\n      tipoField: typeof field,\r\n      tipoDirection: typeof direction,\r\n      valorField: field,\r\n      valorDirection: direction\r\n    });\r\n\r\n    // Garantir que a direção seja uma string válida\r\n    const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())\r\n      ? direction.toLowerCase()\r\n      : 'asc';\r\n\r\n    console.log(`Direção normalizada: ${validDirection}`);\r\n\r\n    setSortField(field);\r\n    setSortDirection(validDirection);\r\n    loadLimits(currentPage, search, personsFilter, filters, field, validDirection);\r\n  };\r\n\r\n  const handleEditLimit = (limit) => {\r\n    setSelectedLimit(limit);\r\n    setLimitFormOpen(true);\r\n  };\r\n\r\n  const handleDeleteLimit = (limit) => {\r\n    setSelectedLimit(limit);\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const confirmDeleteLimit = async () => {\r\n    try {\r\n      await insuranceServiceLimitService.deleteLimit(selectedLimit.id);\r\n      toast_success(\"Limite de convênio excluído com sucesso\");\r\n      loadLimits();\r\n      setConfirmationDialogOpen(false);\r\n    } catch (error) {\r\n      console.error(\"Erro ao excluir limite de convênio:\", error);\r\n      toast_error(\"Erro ao excluir limite de convênio\");\r\n    }\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Encontrar os nomes dos filtros selecionados para o subtítulo da exportação\r\n      let insuranceName, serviceTypeName;\r\n\r\n      if (filters.insuranceId) {\r\n        const selectedInsurance = insurances.find(i => i.id === filters.insuranceId);\r\n        insuranceName = selectedInsurance ? selectedInsurance.name : undefined;\r\n      }\r\n\r\n      if (filters.serviceTypeId) {\r\n        const selectedServiceType = serviceTypes.find(s => s.id === filters.serviceTypeId);\r\n        serviceTypeName = selectedServiceType ? selectedServiceType.name : undefined;\r\n      }\r\n\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      const success = await insuranceServiceLimitService.exportInsuranceLimits({\r\n        search: search || undefined,\r\n        personIds: personsFilter.length > 0 ? personsFilter : undefined,\r\n        insuranceId: filters.insuranceId || undefined,\r\n        insuranceName,\r\n        serviceTypeId: filters.serviceTypeId || undefined,\r\n        serviceTypeName,\r\n        companyId: filters.companyId || undefined,\r\n        sortField,\r\n        sortDirection\r\n      }, format);\r\n\r\n      if (success) {\r\n        toast_success(`Dados exportados com sucesso no formato ${format.toUpperCase()}`);\r\n      } else {\r\n        toast_error(\"Erro ao exportar dados\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar dados:\", error);\r\n      toast_error(\"Erro ao exportar dados\");\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título e botões de exportar e adicionar */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <CreditCard size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Limites de Convênio\r\n        </h1>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Botão de exportar */}\r\n          <ExportMenu\r\n            onExport={handleExport}\r\n            isExporting={isExporting}\r\n            disabled={isLoading || limits.length === 0}\r\n            className=\"text-orange-700 dark:text-orange-300\"\r\n          />\r\n\r\n          {/* Botão de adicionar */}\r\n          <button\r\n            onClick={() => {\r\n              setSelectedLimit(null);\r\n              setLimitFormOpen(true);\r\n            }}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\"\r\n          >\r\n            <Plus size={18} />\r\n            <span className=\"font-medium\">Novo Limite</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cabeçalho e filtros da página */}\r\n      <ModuleHeader\r\n        title=\"Filtros\"\r\n        icon={<Filter size={22} className=\"text-module-people-icon dark:text-module-people-icon-dark\" />}\r\n        description=\"Configure os limites de utilização de serviços por convênio para cada paciente. Utilize os filtros abaixo para encontrar limites específicos.\"\r\n        tutorialSteps={insuranceLimitsTutorialSteps}\r\n        tutorialName=\"insurance-limits-overview\"\r\n        moduleColor=\"people\"\r\n        filters={\r\n          <form\r\n            onSubmit={handleSearch}\r\n            className=\"flex flex-col gap-4\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\" />\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  placeholder=\"Buscar por nome do paciente ou convênio...\"\r\n                  value={search}\r\n                  onChange={(e) => setSearch(e.target.value)}\r\n                  className=\"w-full pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-2\">\r\n                {/* Filtro de empresa (apenas para system_admin) */}\r\n                {isSystemAdmin && (\r\n                  <div className=\"w-full sm:w-48\">\r\n                    <ModuleSelect\r\n                      moduleColor=\"people\"\r\n                      value={filters.companyId}\r\n                      onChange={(e) => handleFilterChange(\"companyId\", e.target.value)}\r\n                      placeholder=\"Empresa\"\r\n                      disabled={isLoadingCompanies}\r\n                    >\r\n                      <option value=\"\">Todas as empresas</option>\r\n                      {companies.map((company) => (\r\n                        <option key={company.id} value={company.id}>\r\n                          {company.name}\r\n                        </option>\r\n                      ))}\r\n                    </ModuleSelect>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Filtro de Convênio */}\r\n                <div className=\"w-full sm:w-48\">\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    value={filters.insuranceId}\r\n                    onChange={(e) => handleFilterChange(\"insuranceId\", e.target.value)}\r\n                    placeholder=\"Convênios\"\r\n                    disabled={isLoadingFilters}\r\n                  >\r\n                    <option value=\"\">Todos os Convênios</option>\r\n                    {Array.isArray(insurances) && insurances.map((insurance) => (\r\n                      <option key={insurance?.id} value={insurance?.id}>\r\n                        {insurance?.name || 'Sem nome'}\r\n                      </option>\r\n                    ))}\r\n                  </ModuleSelect>\r\n                </div>\r\n\r\n                {/* Filtro de Tipo de Serviço */}\r\n                <div className=\"w-full sm:w-48\">\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    value={filters.serviceTypeId}\r\n                    onChange={(e) => handleFilterChange(\"serviceTypeId\", e.target.value)}\r\n                    placeholder=\"Tipos de Serviço\"\r\n                    disabled={isLoadingFilters}\r\n                  >\r\n                    <option value=\"\">Todos os Tipos de Serviço</option>\r\n                    {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (\r\n                      <option key={serviceType?.id} value={serviceType?.id}>\r\n                        {serviceType?.name || 'Sem nome'}\r\n                      </option>\r\n                    ))}\r\n                  </ModuleSelect>\r\n                </div>\r\n\r\n                <FilterButton type=\"submit\" moduleColor=\"people\" variant=\"primary\">\r\n                  <Filter size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Filtrar</span>\r\n                </FilterButton>\r\n\r\n                <FilterButton\r\n                  type=\"button\"\r\n                  onClick={handleResetFilters}\r\n                  moduleColor=\"people\"\r\n                  variant=\"secondary\"\r\n                >\r\n                  <RefreshCw size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Limpar</span>\r\n                </FilterButton>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Multi-select para filtrar por múltiplos pacientes */}\r\n            <div className=\"w-full\">\r\n              <MultiSelect\r\n                label=\"Filtrar por Pacientes\"\r\n                value={personsFilter}\r\n                onChange={handlePersonsFilterChange}\r\n                options={personOptions}\r\n                placeholder=\"Selecione um ou mais pacientes pelo nome...\"\r\n                loading={isLoadingPersonOptions}\r\n                moduleOverride=\"people\"\r\n              />\r\n            </div>\r\n          </form>\r\n      }\r\n      />\r\n\r\n      {/* Tabela de Limites de Convênio */}\r\n      <ModuleTable\r\n        moduleColor=\"people\"\r\n        columns={[\r\n          {\r\n            header: 'Paciente',\r\n            field: 'patient',\r\n            width: '25%',\r\n            // Função de acesso personalizada para ordenação\r\n            accessor: (limit) => limit?.Person?.fullName || limit?.person?.fullName || ''\r\n          },\r\n          {\r\n            header: 'Convênio',\r\n            field: 'insurance',\r\n            width: '20%',\r\n            accessor: (limit) => limit?.Insurance?.name || limit?.insurance?.name || ''\r\n          },\r\n          {\r\n            header: 'Tipo de Serviço',\r\n            field: 'serviceType',\r\n            width: '25%',\r\n            accessor: (limit) => limit?.ServiceType?.name || limit?.serviceType?.name || ''\r\n          },\r\n          { header: 'Limite Mensal', field: 'monthlyLimit', width: '15%' },\r\n          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }\r\n        ]}\r\n        data={limits} // Já contém apenas os 10 itens da página atual\r\n        isLoading={isLoading}\r\n        emptyMessage=\"Nenhum limite de convênio encontrado\"\r\n        emptyIcon={<Shield size={24} />}\r\n        tableId=\"people-insurance-limits-table\"\r\n        enableColumnToggle={true}\r\n        defaultSortField=\"patient\"\r\n        defaultSortDirection=\"asc\"\r\n        currentPage={currentPage}\r\n        totalPages={totalPages}\r\n        totalItems={totalItems}\r\n        onPageChange={handlePageChange}\r\n        onSort={handleSortChange}\r\n        sortField={sortField}\r\n        sortDirection={sortDirection}\r\n        showPagination={true}\r\n        itemsPerPage={ITEMS_PER_PAGE}\r\n        renderRow={(limit, index, moduleColors, visibleColumns) => (\r\n          <tr key={limit.id} className={moduleColors.hoverBg}>\r\n            {visibleColumns.includes('patient') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center\">\r\n                    <User size={20} />\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <div className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                      {limit?.Person?.fullName || limit?.person?.fullName || \"N/A\"}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('insurance') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <CreditCard className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300 truncate\">\r\n                    {limit?.Insurance?.name || limit?.insurance?.name || \"N/A\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('serviceType') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <Tag className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300 truncate\">\r\n                    {limit?.ServiceType?.name || limit?.serviceType?.name || \"N/A\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('monthlyLimit') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <Clock className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300\">\r\n                    {limit?.monthlyLimit > 0 ? `${limit.monthlyLimit}x por mês` : \"Ilimitado\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('actions') && (\r\n              <td className=\"px-4 py-4 text-right\">\r\n                <div className=\"flex items-center justify-end gap-2\">\r\n                  <button\r\n                    onClick={() => handleEditLimit(limit)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors\"\r\n                    title=\"Editar\"\r\n                  >\r\n                    <Edit size={16} />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleDeleteLimit(limit)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                    title=\"Excluir\"\r\n                  >\r\n                    <Trash size={16} />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            )}\r\n          </tr>\r\n        )}\r\n      />\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmationDialogOpen}\r\n        onClose={() => setConfirmationDialogOpen(false)}\r\n        onConfirm={confirmDeleteLimit}\r\n        title=\"Excluir Limite de Convênio\"\r\n        message={`Tem certeza que deseja excluir o limite para ${selectedLimit?.Person?.fullName || selectedLimit?.person?.fullName || 'este paciente'} com o convênio ${selectedLimit?.Insurance?.name || selectedLimit?.insurance?.name || 'selecionado'}?`}\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n\r\n      {/* Limit Form Modal */}\r\n      {limitFormOpen && (\r\n        <InsuranceLimitFormModal\r\n          isOpen={limitFormOpen}\r\n          onClose={() => setLimitFormOpen(false)}\r\n          limit={selectedLimit}\r\n          onSuccess={() => {\r\n            setLimitFormOpen(false);\r\n            loadLimits();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Gerenciador de tutorial */}\r\n      <TutorialManager />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InsuranceLimitsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA;AAAA;AAAA;AAAA;AAFA;AAAA;AAEA;AAFA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;;;;;;;;;;;AAgCA,sDAAsD;AACtD,MAAM,+BAA+B;IACnC;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,sBAAsB;IAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACxD,MAAM,UAAU,aAAa,SAAS,WAAW,aAAa,SAAS;IACvE,MAAM,gBAAgB,aAAa,SAAS;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,aAAa;QACb,eAAe;QACf,WAAW;IACb;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,yBAAyB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,wBAAwB;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,+DAA+D;IAC/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,0BAA0B;QAC1B,IAAI;YACF,qEAAqE;YACrE,MAAM,WAAW,MAAM,6JAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;gBAC/C,OAAO;gBACP,QAAQ,KAAK,qCAAqC;YACpD;YAEA,yEAAyE;YACzE,MAAM,eAAe,UAAU,WAAW,UAAU,UAAU,EAAE;YAEhE,MAAM,UAAU,aAAa,GAAG,CAAC,CAAA,SAAU,CAAC;oBAC1C,OAAO,OAAO,EAAE;oBAChB,OAAO,OAAO,QAAQ;oBACtB,gCAAgC;oBAChC,UAAU,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK;gBAC9D,CAAC,MAAM,EAAE;YAET,8CAA8C;YAC9C,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,SAAS;oBAAE,aAAa;gBAAO;YAEjH,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,iBAAiB,EAAE;QACrB,SAAU;YACR,0BAA0B;QAC5B;IACF,GAAG,EAAE;IAEL,2DAA2D;IAC3D,MAAM,gBAAgB;QACpB,IAAI,CAAC,eAAe;QAEpB,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,4JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC3D,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA;QACA,kDAAkD;QAClD,IAAI,eAAe;YACjB;QACF;IACF,GAAG;QAAC;QAAmB;KAAc;IAErC,kCAAkC;IAClC,MAAM,iBAAiB;IAEvB,+BAA+B;IAC/B,MAAM,aAAa,OACjB,OAAO,WAAW,EAClB,cAAc,MAAM,EACpB,YAAY,aAAa,EACzB,gBAAgB,OAAO,EACvB,OAAO,aAAa,SAAS,EAC7B,YAAY,iBAAiB,KAAK;QAElC,aAAa;QACb,IAAI;YACF,gDAAgD;YAChD,MAAM,iBAAiB,aAAa;gBAAC;gBAAO;aAAO,CAAC,QAAQ,CAAC,UAAU,WAAW,MAC9E,UAAU,WAAW,KACrB;YAEJ,oCAAoC;YACpC,MAAM,aAAa,SAAS,MAAM;YAElC,qCAAqC;YACrC,eAAe;YAEf,iEAAiE;YACjE,MAAM,WAAW,MAAM,2KAAA,CAAA,+BAA4B,CAAC,YAAY,CAAC;gBAC/D,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,GAAG,aAAa;gBAChB,WAAW,QAAQ;gBACnB,eAAe;YACjB;YAEA,QAAQ,GAAG,CAAC,uCAAuC;gBACjD,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,GAAG,aAAa;gBAChB,WAAW,QAAQ;gBACnB,eAAe;YACjB;YAEA,kDAAkD;YAClD,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,2BAA2B;gBAC3B,IAAI,YAAY,SAAS,MAAM,IAAI,EAAE;gBAErC,gFAAgF;gBAChF,4CAA4C;gBAC5C,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,UAAU,UAAU,EAAE,gBAAgB;gBAEvF,sCAAsC;gBACtC,MAAM,QAAQ,UAAU,MAAM;gBAC9B,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,mBAAmB;gBAEnD,8CAA8C;gBAC9C,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI;gBACtC,MAAM,WAAW,aAAa;gBAC9B,MAAM,kBAAkB,UAAU,KAAK,CAAC,YAAY;gBAEpD,uCAAuC;gBACvC,IAAI,aAAa,KAAK,gBAAgB,MAAM,KAAK,KAAK,UAAU,MAAM,GAAG,GAAG;oBAC1E,wEAAwE;oBACxE,gCAAgC;oBAChC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,qCAAqC,CAAC;oBACvE,eAAe;oBACf,MAAM,kBAAkB,UAAU,KAAK,CAAC,GAAG;oBAC3C,UAAU;gBACZ,OAAO;oBACL,wDAAwD;oBACxD,UAAU,kBAAkB,qCAAqC;gBACnE;gBAEA,cAAc;gBACd,cAAc;gBAEd,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,WAAW,EAAE,gBAAgB,MAAM,CAAC,QAAQ,EAAE,aAAW,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,UAAU,OAAO,KAAK,EAAE,MAAM,MAAM,CAAC;YACjK,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,UAAU,EAAE;gBACZ,cAAc;gBACd,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,YAAY;YACZ,UAAU,EAAE;YACZ,cAAc;YACd,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA,kCAAkC;IAClC,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,IAAI;YACF,mBAAmB;YACnB,IAAI;gBACF,MAAM,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAI;gBACjE,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,qCAAqC;gBACrC,IAAI,eAAe,OAAO,gBAAgB,UAAU;oBAClD,0EAA0E;oBAC1E,MAAM,eAAe,MAAM,OAAO,CAAC,eAAe,cAC9B,YAAY,OAAO,GAAG,YAAY,OAAO,GACzC,YAAY,MAAM,GAAG,YAAY,MAAM,GACvC,YAAY,IAAI,GAAG,YAAY,IAAI,GAAG,EAAE;oBAE5D,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,0CAA0C;oBAC1C,WAAW,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;gBAC5D,OAAO;oBACL,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,WAAW,EAAE;gBACf;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,WAAW,EAAE;YACf;YAEA,qBAAqB;YACrB,IAAI;gBACF,MAAM,iBAAiB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,aAAa;gBAC5D,QAAQ,GAAG,CAAC,iCAAiC;gBAE7C,qCAAqC;gBACrC,IAAI,kBAAkB,OAAO,mBAAmB,UAAU;oBACxD,4EAA4E;oBAC5E,MAAM,kBAAkB,MAAM,OAAO,CAAC,kBAAkB,iBACjC,eAAe,UAAU,GAAG,eAAe,UAAU,GACrD,eAAe,IAAI,GAAG,eAAe,IAAI,GAAG,EAAE;oBAErE,QAAQ,GAAG,CAAC,gCAAgC;oBAC5C,0CAA0C;oBAC1C,cAAc,MAAM,OAAO,CAAC,mBAAmB,kBAAkB,EAAE;gBACrE,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,cAAc,EAAE;gBAClB;YACF,EAAE,OAAO,gBAAgB;gBACvB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,cAAc,EAAE;YAClB;YAEA,4BAA4B;YAC5B,IAAI;gBACF,MAAM,mBAAmB,MAAM,oKAAA,CAAA,qBAAkB,CAAC,eAAe;gBACjE,QAAQ,GAAG,CAAC,wCAAwC;gBAEpD,qCAAqC;gBACrC,IAAI,oBAAoB,OAAO,qBAAqB,UAAU;oBAC5D,mFAAmF;oBACnF,MAAM,oBAAoB,MAAM,OAAO,CAAC,oBAAoB,mBACnC,iBAAiB,YAAY,GAAG,iBAAiB,YAAY,GAC7D,iBAAiB,IAAI,GAAG,iBAAiB,IAAI,GAAG,EAAE;oBAE3E,QAAQ,GAAG,CAAC,uCAAuC;oBACnD,0CAA0C;oBAC1C,gBAAgB,MAAM,OAAO,CAAC,qBAAqB,oBAAoB,EAAE;gBAC3E,OAAO;oBACL,QAAQ,KAAK,CAAC,wCAAwC;oBACtD,gBAAgB,EAAE;gBACpB;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,gBAAgB,EAAE;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,YAAY;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW,GAAG,QAAQ,eAAe,SAAS,WAAW;IAC3D;IAEA,MAAM,qBAAqB,CAAC,OAAO;QACjC,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,MAAM,EAAE;QAAM;QAChD,WAAW;QACX,WAAW,GAAG,QAAQ,eAAe,YAAY,WAAW;IAC9D;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,WAAW,GAAG,QAAQ,OAAO,SAAS,WAAW;IACnD;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,iBAAiB,EAAE;QACnB,WAAW;YACT,aAAa;YACb,eAAe;YACf,WAAW;QACb;QACA,WAAW,GAAG,IAAI,EAAE,EAAE;YACpB,aAAa;YACb,eAAe;YACf,WAAW;QACb,GAAG,WAAW;IAChB;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM;QAC3C,wCAAwC;QACxC,WAAW,MAAM,QAAQ,eAAe,SAAS,WAAW;IAC9D;IAEA,+CAA+C;IAC/C,MAAM,mBAAmB,CAAC,OAAO;QAC/B,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,MAAM,UAAU,EAAE,WAAW,EAAE;YACvE,WAAW,OAAO;YAClB,eAAe,OAAO;YACtB,YAAY;YACZ,gBAAgB;QAClB;QAEA,gDAAgD;QAChD,MAAM,iBAAiB,aAAa;YAAC;YAAO;SAAO,CAAC,QAAQ,CAAC,UAAU,WAAW,MAC9E,UAAU,WAAW,KACrB;QAEJ,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,gBAAgB;QAEpD,aAAa;QACb,iBAAiB;QACjB,WAAW,aAAa,QAAQ,eAAe,SAAS,OAAO;IACjE;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,2KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC,cAAc,EAAE;YAC/D,cAAc;YACd;YACA,0BAA0B;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,YAAY;QACd;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,6EAA6E;YAC7E,IAAI,eAAe;YAEnB,IAAI,QAAQ,WAAW,EAAE;gBACvB,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,WAAW;gBAC3E,gBAAgB,oBAAoB,kBAAkB,IAAI,GAAG;YAC/D;YAEA,IAAI,QAAQ,aAAa,EAAE;gBACzB,MAAM,sBAAsB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,aAAa;gBACjF,kBAAkB,sBAAsB,oBAAoB,IAAI,GAAG;YACrE;YAEA,oDAAoD;YACpD,MAAM,UAAU,MAAM,2KAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC;gBACvE,QAAQ,UAAU;gBAClB,WAAW,cAAc,MAAM,GAAG,IAAI,gBAAgB;gBACtD,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA,eAAe,QAAQ,aAAa,IAAI;gBACxC;gBACA,WAAW,QAAQ,SAAS,IAAI;gBAChC;gBACA;YACF,GAAG;YAEH,IAAI,SAAS;gBACX,cAAc,CAAC,wCAAwC,EAAE,OAAO,WAAW,IAAI;YACjF,OAAO;gBACL,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,YAAY;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA8C;;;;;;;kCAIhF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,qIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,aAAa,OAAO,MAAM,KAAK;gCACzC,WAAU;;;;;;0CAIZ,8OAAC;gCACC,SAAS;oCACP,iBAAiB;oCACjB,iBAAiB;gCACnB;gCACA,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,uIAAA,CAAA,UAAY;gBACX,OAAM;gBACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAClC,aAAY;gBACZ,eAAe;gBACf,cAAa;gBACb,aAAY;gBACZ,uBACE,8OAAC;oBACC,UAAU;oBACV,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gLAAA,CAAA,cAAW;4CACV,aAAY;4CACZ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;wCAEZ,+BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC/D,aAAY;gDACZ,UAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;4DAAwB,OAAO,QAAQ,EAAE;sEACvC,QAAQ,IAAI;2DADF,QAAQ,EAAE;;;;;;;;;;;;;;;;sDAS/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,WAAW;gDAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;gDACZ,UAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,MAAM,OAAO,CAAC,eAAe,WAAW,GAAG,CAAC,CAAC,0BAC5C,8OAAC;4DAA2B,OAAO,WAAW;sEAC3C,WAAW,QAAQ;2DADT,WAAW;;;;;;;;;;;;;;;;sDAQ9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,aAAa;gDAC5B,UAAU,CAAC,IAAM,mBAAmB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDACnE,aAAY;gDACZ,UAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,MAAM,OAAO,CAAC,iBAAiB,aAAa,GAAG,CAAC,CAAC,4BAChD,8OAAC;4DAA6B,OAAO,aAAa;sEAC/C,aAAa,QAAQ;2DADX,aAAa;;;;;;;;;;;;;;;;sDAOhC,8OAAC,uIAAA,CAAA,eAAY;4CAAC,MAAK;4CAAS,aAAY;4CAAS,SAAQ;;8DACvD,8OAAC,sMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,8OAAC,uIAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAS;4CACT,aAAY;4CACZ,SAAQ;;8DAER,8OAAC,gNAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC/B,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;gCACV,OAAM;gCACN,OAAO;gCACP,UAAU;gCACV,SAAS;gCACT,aAAY;gCACZ,SAAS;gCACT,gBAAe;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC,gLAAA,CAAA,cAAW;gBACV,aAAY;gBACZ,SAAS;oBACP;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,gDAAgD;wBAChD,UAAU,CAAC,QAAU,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;oBAC7E;oBACA;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,UAAU,CAAC,QAAU,OAAO,WAAW,QAAQ,OAAO,WAAW,QAAQ;oBAC3E;oBACA;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,UAAU,CAAC,QAAU,OAAO,aAAa,QAAQ,OAAO,aAAa,QAAQ;oBAC/E;oBACA;wBAAE,QAAQ;wBAAiB,OAAO;wBAAgB,OAAO;oBAAM;oBAC/D;wBAAE,QAAQ;wBAAS,OAAO;wBAAW,WAAW;wBAAc,OAAO;wBAAO,UAAU;oBAAM;iBAC7F;gBACD,MAAM;gBACN,WAAW;gBACX,cAAa;gBACb,yBAAW,8OAAC,sMAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;gBACzB,SAAQ;gBACR,oBAAoB;gBACpB,kBAAiB;gBACjB,sBAAqB;gBACrB,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,QAAQ;gBACR,WAAW;gBACX,eAAe;gBACf,gBAAgB;gBAChB,cAAc;gBACd,WAAW,CAAC,OAAO,OAAO,cAAc,+BACtC,8OAAC;wBAAkB,WAAW,aAAa,OAAO;;4BAC/C,eAAe,QAAQ,CAAC,4BACvB,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;4BAOhE,eAAe,QAAQ,CAAC,8BACvB,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDACb,OAAO,WAAW,QAAQ,OAAO,WAAW,QAAQ;;;;;;;;;;;;;;;;;4BAM5D,eAAe,QAAQ,CAAC,gCACvB,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDACb,OAAO,aAAa,QAAQ,OAAO,aAAa,QAAQ;;;;;;;;;;;;;;;;;4BAMhE,eAAe,QAAQ,CAAC,iCACvB,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDACb,OAAO,eAAe,IAAI,GAAG,MAAM,YAAY,CAAC,SAAS,CAAC,GAAG;;;;;;;;;;;;;;;;;4BAMrE,eAAe,QAAQ,CAAC,4BACvB,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uBAhEd,MAAM,EAAE;;;;;;;;;;0BA0ErB,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,OAAM;gBACN,SAAS,CAAC,6CAA6C,EAAE,eAAe,QAAQ,YAAY,eAAe,QAAQ,YAAY,gBAAgB,gBAAgB,EAAE,eAAe,WAAW,QAAQ,eAAe,WAAW,QAAQ,cAAc,CAAC,CAAC;gBACrP,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;YAIZ,+BACC,8OAAC,sJAAA,CAAA,UAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,OAAO;gBACP,WAAW;oBACT,iBAAiB;oBACjB;gBACF;;;;;;0BAKJ,8OAAC,gJAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;uCAEe"}}, {"offset": {"line": 2980, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/dashboard/people/insurance-limits/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport InsuranceLimitsPage from \"@/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage\";\r\n\r\nexport default function InsuranceLimitsRoute() {\r\n  return <InsuranceLimitsPage />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,8OAAC,6KAAA,CAAA,UAAmB;;;;;AAC7B"}}, {"offset": {"line": 3001, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}