// scripts/run-patients-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de pacientes...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-patients.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de pacientes executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de pacientes:', error);
  process.exit(1);
}
