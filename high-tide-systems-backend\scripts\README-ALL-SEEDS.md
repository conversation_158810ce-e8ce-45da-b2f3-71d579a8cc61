# Script para Execução de Todos os Seeds

Este script executa todos os arquivos de seed do sistema em uma ordem específica, garantindo que as dependências entre os dados sejam respeitadas.

## Características

- Executa todos os seeds em sequência
- Respeita a ordem correta de dependências entre os dados
- Exibe um resumo ao final com os seeds executados com sucesso e os que falharam
- Continua a execução mesmo se um seed falhar

## Como Executar

### Opção 1: Execução Local
```bash
node scripts/run-all-seeds.js
```

### Opção 2: Execução no Docker
```bash
node scripts/run-all-seeds-docker.js
```

## Ordem de Execução dos Seeds

Os seeds são executados na seguinte ordem:

1. **seed.js** - Seed base (empresas e usuário admin)
2. **seed-professions.js** - Seed de profissões
3. **seed-users.js** - Seed de usuários
4. **seed-insurances.js** - Seed de convênios
5. **seed-insurance-limits.js** - Seed de limites de convênio
6. **seed-service-types.js** - Seed de tipos de serviço
7. **seed-locations.js** - Seed de localizações
8. **seed-clients.js** - Seed de clientes
9. **seed-patients.js** - Seed de pacientes
10. **seed-working-hours.js** - Seed de horários de trabalho

## Executando dentro do Docker

### Opção A: Script automatizado (recomendado)
```bash
node scripts/run-all-seeds-docker.js
```

### Opção B: Comando manual
```bash
docker exec -it high-tide-systems-api node scripts/run-all-seeds.js
```

## Executando Seeds Individualmente

Se algum seed falhar, você pode executá-lo individualmente usando os scripts específicos:

```bash
node scripts/run-base-seed.js
node scripts/run-professions-seed.js
node scripts/run-users-seed.js
node scripts/run-insurance-seed.js
node scripts/run-insurance-limits-seed.js
node scripts/run-service-types-seed.js
node scripts/run-locations-seed.js
node scripts/run-clients-seed.js
node scripts/run-patients-seed.js
node scripts/run-working-hours-seed.js
```

Ou você pode executá-los diretamente:

```bash
node prisma/seed.js
node prisma/seed-professions.js
node prisma/seed-users.js
node prisma/seed-insurances.js
node prisma/seed-insurance-limits.js
node prisma/seed-service-types.js
node prisma/seed-locations.js
node prisma/seed-clients.js
node prisma/seed-patients.js
node prisma/seed-working-hours.js
```

## Notas Importantes

- Certifique-se de que o banco de dados está configurado corretamente antes de executar os seeds
- Alguns seeds podem demorar mais tempo para serem executados, dependendo da quantidade de dados
- Os seeds verificam duplicidade, então é seguro executá-los múltiplas vezes
- Se você estiver usando Docker, certifique-se de que o container está em execução antes de executar os seeds
