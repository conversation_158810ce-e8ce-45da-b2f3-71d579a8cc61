// scripts/check-seed-status.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkSeedStatus() {
  console.log('🔍 Verificando status dos dados no banco...\n');

  try {
    // Verificar empresas
    const companies = await prisma.company.count();
    const testCompany = await prisma.company.findUnique({
      where: { id: '00000000-0000-0000-0000-000000000001' }
    });
    
    console.log(`📊 RESUMO DOS DADOS:`);
    console.log(`═══════════════════════════════════════════════════════════════`);
    console.log(`🏢 Empresas: ${companies} ${testCompany ? '(inclui empresa de teste)' : ''}`);

    // Verificar usuários
    const users = await prisma.user.count();
    const adminUser = await prisma.user.findUnique({
      where: { id: '00000000-0000-0000-0000-000000000001' }
    });
    console.log(`👥 Usuários: ${users} ${adminUser ? '(inclui admin)' : ''}`);

    // Verificar profissões
    const professions = await prisma.profession.count();
    const professionGroups = await prisma.professionGroup.count();
    console.log(`💼 Profissões: ${professions} (${professionGroups} grupos)`);

    // Verificar convênios
    const insurances = await prisma.insurance.count();
    console.log(`🏥 Convênios: ${insurances}`);

    // Verificar limites de convênio
    const insuranceLimits = await prisma.personInsuranceServiceLimit.count();
    console.log(`📋 Limites de Convênio: ${insuranceLimits}`);

    // Verificar tipos de serviço
    const serviceTypes = await prisma.serviceType.count();
    console.log(`🔧 Tipos de Serviço: ${serviceTypes}`);

    // Verificar localizações
    const locations = await prisma.location.count();
    console.log(`📍 Localizações: ${locations}`);

    // Verificar clientes
    const clients = await prisma.client.count();
    console.log(`👤 Clientes: ${clients}`);

    // Verificar pacientes
    const patients = await prisma.person.count();
    console.log(`🏥 Pacientes: ${patients}`);

    // Verificar horários de trabalho
    const workingHours = await prisma.workingHours.count();
    const usersWithWorkingHours = await prisma.user.count({
      where: {
        WorkingHours: {
          some: {}
        }
      }
    });
    console.log(`⏰ Horários de Trabalho: ${workingHours} (${usersWithWorkingHours} usuários configurados)`);

    // Verificar unidades
    const branches = await prisma.branch.count();
    console.log(`🏪 Unidades: ${branches}`);

    console.log(`═══════════════════════════════════════════════════════════════`);

    // Verificar se dados básicos existem
    console.log(`\n✅ STATUS DOS SEEDS:`);
    console.log(`═══════════════════════════════════════════════════════════════`);
    
    const seedStatus = [
      { name: 'Base (Empresas + Admin)', check: companies > 0 && adminUser !== null },
      { name: 'Profissões', check: professions > 0 },
      { name: 'Usuários', check: users > 1 }, // Mais que só o admin
      { name: 'Convênios', check: insurances > 0 },
      { name: 'Limites de Convênio', check: insuranceLimits > 0 },
      { name: 'Tipos de Serviço', check: serviceTypes > 0 },
      { name: 'Localizações', check: locations > 0 },
      { name: 'Clientes', check: clients > 0 },
      { name: 'Pacientes', check: patients > 0 },
      { name: 'Horários de Trabalho', check: workingHours > 0 }
    ];

    seedStatus.forEach((seed, index) => {
      const status = seed.check ? '✅' : '❌';
      const number = (index + 1).toString().padStart(2, ' ');
      console.log(`${status} ${number}. ${seed.name}`);
    });

    const completedSeeds = seedStatus.filter(s => s.check).length;
    const totalSeeds = seedStatus.length;

    console.log(`═══════════════════════════════════════════════════════════════`);
    console.log(`📈 Progresso: ${completedSeeds}/${totalSeeds} seeds executados (${Math.round(completedSeeds/totalSeeds*100)}%)`);

    if (completedSeeds === totalSeeds) {
      console.log(`🎉 Todos os seeds foram executados com sucesso!`);
    } else {
      console.log(`⚠️  Execute os seeds faltando com: node seed-all.js`);
    }

    console.log(`═══════════════════════════════════════════════════════════════\n`);

  } catch (error) {
    console.error('❌ Erro ao verificar status dos seeds:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  checkSeedStatus();
}

module.exports = { checkSeedStatus };
