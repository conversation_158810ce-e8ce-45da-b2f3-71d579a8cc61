// scripts/run-insurance-limits-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de limites de convênio...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-insurance-limits.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de limites de convênio executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de limites de convênio:', error);
  process.exit(1);
}
