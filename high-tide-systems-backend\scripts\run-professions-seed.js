// scripts/run-professions-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de profissões...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-professions.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de profissões executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de profissões:', error);
  process.exit(1);
}
