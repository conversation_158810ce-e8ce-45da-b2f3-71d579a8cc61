# 🌱 High Tide Systems - Sistema de Seeds

Este documento descreve como usar o sistema de seeds para popular o banco de dados com dados de teste.

## 🚀 Comando Único (Recomendado)

Para facilitar o uso, criamos um comando único que executa todos os seeds na ordem correta:

```bash
# Execução local (padrão)
node seed-all.js

# Execução no Docker
node seed-all.js --docker

# Verificar status dos dados
node seed-all.js --status

# Mostrar todos os comandos
node seed-all.js --commands

# Ajuda
node seed-all.js --help
```

## 📋 Ordem de Execução dos Seeds

Os seeds são executados automaticamente na seguinte ordem para respeitar as dependências:

1. **Empresas e Usuário Admin** (`seed.js`)
   - Cria empresa de teste e usuário administrador
   - Cria 5 empresas de exemplo com unidades

2. **Profissões** (`seed-professions.js`)
   - Cria grupos de profissões por área
   - Adiciona profissões específicas para 3 empresas

3. **Usuários** (`seed-users.js`)
   - Cria usuários baseados nas profissões
   - Atribui módulos e permissões específicas

4. **Convênios** (`seed-insurances.js`)
   - Adiciona convênios de saúde reais
   - 4-10 convênios por empresa

5. **Limites de Convênio** (`seed-insurance-limits.js`)
   - Define limites de serviços por convênio
   - Associa pacientes aos convênios

6. **Tipos de Serviço** (`seed-service-types.js`)
   - Cria tipos de serviço por empresa
   - Valores realistas por serviço

7. **Localizações** (`seed-locations.js`)
   - Adiciona locais de atendimento
   - Distribui entre unidades da empresa

8. **Clientes** (`seed-clients.js`)
   - Cria clientes com dados realistas
   - Associa convênios aos clientes

9. **Pacientes** (`seed-patients.js`)
   - Cria pacientes relacionados aos clientes
   - Define relacionamentos familiares

10. **Horários de Trabalho** (`seed-working-hours.js`)
    - Configura horários para profissionais
    - Segunda a sexta, 8h-17h com almoço

## 🛠️ Scripts Individuais

Caso precise executar seeds específicos:

### Scripts Auxiliares
```bash
node scripts/run-base-seed.js
node scripts/run-professions-seed.js
node scripts/run-users-seed.js
node scripts/run-insurance-seed.js
node scripts/run-insurance-limits-seed.js
node scripts/run-service-types-seed.js
node scripts/run-locations-seed.js
node scripts/run-clients-seed.js
node scripts/run-patients-seed.js
node scripts/run-working-hours-seed.js
```

### Seeds Diretos
```bash
node prisma/seed.js
node prisma/seed-professions.js
node prisma/seed-users.js
node prisma/seed-insurances.js
node prisma/seed-insurance-limits.js
node prisma/seed-service-types.js
node prisma/seed-locations.js
node prisma/seed-clients.js
node prisma/seed-patients.js
node prisma/seed-working-hours.js
```

## 🐳 Execução no Docker

### Comando Único (Recomendado)
```bash
node seed-all.js --docker
```

### Scripts Específicos
```bash
node scripts/run-all-seeds-docker.js
```

### Comandos Manuais
```bash
docker exec -it high-tide-systems-api node prisma/seed.js
docker exec -it high-tide-systems-api node prisma/seed-professions.js
# ... outros seeds
```

## 📚 Documentação Detalhada

Para informações específicas sobre cada seed:

- `scripts/README-ALL-SEEDS.md` - Execução de todos os seeds
- `scripts/README-ALL-SEEDS-DOCKER.md` - Execução no Docker
- `scripts/README-BASE-SEED.md` - Seed base
- `scripts/README-PROFESSIONS.md` - Seed de profissões
- `scripts/README-USERS.md` - Seed de usuários
- `scripts/README-INSURANCES.md` - Seed de convênios
- `scripts/README-INSURANCE-LIMITS.md` - Seed de limites
- `scripts/README-SERVICE-TYPES.md` - Seed de tipos de serviço
- `scripts/README-LOCATIONS.md` - Seed de localizações
- `scripts/README-CLIENTS.md` - Seed de clientes
- `scripts/README-PATIENTS.md` - Seed de pacientes
- `scripts/README-WORKING-HOURS.md` - Seed de horários

## ⚠️ Notas Importantes

- **Ordem**: Os seeds devem ser executados na ordem correta devido às dependências
- **Duplicidade**: A maioria dos seeds verifica duplicidade e pula registros existentes
- **Senha Padrão**: Todos os usuários criados têm senha `123456`
- **Docker**: Certifique-se de que o container `high-tide-systems-api` está rodando
- **Banco**: O banco de dados deve estar configurado e as migrações aplicadas

## 🔧 Troubleshooting

### Erro de dependência
Se um seed falhar por dependência, execute os seeds anteriores primeiro.

### Container não encontrado
```bash
docker ps  # Verificar containers rodando
docker start high-tide-systems-api  # Iniciar se necessário
```

### Erro de conexão com banco
```bash
docker exec -it high-tide-systems-api npx prisma migrate deploy
```

### Limpar dados
Para recomeçar do zero, você pode resetar o banco de dados e executar as migrações novamente.
