{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/ui/ExportMenu.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, Construction, Image } from \"lucide-react\";\r\nimport { useConstructionMessage } from '@/hooks/useConstructionMessage';\r\nimport { ConstructionButton } from '@/components/construction';\r\n\r\nconst ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' }) => {\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n  const buttonRef = useRef(null);\r\n  const dropdownRef = useRef(null);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Fecha o dropdown ao clicar fora dele\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleExport = (format) => {\r\n    onExport(format);\r\n    setDropdownOpen(false);\r\n  };\r\n\r\n  // Se estiver em construção, mostrar o botão de construção\r\n  if (underConstruction) {\r\n    return (\r\n      <ConstructionButton\r\n        className=\"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors\"\r\n        title=\"Exportação em Construção\"\r\n        content=\"A funcionalidade de exportação está em desenvolvimento e estará disponível em breve.\"\r\n        icon=\"FileText\"\r\n      >\r\n        <Download size={16} />\r\n        <span>Exportar</span>\r\n        <ChevronDown size={14} />\r\n      </ConstructionButton>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <button\r\n        ref={buttonRef}\r\n        onClick={() => setDropdownOpen(!dropdownOpen)}\r\n        className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`}\r\n        disabled={isExporting || (disabled && !underConstruction)}\r\n        title={disabled ? \"Não há dados para exportar\" : \"Exportar dados\"}\r\n      >\r\n        {isExporting ? (\r\n          <Loader2 size={16} className=\"animate-spin\" />\r\n        ) : (\r\n          <Download size={16} />\r\n        )}\r\n        <span>{isExporting ? \"Exportando...\" : \"Exportar\"}</span>\r\n        <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n      </button>\r\n\r\n      {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n      {dropdownOpen && mounted && createPortal(\r\n        <div\r\n          ref={dropdownRef}\r\n          className=\"fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n          style={{\r\n            top: `${dropdownPosition.top}px`,\r\n            right: `${dropdownPosition.right}px`,\r\n            width: `${dropdownPosition.width}px`,\r\n          }}\r\n        >\r\n          <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n            <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Formato de exportação</h4>\r\n          </div>\r\n          <div className=\"p-1\">\r\n            <button\r\n              onClick={() => handleExport('image')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <Image size={16} className=\"text-blue-500 dark:text-blue-400\" />\r\n              <span>Imagem (PNG)</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('pdf')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileText size={16} className=\"text-red-500 dark:text-red-400\" />\r\n              <span>PDF</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleExport('xlsx')}\r\n              className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md\"\r\n            >\r\n              <FileSpreadsheet size={16} className=\"text-green-500 dark:text-green-400\" />\r\n              <span>Excel (XLSX)</span>\r\n            </button>\r\n          </div>\r\n        </div>,\r\n        document.body\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportMenu;"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;;AAQA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,cAAc,KAAK,EAAE,WAAW,KAAK,EAAE,oBAAoB,KAAK,EAAE,YAAY,EAAE,EAAE;IAChH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IACtF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,gBAAgB;IAClB;IAEA,0DAA0D;IAC1D,IAAI,mBAAmB;QACrB,qBACE,8OAAC,wMAAA,CAAA,qBAAkB;YACjB,WAAU;YACV,OAAM;YACN,SAAQ;YACR,MAAK;;8BAEL,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;8BAChB,8OAAC;8BAAK;;;;;;8BACN,8OAAC,oNAAA,CAAA,cAAW;oBAAC,MAAM;;;;;;;;;;;;IAGzB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,KAAK;gBACL,SAAS,IAAM,gBAAgB,CAAC;gBAChC,WAAW,CAAC,+GAA+G,EAAE,UAAU,QAAQ,CAAC,gBAAgB,6CAA6C,4FAA4F,CAAC,EAAE,WAAW;gBACvT,UAAU,eAAgB,YAAY,CAAC;gBACvC,OAAO,WAAW,+BAA+B;;oBAEhD,4BACC,8OAAC,iNAAA,CAAA,UAAO;wBAAC,MAAM;wBAAI,WAAU;;;;;6CAE7B,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,MAAM;;;;;;kCAElB,8OAAC;kCAAM,cAAc,kBAAkB;;;;;;kCACvC,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;YAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oBACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBACtC;;kCAEA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;;;;;;kCAE1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC3B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAU;;kDAEV,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDACrC,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;sBAIZ,SAAS,IAAI;;;;;;;AAIrB;uCAEe"}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/permissions/Protected.js"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { usePermissions } from '@/hooks/usePermissions';\r\n\r\n// Componente que renderiza condicionalmente baseado em permissões\r\nexport function Protected({ \r\n  permission, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { can, canAll, canAny } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (!permission) return true;\r\n    \r\n    if (Array.isArray(permission)) {\r\n      return requireAll ? canAll(permission) : canAny(permission);\r\n    }\r\n    \r\n    return can(permission);\r\n  }, [permission, requireAll, can, canAll, canAny]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza condicionalmente baseado em módulos\r\nexport function ProtectedModule({ \r\n  module, \r\n  requireAll = false, \r\n  fallback = null, \r\n  children \r\n}) {\r\n  const { hasModule, isAdmin } = usePermissions();\r\n  \r\n  const hasAccess = React.useMemo(() => {\r\n    if (isAdmin()) return true;\r\n    \r\n    if (!module) return true;\r\n    \r\n    if (Array.isArray(module)) {\r\n      return requireAll \r\n        ? module.every(m => hasModule(m))\r\n        : module.some(m => hasModule(m));\r\n    }\r\n    \r\n    return hasModule(module);\r\n  }, [module, requireAll, hasModule, isAdmin]);\r\n  \r\n  return hasAccess ? children : fallback;\r\n}\r\n\r\n// Componente que renderiza apenas para administradores\r\nexport function AdminOnly({ fallback = null, children }) {\r\n  const { isAdmin } = usePermissions();\r\n  \r\n  return isAdmin() ? children : fallback;\r\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAMO,SAAS,UAAU,EACxB,UAAU,EACV,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE7C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,MAAM,OAAO,CAAC,aAAa;YAC7B,OAAO,aAAa,OAAO,cAAc,OAAO;QAClD;QAEA,OAAO,IAAI;IACb,GAAG;QAAC;QAAY;QAAY;QAAK;QAAQ;KAAO;IAEhD,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,gBAAgB,EAC9B,MAAM,EACN,aAAa,KAAK,EAClB,WAAW,IAAI,EACf,QAAQ,EACT;IACC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE5C,MAAM,YAAY,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI,WAAW,OAAO;QAEtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,OAAO,aACH,OAAO,KAAK,CAAC,CAAA,IAAK,UAAU,MAC5B,OAAO,IAAI,CAAC,CAAA,IAAK,UAAU;QACjC;QAEA,OAAO,UAAU;IACnB,GAAG;QAAC;QAAQ;QAAY;QAAW;KAAQ;IAE3C,OAAO,YAAY,WAAW;AAChC;AAGO,SAAS,UAAU,EAAE,WAAW,IAAI,EAAE,QAAQ,EAAE;IACrD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,YAAY,WAAW;AAChC"}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/admin/services/professionsService.js"], "sourcesContent": ["// src/app/modules/admin/services/professionsService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\n/**\r\n * Serviço para gerenciar operações relacionadas a profissões\r\n */\r\nexport const professionsService = {\r\n  /**\r\n   * Obtém a lista de profissões com filtros\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {string} params.search - Termo de busca\r\n   * @param {string} params.groupId - Filtro por grupo\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @param {string} params.companyId - Filtro por empresa\r\n   * @param {Array} params.professionIds - Filtro por IDs específicos de profissões\r\n   * @returns {Promise<Array>} Lista de profissões\r\n   */\r\n  async getProfessions(params = {}) {\r\n    try {\r\n      // Extrair professionIds do objeto params para tratamento especial\r\n      const { professionIds, ...otherParams } = params;\r\n\r\n      // Criar objeto de parâmetros base\r\n      let queryParams = { ...otherParams };\r\n\r\n      // Adicionar professionIds como parâmetros separados com notação de array\r\n      if (professionIds && professionIds.length > 0) {\r\n        // Garantir que professionIds seja um array\r\n        const professionIdsArray = Array.isArray(professionIds) ? professionIds : [professionIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        professionIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          queryParams[`professionIds[${index}]`] = id;\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de profissões:\", professionIdsArray);\r\n      }\r\n\r\n      // Log para debug\r\n      console.log(\"Parâmetros de filtro para profissões:\", queryParams);\r\n\r\n      const response = await api.get(\"/professions\", { params: queryParams });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar profissões:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Obtém uma profissão pelo ID\r\n   * @param {string} professionId - ID da profissão\r\n   * @returns {Promise<Object>} Dados da profissão\r\n   */\r\n  async getProfessionById(professionId) {\r\n    try {\r\n      const response = await api.get(`/professions/${professionId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar profissão ${professionId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Cria uma nova profissão\r\n   * @param {Object} professionData - Dados da profissão\r\n   * @returns {Promise<Object>} Profissão criada\r\n   */\r\n  async createProfession(professionData) {\r\n    try {\r\n      const response = await api.post(\"/professions\", professionData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar profissão:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Atualiza uma profissão existente\r\n   * @param {string} professionId - ID da profissão\r\n   * @param {Object} professionData - Dados atualizados da profissão\r\n   * @returns {Promise<Object>} Profissão atualizada\r\n   */\r\n  async updateProfession(professionId, professionData) {\r\n    try {\r\n      const response = await api.put(`/professions/${professionId}`, professionData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar profissão ${professionId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exclui uma profissão\r\n   * @param {string} professionId - ID da profissão\r\n   * @returns {Promise<void>}\r\n   */\r\n  async deleteProfession(professionId) {\r\n    try {\r\n      await api.delete(`/professions/${professionId}`);\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir profissão ${professionId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Obtém a lista de grupos de profissões\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {string} params.search - Termo de busca\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @param {string} params.companyId - Filtro por empresa\r\n   * @param {Array} params.groupIds - Filtro por IDs específicos de grupos\r\n   * @returns {Promise<Array>} Lista de grupos de profissões\r\n   */\r\n  async getProfessionGroups(params = {}) {\r\n    try {\r\n      // Extrair groupIds do objeto params para tratamento especial\r\n      const { groupIds, ...otherParams } = params;\r\n\r\n      // Criar objeto de parâmetros base\r\n      let queryParams = { ...otherParams };\r\n\r\n      // Adicionar groupIds como parâmetros separados com notação de array\r\n      if (groupIds && groupIds.length > 0) {\r\n        // Garantir que groupIds seja um array\r\n        const groupIdsArray = Array.isArray(groupIds) ? groupIds : [groupIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        groupIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          queryParams[`groupIds[${index}]`] = id;\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de grupos:\", groupIdsArray);\r\n      }\r\n\r\n      // Log para debug\r\n      console.log(\"Parâmetros de filtro para grupos:\", queryParams);\r\n\r\n      const response = await api.get(\"/professions/groups\", { params: queryParams });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar grupos de profissões:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Obtém um grupo de profissões pelo ID\r\n   * @param {string} groupId - ID do grupo\r\n   * @returns {Promise<Object>} Dados do grupo\r\n   */\r\n  async getProfessionGroupById(groupId) {\r\n    try {\r\n      const response = await api.get(`/professions/groups/${groupId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar grupo de profissões ${groupId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Cria um novo grupo de profissões\r\n   * @param {Object} groupData - Dados do grupo\r\n   * @returns {Promise<Object>} Grupo criado\r\n   */\r\n  async createProfessionGroup(groupData) {\r\n    try {\r\n      const response = await api.post(\"/professions/groups\", groupData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar grupo de profissões:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Atualiza um grupo de profissões existente\r\n   * @param {string} groupId - ID do grupo\r\n   * @param {Object} groupData - Dados atualizados do grupo\r\n   * @returns {Promise<Object>} Grupo atualizado\r\n   */\r\n  async updateProfessionGroup(groupId, groupData) {\r\n    try {\r\n      const response = await api.put(`/professions/groups/${groupId}`, groupData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar grupo de profissões ${groupId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exclui um grupo de profissões\r\n   * @param {string} groupId - ID do grupo\r\n   * @returns {Promise<void>}\r\n   */\r\n  async deleteProfessionGroup(groupId) {\r\n    try {\r\n      await api.delete(`/professions/groups/${groupId}`);\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir grupo de profissões ${groupId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Obtém a lista de usuários de uma profissão específica\r\n   * @param {string} professionId - ID da profissão\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @returns {Promise<Object>} Dados da profissão e lista de usuários\r\n   */\r\n  async getProfessionUsers(professionId, params = {}) {\r\n    try {\r\n      const response = await api.get(`/professions/${professionId}/users`, { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar usuários da profissão ${professionId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de profissões com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  async exportProfessions(filters, exportFormat = \"xlsx\") {\r\n    try {\r\n      // Obter os dados filtrados\r\n      const data = await professionsService.getProfessions({\r\n        ...filters,\r\n      });\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome\" },\r\n        { key: \"description\", header: \"Descrição\" },\r\n        { key: \"groupName\", header: \"Grupo\" },\r\n        { key: \"companyName\", header: \"Empresa\" },\r\n        {\r\n          key: \"active\",\r\n          header: \"Status\",\r\n          format: (value) => value ? \"Ativo\" : \"Inativo\",\r\n          align: \"center\",\r\n          width: 20\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(profession => {\r\n        return {\r\n          name: profession.name || \"\",\r\n          description: profession.description || \"\",\r\n          groupName: profession.group?.name || \"\",\r\n          companyName: profession.company?.name || \"\",\r\n          active: profession.active,\r\n          createdAt: profession.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.professionIds && filters.professionIds.length > 0) {\r\n        subtitleParts.push(`Profissões específicas: ${filters.professionIds.length} selecionadas`);\r\n      }\r\n      if (filters.groupId) {\r\n        subtitleParts.push(`Grupo: ${filters.groupName || filters.groupId}`);\r\n      }\r\n      if (filters.active !== undefined) {\r\n        subtitleParts.push(`Status: ${filters.active ? \"Ativas\" : \"Inativas\"}`);\r\n      }\r\n      if (filters.companyId) {\r\n        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"profissoes\",\r\n        columns,\r\n        title: \"Lista de Profissões\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar profissões:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de grupos de profissões com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  async exportProfessionGroups(filters, exportFormat = \"xlsx\") {\r\n    try {\r\n      // Obter os dados filtrados\r\n      const data = await professionsService.getProfessionGroups({\r\n        ...filters,\r\n      });\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome\" },\r\n        { key: \"description\", header: \"Descrição\" },\r\n        { key: \"companyName\", header: \"Empresa\" },\r\n        { key: \"professionsCount\", header: \"Nº de Profissões\" },\r\n        {\r\n          key: \"active\",\r\n          header: \"Status\",\r\n          format: (value) => value ? \"Ativo\" : \"Inativo\",\r\n          align: \"center\",\r\n          width: 20\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(group => {\r\n        return {\r\n          name: group.name || \"\",\r\n          description: group.description || \"\",\r\n          companyName: group.company?.name || \"\",\r\n          professionsCount: group.professions?.length || 0,\r\n          active: group.active,\r\n          createdAt: group.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.groupIds && filters.groupIds.length > 0) {\r\n        subtitleParts.push(`Grupos específicos: ${filters.groupIds.length} selecionados`);\r\n      }\r\n      if (filters.active !== undefined) {\r\n        subtitleParts.push(`Status: ${filters.active ? \"Ativos\" : \"Inativos\"}`);\r\n      }\r\n      if (filters.companyId) {\r\n        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"grupos-profissoes\",\r\n        columns,\r\n        title: \"Lista de Grupos de Profissões\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar grupos de profissões:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;AACvD;AACA;AACA;AACA;;;;;AAKO,MAAM,qBAAqB;IAChC;;;;;;;;;GASC,GACD,MAAM,gBAAe,SAAS,CAAC,CAAC;QAC9B,IAAI;YACF,kEAAkE;YAClE,MAAM,EAAE,aAAa,EAAE,GAAG,aAAa,GAAG;YAE1C,kCAAkC;YAClC,IAAI,cAAc;gBAAE,GAAG,WAAW;YAAC;YAEnC,yEAAyE;YACzE,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;gBAC7C,2CAA2C;gBAC3C,MAAM,qBAAqB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB;oBAAC;iBAAc;gBAEzF,+CAA+C;gBAC/C,mBAAmB,OAAO,CAAC,CAAC,IAAI;oBAC9B,yDAAyD;oBACzD,WAAW,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;gBAC3C;gBAEA,QAAQ,GAAG,CAAC,8CAA8C;YAC5D;YAEA,iBAAiB;YACjB,QAAQ,GAAG,CAAC,yCAAyC;YAErD,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,gBAAgB;gBAAE,QAAQ;YAAY;YACrE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,mBAAkB,YAAY;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;YAC7D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,kBAAiB,cAAc;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,gBAAgB;YAChD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,kBAAiB,YAAY,EAAE,cAAc;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,aAAa,CAAC,CAAC,EAAE;YAC9D,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,kBAAiB,YAAY;QACjC,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAC,EAAE;YAC5D,MAAM;QACR;IACF;IAEA;;;;;;;;GAQC,GACD,MAAM,qBAAoB,SAAS,CAAC,CAAC;QACnC,IAAI;YACF,6DAA6D;YAC7D,MAAM,EAAE,QAAQ,EAAE,GAAG,aAAa,GAAG;YAErC,kCAAkC;YAClC,IAAI,cAAc;gBAAE,GAAG,WAAW;YAAC;YAEnC,oEAAoE;YACpE,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;gBACnC,sCAAsC;gBACtC,MAAM,gBAAgB,MAAM,OAAO,CAAC,YAAY,WAAW;oBAAC;iBAAS;gBAErE,+CAA+C;gBAC/C,cAAc,OAAO,CAAC,CAAC,IAAI;oBACzB,yDAAyD;oBACzD,WAAW,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;gBACtC;gBAEA,QAAQ,GAAG,CAAC,0CAA0C;YACxD;YAEA,iBAAiB;YACjB,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,uBAAuB;gBAAE,QAAQ;YAAY;YAC5E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,wBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS;YAC/D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,CAAC,EAAE;YAChE,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,uBAAsB,SAAS;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,uBAAuB;YACvD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,uBAAsB,OAAO,EAAE,SAAS;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,EAAE;YACjE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,QAAQ,CAAC,CAAC,EAAE;YACnE,MAAM;QACR;IACF;IAEA;;;;GAIC,GACD,MAAM,uBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,QAAQ,CAAC,CAAC,EAAE;YACjE,MAAM;QACR;IACF;IAEA;;;;;;GAMC,GACD,MAAM,oBAAmB,YAAY,EAAE,SAAS,CAAC,CAAC;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,MAAM,CAAC,EAAE;gBAAE;YAAO;YAC9E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,aAAa,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,MAAM,mBAAkB,OAAO,EAAE,eAAe,MAAM;QACpD,IAAI;YACF,2BAA2B;YAC3B,MAAM,OAAO,MAAM,mBAAmB,cAAc,CAAC;gBACnD,GAAG,OAAO;YACZ;YAEA,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAO;gBAC9B;oBAAE,KAAK;oBAAe,QAAQ;gBAAY;gBAC1C;oBAAE,KAAK;oBAAa,QAAQ;gBAAQ;gBACpC;oBAAE,KAAK;oBAAe,QAAQ;gBAAU;gBACxC;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,UAAU;oBACrC,OAAO;oBACP,OAAO;gBACT;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,OAAO;oBACL,MAAM,WAAW,IAAI,IAAI;oBACzB,aAAa,WAAW,WAAW,IAAI;oBACvC,WAAW,WAAW,KAAK,EAAE,QAAQ;oBACrC,aAAa,WAAW,OAAO,EAAE,QAAQ;oBACzC,QAAQ,WAAW,MAAM;oBACzB,WAAW,WAAW,SAAS,IAAI;gBACrC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC7D,cAAc,IAAI,CAAC,CAAC,wBAAwB,EAAE,QAAQ,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC;YAC3F;YACA,IAAI,QAAQ,OAAO,EAAE;gBACnB,cAAc,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,SAAS,IAAI,QAAQ,OAAO,EAAE;YACrE;YACA,IAAI,QAAQ,MAAM,KAAK,WAAW;gBAChC,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,WAAW,YAAY;YACxE;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE;YAC3E;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;;;;GAKC,GACD,MAAM,wBAAuB,OAAO,EAAE,eAAe,MAAM;QACzD,IAAI;YACF,2BAA2B;YAC3B,MAAM,OAAO,MAAM,mBAAmB,mBAAmB,CAAC;gBACxD,GAAG,OAAO;YACZ;YAEA,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAO;gBAC9B;oBAAE,KAAK;oBAAe,QAAQ;gBAAY;gBAC1C;oBAAE,KAAK;oBAAe,QAAQ;gBAAU;gBACxC;oBAAE,KAAK;oBAAoB,QAAQ;gBAAmB;gBACtD;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,UAAU;oBACrC,OAAO;oBACP,OAAO;gBACT;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,OAAO;oBACL,MAAM,MAAM,IAAI,IAAI;oBACpB,aAAa,MAAM,WAAW,IAAI;oBAClC,aAAa,MAAM,OAAO,EAAE,QAAQ;oBACpC,kBAAkB,MAAM,WAAW,EAAE,UAAU;oBAC/C,QAAQ,MAAM,MAAM;oBACpB,WAAW,MAAM,SAAS,IAAI;gBAChC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACnD,cAAc,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC;YAClF;YACA,IAAI,QAAQ,MAAM,KAAK,WAAW;gBAChC,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,WAAW,YAAY;YACxE;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE;YAC3E;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;AACF"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/admin/services/companyService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\n\r\nexport const companyService = {\r\n  // Obter a empresa atual do usuário autenticado\r\n  getCurrentCompany: async () => {\r\n    try {\r\n      const response = await api.get('/companies/current');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresa atual:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar empresas com suporte a paginação e filtros\r\n  getCompanies: async ({ page = 1, limit = 10, search, active } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n      if (search) params.append('search', search);\r\n      if (active !== undefined) params.append('active', active);\r\n\r\n      const response = await api.get(`/companies?${params.toString()}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter uma empresa específica\r\n  getCompany: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter lista de empresas para formulários de seleção\r\n  getCompaniesForSelect: async () => {\r\n    try {\r\n      const response = await api.get(\"/companies/select\");\r\n      return response.data.companies || [];\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas para seleção:\", error);\r\n      // Retorna array vazio em caso de erro para facilitar o manuseio no frontend\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Criar uma nova empresa\r\n  createCompany: async (formData) => {\r\n    try {\r\n      const response = await api.post('/companies', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar empresa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar uma empresa existente\r\n  updateCompany: async (id, formData) => {\r\n    try {\r\n      const response = await api.put(`/companies/${id}`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Alternar o status de uma empresa (ativo/inativo)\r\n  toggleCompanyStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/companies/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao alterar status da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir uma empresa\r\n  deleteCompany: async (id) => {\r\n    try {\r\n      await api.delete(`/companies/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter o logo de uma empresa\r\n  getCompanyLogo: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}/logo`, {\r\n        responseType: 'blob'\r\n      });\r\n      return URL.createObjectURL(response.data);\r\n    } catch (error) {\r\n      console.error(`Erro ao obter logo da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default companyService;"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,iBAAiB;IAC5B,+CAA+C;IAC/C,mBAAmB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,cAAc,OAAO,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAClC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,WAAW,OAAO,MAAM,CAAC,UAAU;YAElD,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,sDAAsD;IACtD,uBAAuB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,4EAA4E;YAC5E,OAAO,EAAE;QACX;IACF;IAEA,yBAAyB;IACzB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,cAAc,UAAU;gBACtD,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,eAAe,OAAO,IAAI;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU;gBAC3D,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,EAAE;YAClD,MAAM;QACR;IACF;IAEA,mDAAmD;IACnD,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC;YAC1D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,EAAE;gBACtD,cAAc;YAChB;YACA,OAAO,IAAI,eAAe,CAAC,SAAS,IAAI;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/admin/ProfessionFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { X, Loader2, Building } from \"lucide-react\";\r\nimport { professionsService } from \"@/app/modules/admin/services/professionsService\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\nconst ProfessionFormModal = ({ isOpen, onClose, profession = null, groups = [], onSuccess }) => {\r\n  const { toast_success, toast_error } = useToast();\r\n  const { user } = useAuth();\r\n  const isSystemAdmin = user?.role === \"SYSTEM_ADMIN\";\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    description: \"\",\r\n    groupId: \"\",\r\n    companyId: \"\",\r\n    active: true\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n\r\n  // Carregar empresas quando o modal é aberto (apenas para system admin)\r\n  useEffect(() => {\r\n    if (isOpen && isSystemAdmin) {\r\n      loadCompanies();\r\n    }\r\n  }, [isOpen, isSystemAdmin]);\r\n\r\n  // Carregar dados da profissão quando selecionada\r\n  useEffect(() => {\r\n    if (profession) {\r\n      setFormData({\r\n        name: profession.name || \"\",\r\n        description: profession.description || \"\",\r\n        groupId: profession.groupId || \"\",\r\n        companyId: profession.companyId || \"\",\r\n        active: profession.active !== undefined ? profession.active : true\r\n      });\r\n    } else {\r\n      setFormData({\r\n        name: \"\",\r\n        description: \"\",\r\n        groupId: \"\",\r\n        companyId: user?.companyId || \"\",\r\n        active: true\r\n      });\r\n    }\r\n  }, [profession, user]);\r\n\r\n  // Função para carregar empresas\r\n  const loadCompanies = async () => {\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const companies = await companyService.getCompaniesForSelect();\r\n      setCompanies(companies || []);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: type === \"checkbox\" ? checked : value\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Validar dados\r\n      if (!formData.name.trim()) {\r\n        toast_error(\"O nome da profissão é obrigatório\");\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Preparar dados para envio\r\n      const dataToSend = {\r\n        name: formData.name.trim(),\r\n        description: formData.description.trim() || undefined,\r\n        groupId: formData.groupId || null,\r\n        active: formData.active\r\n      };\r\n\r\n      // Adicionar companyId apenas se for system admin\r\n      if (isSystemAdmin) {\r\n        dataToSend.companyId = formData.companyId || null;\r\n      }\r\n\r\n      if (profession) {\r\n        // Modo de edição\r\n        await professionsService.updateProfession(profession.id, dataToSend);\r\n        toast_success(\"Profissão atualizada com sucesso\");\r\n      } else {\r\n        // Modo de criação\r\n        await professionsService.createProfession(dataToSend);\r\n        toast_success(\"Profissão criada com sucesso\");\r\n      }\r\n\r\n      if (onSuccess) onSuccess();\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar profissão:\", error);\r\n      toast_error(error.response?.data?.message || \"Erro ao salvar profissão\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n      <div className=\"fixed inset-0 bg-black/50\" onClick={onClose}></div>\r\n      <div className=\"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center p-4 border-b border-neutral-200 dark:border-gray-700\">\r\n          <h2 className=\"text-lg font-medium text-neutral-800 dark:text-neutral-100\">\r\n            {profession ? \"Editar Profissão\" : \"Nova Profissão\"}\r\n          </h2>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors\"\r\n          >\r\n            <X size={20} className=\"text-neutral-500 dark:text-neutral-400\" />\r\n          </button>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"p-4 space-y-4\">\r\n          {/* Nome */}\r\n          <div>\r\n            <label\r\n              htmlFor=\"name\"\r\n              className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\"\r\n            >\r\n              Nome <span className=\"text-red-500\">*</span>\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              value={formData.name}\r\n              onChange={handleChange}\r\n              className=\"w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          {/* Descrição */}\r\n          <div>\r\n            <label\r\n              htmlFor=\"description\"\r\n              className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\"\r\n            >\r\n              Descrição\r\n            </label>\r\n            <textarea\r\n              id=\"description\"\r\n              name=\"description\"\r\n              value={formData.description}\r\n              onChange={handleChange}\r\n              rows=\"3\"\r\n              className=\"w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100 resize-none\"\r\n            ></textarea>\r\n          </div>\r\n\r\n          {/* Grupo */}\r\n          <div>\r\n            <label\r\n              htmlFor=\"groupId\"\r\n              className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\"\r\n            >\r\n              Grupo\r\n            </label>\r\n            <select\r\n              id=\"groupId\"\r\n              name=\"groupId\"\r\n              value={formData.groupId}\r\n              onChange={handleChange}\r\n              className=\"w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n            >\r\n              <option value=\"\">Sem grupo</option>\r\n              {groups.map((group) => (\r\n                <option key={group.id} value={group.id}>\r\n                  {group.name}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n\r\n          {/* Empresa (apenas para system admin) */}\r\n          {isSystemAdmin && (\r\n            <div>\r\n              <label\r\n                htmlFor=\"companyId\"\r\n                className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1 flex items-center gap-1\"\r\n              >\r\n                <Building size={16} className=\"text-neutral-500\" />\r\n                Empresa\r\n              </label>\r\n              <select\r\n                id=\"companyId\"\r\n                name=\"companyId\"\r\n                value={formData.companyId}\r\n                onChange={handleChange}\r\n                className=\"w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n                disabled={isLoadingCompanies}\r\n              >\r\n                <option value=\"\">Selecione uma empresa</option>\r\n                {companies.map((company) => (\r\n                  <option key={company.id} value={company.id}>\r\n                    {company.name}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n              {isLoadingCompanies && (\r\n                <div className=\"mt-1 text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1\">\r\n                  <Loader2 size={12} className=\"animate-spin\" />\r\n                  <span>Carregando empresas...</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {/* Status (apenas em modo de edição) */}\r\n          {profession && (\r\n            <div className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                id=\"active\"\r\n                name=\"active\"\r\n                checked={formData.active}\r\n                onChange={handleChange}\r\n                className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\r\n              />\r\n              <label\r\n                htmlFor=\"active\"\r\n                className=\"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\"\r\n              >\r\n                Profissão ativa\r\n              </label>\r\n            </div>\r\n          )}\r\n\r\n          {/* Botões */}\r\n          <div className=\"flex justify-end gap-2 pt-2\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              disabled={isSubmitting}\r\n              className=\"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-60\"\r\n            >\r\n              Cancelar\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-60 flex items-center gap-2\"\r\n            >\r\n              {isSubmitting ? (\r\n                <>\r\n                  <Loader2 size={16} className=\"animate-spin\" />\r\n                  <span>Salvando...</span>\r\n                </>\r\n              ) : (\r\n                <span>Salvar</span>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfessionFormModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AAJA;AAAA;AAAA;;;;;;;;AAMA,MAAM,sBAAsB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE;IACzF,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,gBAAgB,MAAM,SAAS;IAErC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,uEAAuE;IACvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe;YAC3B;QACF;IACF,GAAG;QAAC;QAAQ;KAAc;IAE1B,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,YAAY;gBACV,MAAM,WAAW,IAAI,IAAI;gBACzB,aAAa,WAAW,WAAW,IAAI;gBACvC,SAAS,WAAW,OAAO,IAAI;gBAC/B,WAAW,WAAW,SAAS,IAAI;gBACnC,QAAQ,WAAW,MAAM,KAAK,YAAY,WAAW,MAAM,GAAG;YAChE;QACF,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,WAAW,MAAM,aAAa;gBAC9B,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAY;KAAK;IAErB,gCAAgC;IAChC,MAAM,gBAAgB;QACpB,sBAAsB;QACtB,IAAI;YACF,MAAM,YAAY,MAAM,4JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC5D,aAAa,aAAa,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,gBAAgB;YAChB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;gBACzB,YAAY;gBACZ,gBAAgB;gBAChB;YACF;YAEA,4BAA4B;YAC5B,MAAM,aAAa;gBACjB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,SAAS,SAAS,OAAO,IAAI;gBAC7B,QAAQ,SAAS,MAAM;YACzB;YAEA,iDAAiD;YACjD,IAAI,eAAe;gBACjB,WAAW,SAAS,GAAG,SAAS,SAAS,IAAI;YAC/C;YAEA,IAAI,YAAY;gBACd,iBAAiB;gBACjB,MAAM,gKAAA,CAAA,qBAAkB,CAAC,gBAAgB,CAAC,WAAW,EAAE,EAAE;gBACzD,cAAc;YAChB,OAAO;gBACL,kBAAkB;gBAClB,MAAM,gKAAA,CAAA,qBAAkB,CAAC,gBAAgB,CAAC;gBAC1C,cAAc;YAChB;YAEA,IAAI,WAAW;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;gBAA4B,SAAS;;;;;;0BACpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,aAAa,qBAAqB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;;4CACX;0DACM,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEtC,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,IAAI;wCACpB,UAAU;wCACV,WAAU;wCACV,QAAQ;;;;;;;;;;;;0CAKZ,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,MAAK;wCACL,WAAU;;;;;;;;;;;;0CAKd,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,OAAO;wCACvB,UAAU;wCACV,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;oDAAsB,OAAO,MAAM,EAAE;8DACnC,MAAM,IAAI;mDADA,MAAM,EAAE;;;;;;;;;;;;;;;;;4BAQ1B,+BACC,8OAAC;;kDACC,8OAAC;wCACC,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAqB;;;;;;;kDAGrD,8OAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU;wCACV,WAAU;wCACV,UAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;oDAAwB,OAAO,QAAQ,EAAE;8DACvC,QAAQ,IAAI;mDADF,QAAQ,EAAE;;;;;;;;;;;oCAK1B,oCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iNAAA,CAAA,UAAO;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC7B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAOb,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,SAAS,SAAS,MAAM;wCACxB,UAAU;wCACV,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAQ;wCACR,WAAU;kDACX;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC7B,8OAAC;8DAAK;;;;;;;yEAGR,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe"}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/admin/ProfessionGroupFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { X, Loader2, Building, ChevronDown, ChevronRight, Search, Shield, Info, FileText, Settings, Users, DollarSign, Calendar, CheckSquare, AlertCircle } from \"lucide-react\";\r\nimport { professionsService } from \"@/app/modules/admin/services/professionsService\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { PERMISSIONS_CONFIG, getAllPermissions } from \"@/utils/permissionConfig\";\r\n\r\nconst ProfessionGroupFormModal = ({ isOpen, onClose, group = null, onSuccess }) => {\r\n  const { toast_success, toast_error } = useToast();\r\n  const { user } = useAuth();\r\n  const isSystemAdmin = user?.role === \"SYSTEM_ADMIN\";\r\n\r\n  // Estado para controlar a tab ativa\r\n  const [activeTab, setActiveTab] = useState(\"info\");\r\n\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    description: \"\",\r\n    companyId: \"\",\r\n    active: true,\r\n    defaultModules: [\"BASIC\"],\r\n    defaultPermissions: []\r\n  });\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [expandedModules, setExpandedModules] = useState({});\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [filteredPermissions, setFilteredPermissions] = useState([]);\r\n\r\n  // Função para mudar de tab\r\n  const handleTabChange = (tab) => {\r\n    setActiveTab(tab);\r\n  };\r\n\r\n\r\n\r\n  // Carregar empresas quando o modal é aberto (apenas para system admin)\r\n  useEffect(() => {\r\n    if (isOpen && isSystemAdmin) {\r\n      loadCompanies();\r\n    }\r\n  }, [isOpen, isSystemAdmin]);\r\n\r\n  // Carregar dados do grupo quando selecionado\r\n  useEffect(() => {\r\n    if (group) {\r\n      setFormData({\r\n        name: group.name || \"\",\r\n        description: group.description || \"\",\r\n        companyId: group.companyId || \"\",\r\n        active: group.active !== undefined ? group.active : true,\r\n        defaultModules: group.defaultModules || [\"BASIC\"],\r\n        defaultPermissions: group.defaultPermissions || []\r\n      });\r\n    } else {\r\n      setFormData({\r\n        name: \"\",\r\n        description: \"\",\r\n        companyId: user?.companyId || \"\",\r\n        active: true,\r\n        defaultModules: [\"BASIC\"],\r\n        defaultPermissions: []\r\n      });\r\n    }\r\n  }, [group, user]);\r\n\r\n  // Inicializar permissões filtradas quando o modal é aberto\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      setFilteredPermissions(getAllPermissions());\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // Filtrar permissões com base no termo de busca\r\n  useEffect(() => {\r\n    if (!searchTerm.trim()) {\r\n      setFilteredPermissions(getAllPermissions());\r\n      return;\r\n    }\r\n\r\n    const searchTermLower = searchTerm.toLowerCase();\r\n    const filtered = getAllPermissions().filter(\r\n      (permission) =>\r\n        permission.name.toLowerCase().includes(searchTermLower) ||\r\n        permission.description.toLowerCase().includes(searchTermLower) ||\r\n        permission.id.toLowerCase().includes(searchTermLower) ||\r\n        PERMISSIONS_CONFIG[permission.moduleId].name\r\n          .toLowerCase()\r\n          .includes(searchTermLower)\r\n    );\r\n\r\n    setFilteredPermissions(filtered);\r\n\r\n    // Expandir módulos que têm permissões correspondentes\r\n    const modulesToExpand = {};\r\n    filtered.forEach(permission => {\r\n      modulesToExpand[permission.moduleId] = true;\r\n    });\r\n\r\n    setExpandedModules(prev => ({\r\n      ...prev,\r\n      ...modulesToExpand\r\n    }));\r\n  }, [searchTerm]);\r\n\r\n  // Função para carregar empresas\r\n  const loadCompanies = async () => {\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const companies = await companyService.getCompaniesForSelect();\r\n      setCompanies(companies || []);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: type === \"checkbox\" ? checked : value\r\n    });\r\n  };\r\n\r\n  // Função para alternar um módulo\r\n  const handleToggleModule = (moduleId) => {\r\n    if (moduleId === \"BASIC\") return; // BASIC é obrigatório\r\n\r\n    setFormData(prev => {\r\n      const defaultModules = [...prev.defaultModules];\r\n\r\n      if (defaultModules.includes(moduleId)) {\r\n        return {\r\n          ...prev,\r\n          defaultModules: defaultModules.filter(m => m !== moduleId)\r\n        };\r\n      } else {\r\n        return {\r\n          ...prev,\r\n          defaultModules: [...defaultModules, moduleId]\r\n        };\r\n      }\r\n    });\r\n  };\r\n\r\n  // Função para alternar uma permissão\r\n  const handleTogglePermission = (permissionId) => {\r\n    setFormData(prev => {\r\n      const defaultPermissions = [...prev.defaultPermissions];\r\n\r\n      if (defaultPermissions.includes(permissionId)) {\r\n        return {\r\n          ...prev,\r\n          defaultPermissions: defaultPermissions.filter(p => p !== permissionId)\r\n        };\r\n      } else {\r\n        return {\r\n          ...prev,\r\n          defaultPermissions: [...defaultPermissions, permissionId]\r\n        };\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n\r\n  // Função para alternar a expansão de um módulo\r\n  const toggleModuleExpansion = (moduleId) => {\r\n    setExpandedModules(prev => ({\r\n      ...prev,\r\n      [moduleId]: !prev[moduleId]\r\n    }));\r\n  };\r\n\r\n  // Alternar todas as permissões de um módulo\r\n  const toggleModulePermissions = (moduleId) => {\r\n    const moduleConfig = PERMISSIONS_CONFIG[moduleId];\r\n    if (!moduleConfig) return;\r\n\r\n    const permissions = moduleConfig.permissions;\r\n    if (!permissions || permissions.length === 0) return;\r\n\r\n    // Verificar se todas as permissões do módulo estão selecionadas\r\n    const allSelected = permissions.every(p =>\r\n      formData.defaultPermissions.includes(p.id)\r\n    );\r\n\r\n    // Se todas estiverem selecionadas, remover todas; caso contrário, adicionar todas\r\n    if (allSelected) {\r\n      // Remover todas as permissões deste módulo\r\n      const updatedPermissions = formData.defaultPermissions.filter(\r\n        id => !permissions.some(p => p.id === id)\r\n      );\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        defaultPermissions: updatedPermissions\r\n      }));\r\n    } else {\r\n      // Adicionar todas as permissões deste módulo que ainda não estão selecionadas\r\n      const permissionsToAdd = permissions\r\n        .filter(p => !formData.defaultPermissions.includes(p.id))\r\n        .map(p => p.id);\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        defaultPermissions: [...prev.defaultPermissions, ...permissionsToAdd]\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Validar dados\r\n      if (!formData.name.trim()) {\r\n        toast_error(\"O nome do grupo é obrigatório\");\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      // Garantir que BASIC esteja sempre incluído nos módulos padrão\r\n      if (!formData.defaultModules.includes(\"BASIC\")) {\r\n        formData.defaultModules.push(\"BASIC\");\r\n      }\r\n\r\n      // Preparar dados para envio\r\n      const dataToSend = {\r\n        name: formData.name.trim(),\r\n        description: formData.description.trim() || undefined,\r\n        active: formData.active,\r\n        defaultModules: formData.defaultModules,\r\n        defaultPermissions: formData.defaultPermissions\r\n      };\r\n\r\n      // Adicionar companyId apenas se for system admin\r\n      if (isSystemAdmin) {\r\n        dataToSend.companyId = formData.companyId || null;\r\n      }\r\n\r\n      if (group) {\r\n        // Modo de edição\r\n        await professionsService.updateProfessionGroup(group.id, dataToSend);\r\n        toast_success(\"Grupo atualizado com sucesso\");\r\n      } else {\r\n        // Modo de criação\r\n        await professionsService.createProfessionGroup(dataToSend);\r\n        toast_success(\"Grupo criado com sucesso\");\r\n      }\r\n\r\n      if (onSuccess) onSuccess();\r\n      onClose(); // Fechar o modal após salvar com sucesso\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar grupo:\", error);\r\n      toast_error(error.response?.data?.message || \"Erro ao salvar grupo\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Classes CSS comuns\r\n  const inputClasses = \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\";\r\n  const iconContainerClasses = \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\";\r\n  const labelClasses = \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\";\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n      {/* Overlay de fundo escuro */}\r\n      <div className=\"fixed inset-0 bg-black/50\" onClick={onClose}></div>\r\n\r\n      <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[55]\">\r\n        {/* Header */}\r\n        <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">\r\n            {group ? \"Editar Grupo de Profissões\" : \"Novo Grupo de Profissões\"}\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex\">\r\n            <button\r\n              onClick={() => handleTabChange(\"info\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"info\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                }`}\r\n            >\r\n              <Info size={16} />\r\n              <span>Informações</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"modules\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"modules\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                }`}\r\n            >\r\n              <Shield size={16} />\r\n              <span>Módulos</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"permissions\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"permissions\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                }`}\r\n            >\r\n              <FileText size={16} />\r\n              <span>Permissões</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Form */}\r\n        <div className=\"overflow-y-auto p-6\">\r\n          {/* Tab de Informações */}\r\n          {activeTab === \"info\" && (\r\n            <div>\r\n              <div className=\"mb-6\">\r\n                <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\">\r\n                  {group?.name || \"Novo Grupo de Profissões\"}\r\n                </h4>\r\n                <p className=\"text-sm text-neutral-600 dark:text-gray-400 mb-4\">\r\n                  Preencha as informações básicas do grupo de profissões:\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"space-y-6\">\r\n                {/* Nome */}\r\n                <div>\r\n                  <label className={labelClasses} htmlFor=\"name\">\r\n                    Nome <span className=\"text-red-500\">*</span>\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className={iconContainerClasses}>\r\n                      <Info className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"name\"\r\n                      name=\"name\"\r\n                      value={formData.name}\r\n                      onChange={handleChange}\r\n                      className={inputClasses}\r\n                      required\r\n                      placeholder=\"Nome do grupo de profissões\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Descrição */}\r\n                <div>\r\n                  <label className={labelClasses} htmlFor=\"description\">\r\n                    Descrição\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className={iconContainerClasses}>\r\n                      <FileText className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <textarea\r\n                      id=\"description\"\r\n                      name=\"description\"\r\n                      value={formData.description}\r\n                      onChange={handleChange}\r\n                      rows=\"3\"\r\n                      className={`${inputClasses} resize-none`}\r\n                      placeholder=\"Descrição detalhada do grupo de profissões\"\r\n                    ></textarea>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Empresa (apenas para system admin) */}\r\n                {isSystemAdmin && (\r\n                  <div>\r\n                    <label className={labelClasses} htmlFor=\"companyId\">\r\n                      Empresa\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <div className={iconContainerClasses}>\r\n                        <Building className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                      </div>\r\n                      <select\r\n                        id=\"companyId\"\r\n                        name=\"companyId\"\r\n                        value={formData.companyId}\r\n                        onChange={handleChange}\r\n                        className={inputClasses}\r\n                        disabled={isLoadingCompanies}\r\n                      >\r\n                        <option value=\"\">Selecione uma empresa</option>\r\n                        {companies.map((company) => (\r\n                          <option key={company.id} value={company.id}>\r\n                            {company.name}\r\n                          </option>\r\n                        ))}\r\n                      </select>\r\n                    </div>\r\n                    {isLoadingCompanies && (\r\n                      <div className=\"mt-1 text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1\">\r\n                        <Loader2 size={12} className=\"animate-spin\" />\r\n                        <span>Carregando empresas...</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Status (apenas em modo de edição) */}\r\n                {group && (\r\n                  <div className=\"flex items-center mt-4\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"active\"\r\n                      name=\"active\"\r\n                      checked={formData.active}\r\n                      onChange={handleChange}\r\n                      className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\r\n                    />\r\n                    <label\r\n                      htmlFor=\"active\"\r\n                      className=\"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\"\r\n                    >\r\n                      Grupo ativo\r\n                    </label>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Tab de Módulos */}\r\n          {activeTab === \"modules\" && (\r\n            <div>\r\n              <div className=\"mb-6\">\r\n                <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\">\r\n                  {group?.name || \"Novo Grupo de Profissões\"}\r\n                </h4>\r\n                <p className=\"text-sm text-neutral-600 dark:text-gray-400 mb-4\">\r\n                  Selecione os módulos padrão que serão atribuídos aos usuários deste grupo de profissões:\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                {/* Módulo ADMIN */}\r\n                <div\r\n                  className={`p-4 rounded-lg border ${formData.defaultModules.includes(\"ADMIN\") ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50\" : \"border-neutral-200 dark:border-gray-700\"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}\r\n                  onClick={() => handleToggleModule(\"ADMIN\")}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.defaultModules.includes(\"ADMIN\")}\r\n                        onChange={() => {}}\r\n                        disabled={isSubmitting}\r\n                        className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Settings className={`h-5 w-5 ${formData.defaultModules.includes(\"ADMIN\") ? \"\" : \"text-neutral-500 dark:text-gray-400\"}`} />\r\n                        <h5 className=\"font-medium text-neutral-800 dark:text-white\">Administração</h5>\r\n                      </div>\r\n                      <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-300\">\r\n                        Gerenciamento de usuários, empresas, configurações do sistema\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Módulo RH */}\r\n                <div\r\n                  className={`p-4 rounded-lg border ${formData.defaultModules.includes(\"RH\") ? \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50\" : \"border-neutral-200 dark:border-gray-700\"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}\r\n                  onClick={() => handleToggleModule(\"RH\")}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.defaultModules.includes(\"RH\")}\r\n                        onChange={() => {}}\r\n                        disabled={isSubmitting}\r\n                        className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Users className={`h-5 w-5 ${formData.defaultModules.includes(\"RH\") ? \"\" : \"text-neutral-500 dark:text-gray-400\"}`} />\r\n                        <h5 className=\"font-medium text-neutral-800 dark:text-white\">Recursos Humanos</h5>\r\n                      </div>\r\n                      <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-300\">\r\n                        Gerenciamento de funcionários, folha de pagamento, benefícios\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Módulo FINANCIAL */}\r\n                <div\r\n                  className={`p-4 rounded-lg border ${formData.defaultModules.includes(\"FINANCIAL\") ? \"bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border-amber-200 dark:border-amber-800/50\" : \"border-neutral-200 dark:border-gray-700\"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}\r\n                  onClick={() => handleToggleModule(\"FINANCIAL\")}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.defaultModules.includes(\"FINANCIAL\")}\r\n                        onChange={() => {}}\r\n                        disabled={isSubmitting}\r\n                        className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <DollarSign className={`h-5 w-5 ${formData.defaultModules.includes(\"FINANCIAL\") ? \"\" : \"text-neutral-500 dark:text-gray-400\"}`} />\r\n                        <h5 className=\"font-medium text-neutral-800 dark:text-white\">Financeiro</h5>\r\n                      </div>\r\n                      <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-300\">\r\n                        Controle de faturas, pagamentos, despesas e relatórios financeiros\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Módulo SCHEDULING */}\r\n                <div\r\n                  className={`p-4 rounded-lg border ${formData.defaultModules.includes(\"SCHEDULING\") ? \"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50\" : \"border-neutral-200 dark:border-gray-700\"} cursor-pointer hover:border-primary-300 dark:hover:border-primary-700`}\r\n                  onClick={() => handleToggleModule(\"SCHEDULING\")}\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={formData.defaultModules.includes(\"SCHEDULING\")}\r\n                        onChange={() => {}}\r\n                        disabled={isSubmitting}\r\n                        className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Calendar className={`h-5 w-5 ${formData.defaultModules.includes(\"SCHEDULING\") ? \"\" : \"text-neutral-500 dark:text-gray-400\"}`} />\r\n                        <h5 className=\"font-medium text-neutral-800 dark:text-white\">Agendamento</h5>\r\n                      </div>\r\n                      <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-300\">\r\n                        Gerenciamento de compromissos, reuniões e alocação de recursos\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Módulo BASIC */}\r\n                <div\r\n                  className=\"p-4 rounded-lg border bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70\"\r\n                >\r\n                  <div className=\"flex items-start gap-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={true}\r\n                        onChange={() => {}}\r\n                        disabled={true}\r\n                        className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\r\n                      />\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <CheckSquare className=\"h-5 w-5\" />\r\n                        <h5 className=\"font-medium text-neutral-800 dark:text-white\">Básico</h5>\r\n                      </div>\r\n                      <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-300\">\r\n                        Acesso básico ao sistema, visualização limitada\r\n                      </p>\r\n                      <div className=\"mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1\">\r\n                        <AlertCircle size={12} />\r\n                        <span>Módulo obrigatório para todos os usuários</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Tab de Permissões */}\r\n          {activeTab === \"permissions\" && (\r\n            <div>\r\n              <div className=\"mb-6\">\r\n                <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\">\r\n                  {group?.name || \"Novo Grupo de Profissões\"}\r\n                </h4>\r\n                <p className=\"text-sm text-neutral-600 dark:text-gray-400 mb-4\">\r\n                  Configure as permissões padrão que serão atribuídas aos usuários deste grupo de profissões:\r\n                </p>\r\n\r\n                <div className=\"bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50\">\r\n                  <div className=\"flex-shrink-0 mt-1\">\r\n                    <Info className=\"h-5 w-5 text-amber-500 dark:text-amber-400\" />\r\n                  </div>\r\n                  <div>\r\n                    <h5 className=\"font-medium text-amber-800 dark:text-amber-300\">Importante</h5>\r\n                    <p className=\"text-sm text-amber-700 dark:text-amber-400\">\r\n                      As permissões só serão aplicadas se o usuário também tiver\r\n                      acesso ao módulo correspondente. Certifique-se de que o\r\n                      usuário tenha os módulos necessários atribuídos.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Barra de pesquisa */}\r\n              <div className=\"mb-6\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                    <Search className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                  </div>\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Buscar permissões...\"\r\n                    className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                    value={searchTerm}\r\n                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Módulos Atribuídos */}\r\n              <div className=\"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700\">\r\n                <h4 className=\"font-medium text-neutral-700 dark:text-gray-300 mb-2\">\r\n                  Módulos Padrão\r\n                </h4>\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {formData.defaultModules.map((moduleId) => (\r\n                    <div\r\n                      key={moduleId}\r\n                      className=\"px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2\"\r\n                    >\r\n                      {moduleId === \"ADMIN\" && <Settings className=\"h-5 w-5\" />}\r\n                      {moduleId === \"RH\" && <Users className=\"h-5 w-5\" />}\r\n                      {moduleId === \"FINANCIAL\" && <DollarSign className=\"h-5 w-5\" />}\r\n                      {moduleId === \"SCHEDULING\" && <Calendar className=\"h-5 w-5\" />}\r\n                      {moduleId === \"BASIC\" && <CheckSquare className=\"h-5 w-5\" />}\r\n                      <span className=\"text-sm dark:text-gray-300\">\r\n                        {moduleId === \"ADMIN\" && \"Administração\"}\r\n                        {moduleId === \"RH\" && \"Recursos Humanos\"}\r\n                        {moduleId === \"FINANCIAL\" && \"Financeiro\"}\r\n                        {moduleId === \"SCHEDULING\" && \"Agendamento\"}\r\n                        {moduleId === \"BASIC\" && \"Básico\"}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                  {(!formData.defaultModules || formData.defaultModules.length === 0) && (\r\n                    <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                      Nenhum módulo atribuído\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Lista de permissões */}\r\n              <div className=\"space-y-6\">\r\n                {/* Agrupar permissões por módulo */}\r\n                {Object.entries(PERMISSIONS_CONFIG).map(([moduleId, moduleConfig]) => {\r\n                  // Só mostrar módulos que o grupo tem acesso\r\n                  if (!formData.defaultModules.includes(moduleId)) return null;\r\n\r\n                  // Filtrar permissões deste módulo\r\n                  const modulePermissions = filteredPermissions.filter(\r\n                    (p) => p.moduleId === moduleId\r\n                  );\r\n\r\n                  if (modulePermissions.length === 0) return null;\r\n\r\n                  return (\r\n                    <div key={moduleId} className=\"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\">\r\n                      {/* Cabeçalho do módulo */}\r\n                      <div\r\n                        className=\"bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer\"\r\n                        onClick={() => toggleModuleExpansion(moduleId)}\r\n                      >\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <div className=\"p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\">\r\n                            {moduleId === \"ADMIN\" && <Settings className=\"h-5 w-5\" />}\r\n                            {moduleId === \"RH\" && <Users className=\"h-5 w-5\" />}\r\n                            {moduleId === \"FINANCIAL\" && <DollarSign className=\"h-5 w-5\" />}\r\n                            {moduleId === \"SCHEDULING\" && <Calendar className=\"h-5 w-5\" />}\r\n                            {moduleId === \"BASIC\" && <CheckSquare className=\"h-5 w-5\" />}\r\n                          </div>\r\n                          <div>\r\n                            <h3 className=\"font-medium text-neutral-800 dark:text-gray-200\">{moduleConfig.name}</h3>\r\n                            <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                              {moduleId === \"ADMIN\" && \"Permissões para gerenciamento administrativo\"}\r\n                              {moduleId === \"RH\" && \"Permissões para recursos humanos\"}\r\n                              {moduleId === \"FINANCIAL\" && \"Permissões para gestão financeira\"}\r\n                              {moduleId === \"SCHEDULING\" && \"Permissões para agendamento\"}\r\n                              {moduleId === \"BASIC\" && \"Permissões básicas do sistema\"}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              toggleModulePermissions(moduleId);\r\n                            }}\r\n                            className=\"px-3 py-1 rounded text-sm font-medium bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700\"\r\n                          >\r\n                            Selecionar todas\r\n                          </button>\r\n                          {expandedModules[moduleId] ? (\r\n                            <ChevronDown className=\"h-5 w-5 text-neutral-500 dark:text-gray-400\" />\r\n                          ) : (\r\n                            <ChevronRight className=\"h-5 w-5 text-neutral-500 dark:text-gray-400\" />\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Lista de permissões do módulo */}\r\n                      {expandedModules[moduleId] && (\r\n                        <div className=\"p-4 divide-y dark:divide-gray-700 dark:bg-gray-850\">\r\n                          {modulePermissions.map((permission) => (\r\n                            <div key={permission.id} className=\"py-3 first:pt-0 last:pb-0\">\r\n                              <div className=\"flex items-start gap-3\">\r\n                                <div className=\"flex-shrink-0 mt-0.5\">\r\n                                  <input\r\n                                    type=\"checkbox\"\r\n                                    id={permission.id}\r\n                                    checked={formData.defaultPermissions.includes(permission.id)}\r\n                                    onChange={() => handleTogglePermission(permission.id)}\r\n                                    className=\"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500\"\r\n                                  />\r\n                                </div>\r\n\r\n                                <div className=\"flex-1\">\r\n                                  <label\r\n                                    htmlFor={permission.id}\r\n                                    className=\"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\"\r\n                                  >\r\n                                    {permission.name}\r\n                                  </label>\r\n                                  <p className=\"mt-1 text-sm text-neutral-600 dark:text-gray-400\">\r\n                                    {permission.description}\r\n                                  </p>\r\n                                  <div className=\"mt-1 text-xs text-neutral-500 dark:text-gray-500\">\r\n                                    ID: {permission.id}\r\n                                  </div>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })}\r\n\r\n                {/* Mensagem quando não há permissões */}\r\n                {filteredPermissions.length === 0 && (\r\n                  <div className=\"text-center py-8\">\r\n                    <p className=\"text-neutral-500 dark:text-gray-400\">\r\n                      Nenhuma permissão encontrada.\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800\">\r\n          <div>\r\n            {activeTab !== \"info\" && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  // Definir a aba anterior com base na aba atual\r\n                  const prevTab = {\r\n                    modules: \"info\",\r\n                    permissions: \"modules\"\r\n                  }[activeTab];\r\n                  setActiveTab(prevTab);\r\n                }}\r\n                className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\"\r\n                disabled={isSubmitting}\r\n              >\r\n                Voltar\r\n              </button>\r\n            )}\r\n          </div>\r\n          <div className=\"flex gap-3\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\"\r\n              disabled={isSubmitting}\r\n            >\r\n              Cancelar\r\n            </button>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleSubmit}\r\n              className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\"\r\n              disabled={isSubmitting}\r\n            >\r\n              {isSubmitting ? (\r\n                <>\r\n                  <Loader2 size={16} className=\"animate-spin\" />\r\n                  <span>Salvando...</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {activeTab === \"info\" && <Info size={16} />}\r\n                  {activeTab === \"modules\" && <Shield size={16} />}\r\n                  {activeTab === \"permissions\" && <FileText size={16} />}\r\n                  <span>\r\n                    {!group ? (\r\n                      (activeTab === \"permissions\") ? \"Criar Grupo\" : \"Continuar\"\r\n                    ) : (\r\n                      `Salvar ${activeTab === \"info\" ? \"Grupo\" :\r\n                              activeTab === \"modules\" ? \"Módulos\" :\r\n                              \"Permissões\"}`\r\n                    )}\r\n                  </span>\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfessionGroupFormModal;\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AACA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAOA,MAAM,2BAA2B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,SAAS,EAAE;IAC5E,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,gBAAgB,MAAM,SAAS;IAErC,oCAAoC;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,WAAW;QACX,QAAQ;QACR,gBAAgB;YAAC;SAAQ;QACzB,oBAAoB,EAAE;IACxB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAEjE,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAIA,uEAAuE;IACvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,eAAe;YAC3B;QACF;IACF,GAAG;QAAC;QAAQ;KAAc;IAE1B,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,YAAY;gBACV,MAAM,MAAM,IAAI,IAAI;gBACpB,aAAa,MAAM,WAAW,IAAI;gBAClC,WAAW,MAAM,SAAS,IAAI;gBAC9B,QAAQ,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,GAAG;gBACpD,gBAAgB,MAAM,cAAc,IAAI;oBAAC;iBAAQ;gBACjD,oBAAoB,MAAM,kBAAkB,IAAI,EAAE;YACpD;QACF,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,WAAW,MAAM,aAAa;gBAC9B,QAAQ;gBACR,gBAAgB;oBAAC;iBAAQ;gBACzB,oBAAoB,EAAE;YACxB;QACF;IACF,GAAG;QAAC;QAAO;KAAK;IAEhB,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,uBAAuB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;QACzC;IACF,GAAG;QAAC;KAAO;IAEX,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,uBAAuB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;YACvC;QACF;QAEA,MAAM,kBAAkB,WAAW,WAAW;QAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,IAAI,MAAM,CACzC,CAAC,aACC,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACvC,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAC9C,WAAW,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACrC,gIAAA,CAAA,qBAAkB,CAAC,WAAW,QAAQ,CAAC,CAAC,IAAI,CACzC,WAAW,GACX,QAAQ,CAAC;QAGhB,uBAAuB;QAEvB,sDAAsD;QACtD,MAAM,kBAAkB,CAAC;QACzB,SAAS,OAAO,CAAC,CAAA;YACf,eAAe,CAAC,WAAW,QAAQ,CAAC,GAAG;QACzC;QAEA,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,GAAG,eAAe;YACpB,CAAC;IACH,GAAG;QAAC;KAAW;IAEf,gCAAgC;IAChC,MAAM,gBAAgB;QACpB,sBAAsB;QACtB,IAAI;YACF,MAAM,YAAY,MAAM,4JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC5D,aAAa,aAAa,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,iCAAiC;IACjC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,aAAa,SAAS,QAAQ,sBAAsB;QAExD,YAAY,CAAA;YACV,MAAM,iBAAiB;mBAAI,KAAK,cAAc;aAAC;YAE/C,IAAI,eAAe,QAAQ,CAAC,WAAW;gBACrC,OAAO;oBACL,GAAG,IAAI;oBACP,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;gBACnD;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,IAAI;oBACP,gBAAgB;2BAAI;wBAAgB;qBAAS;gBAC/C;YACF;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,yBAAyB,CAAC;QAC9B,YAAY,CAAA;YACV,MAAM,qBAAqB;mBAAI,KAAK,kBAAkB;aAAC;YAEvD,IAAI,mBAAmB,QAAQ,CAAC,eAAe;gBAC7C,OAAO;oBACL,GAAG,IAAI;oBACP,oBAAoB,mBAAmB,MAAM,CAAC,CAAA,IAAK,MAAM;gBAC3D;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,IAAI;oBACP,oBAAoB;2BAAI;wBAAoB;qBAAa;gBAC3D;YACF;QACF;IACF;IAIA,+CAA+C;IAC/C,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAC7B,CAAC;IACH;IAEA,4CAA4C;IAC5C,MAAM,0BAA0B,CAAC;QAC/B,MAAM,eAAe,gIAAA,CAAA,qBAAkB,CAAC,SAAS;QACjD,IAAI,CAAC,cAAc;QAEnB,MAAM,cAAc,aAAa,WAAW;QAC5C,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;QAE9C,gEAAgE;QAChE,MAAM,cAAc,YAAY,KAAK,CAAC,CAAA,IACpC,SAAS,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE;QAG3C,kFAAkF;QAClF,IAAI,aAAa;YACf,2CAA2C;YAC3C,MAAM,qBAAqB,SAAS,kBAAkB,CAAC,MAAM,CAC3D,CAAA,KAAM,CAAC,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAExC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,oBAAoB;gBACtB,CAAC;QACH,OAAO;YACL,8EAA8E;YAC9E,MAAM,mBAAmB,YACtB,MAAM,CAAC,CAAA,IAAK,CAAC,SAAS,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,GACtD,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YAChB,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,oBAAoB;2BAAI,KAAK,kBAAkB;2BAAK;qBAAiB;gBACvE,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAEhB,IAAI;YACF,gBAAgB;YAChB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;gBACzB,YAAY;gBACZ,gBAAgB;gBAChB;YACF;YAEA,+DAA+D;YAC/D,IAAI,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,UAAU;gBAC9C,SAAS,cAAc,CAAC,IAAI,CAAC;YAC/B;YAEA,4BAA4B;YAC5B,MAAM,aAAa;gBACjB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,aAAa,SAAS,WAAW,CAAC,IAAI,MAAM;gBAC5C,QAAQ,SAAS,MAAM;gBACvB,gBAAgB,SAAS,cAAc;gBACvC,oBAAoB,SAAS,kBAAkB;YACjD;YAEA,iDAAiD;YACjD,IAAI,eAAe;gBACjB,WAAW,SAAS,GAAG,SAAS,SAAS,IAAI;YAC/C;YAEA,IAAI,OAAO;gBACT,iBAAiB;gBACjB,MAAM,gKAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAAC,MAAM,EAAE,EAAE;gBACzD,cAAc;YAChB,OAAO;gBACL,kBAAkB;gBAClB,MAAM,gKAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAAC;gBAC/C,cAAc;YAChB;YAEA,IAAI,WAAW;YACf,WAAW,yCAAyC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,YAAY,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,qBAAqB;IACrB,MAAM,eAAe;IACrB,MAAM,uBAAuB;IAC7B,MAAM,eAAe;IAErB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;gBAA4B,SAAS;;;;;;0BAEpD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,QAAQ,+BAA+B;;;;;;0CAE1C,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,SAChG,iGACA,+HACA;;sDAEJ,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,YAChG,iGACA,+HACA;;sDAEJ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;sDACd,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,gBAChG,iGACA,+HACA;;sDAEJ,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,wBACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,OAAO,QAAQ;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;;;;;;;kDAKlE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAM,WAAW;wDAAc,SAAQ;;4DAAO;0EACxC,8OAAC;gEAAK,WAAU;0EAAe;;;;;;;;;;;;kEAEtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW;0EACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAW;gEACX,QAAQ;gEACR,aAAY;;;;;;;;;;;;;;;;;;0DAMlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAW;wDAAc,SAAQ;kEAAc;;;;;;kEAGtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW;0EACd,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,WAAW;gEAC3B,UAAU;gEACV,MAAK;gEACL,WAAW,GAAG,aAAa,YAAY,CAAC;gEACxC,aAAY;;;;;;;;;;;;;;;;;;4CAMjB,+BACC,8OAAC;;kEACC,8OAAC;wDAAM,WAAW;wDAAc,SAAQ;kEAAY;;;;;;kEAGpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW;0EACd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,SAAS;gEACzB,UAAU;gEACV,WAAW;gEACX,UAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;4EAAwB,OAAO,QAAQ,EAAE;sFACvC,QAAQ,IAAI;2EADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;oDAM5B,oCACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iNAAA,CAAA,UAAO;gEAAC,MAAM;gEAAI,WAAU;;;;;;0EAC7B,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;4CAOb,uBACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,SAAS,MAAM;wDACxB,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDACC,SAAQ;wDACR,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;4BAUV,cAAc,2BACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,OAAO,QAAQ;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;;;;;;;kDAKlE,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW,6GAA6G,0CAA0C,sEAAsE,CAAC;gDAC9S,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,cAAc,CAAC,QAAQ,CAAC;gEAC1C,UAAU,KAAO;gEACjB,UAAU;gEACV,WAAU;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW,KAAK,uCAAuC;;;;;;sFACxH,8OAAC;4EAAG,WAAU;sFAA+C;;;;;;;;;;;;8EAE/D,8OAAC;oEAAE,WAAU;8EAAmD;;;;;;;;;;;;;;;;;;;;;;;0DAQtE,8OAAC;gDACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,QAAQ,mHAAmH,0CAA0C,sEAAsE,CAAC;gDACjT,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,cAAc,CAAC,QAAQ,CAAC;gEAC1C,UAAU,KAAO;gEACjB,UAAU;gEACV,WAAU;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,QAAQ,KAAK,uCAAuC;;;;;;sFAClH,8OAAC;4EAAG,WAAU;sFAA+C;;;;;;;;;;;;8EAE/D,8OAAC;oEAAE,WAAU;8EAAmD;;;;;;;;;;;;;;;;;;;;;;;0DAQtE,8OAAC;gDACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,eAAe,mHAAmH,0CAA0C,sEAAsE,CAAC;gDACxT,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,cAAc,CAAC,QAAQ,CAAC;gEAC1C,UAAU,KAAO;gEACjB,UAAU;gEACV,WAAU;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,eAAe,KAAK,uCAAuC;;;;;;sFAC9H,8OAAC;4EAAG,WAAU;sFAA+C;;;;;;;;;;;;8EAE/D,8OAAC;oEAAE,WAAU;8EAAmD;;;;;;;;;;;;;;;;;;;;;;;0DAQtE,8OAAC;gDACC,WAAW,CAAC,sBAAsB,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,gBAAgB,yHAAyH,0CAA0C,sEAAsE,CAAC;gDAC/T,SAAS,IAAM,mBAAmB;0DAElC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS,SAAS,cAAc,CAAC,QAAQ,CAAC;gEAC1C,UAAU,KAAO;gEACjB,UAAU;gEACV,WAAU;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC,QAAQ,CAAC,gBAAgB,KAAK,uCAAuC;;;;;;sFAC7H,8OAAC;4EAAG,WAAU;sFAA+C;;;;;;;;;;;;8EAE/D,8OAAC;oEAAE,WAAU;8EAAmD;;;;;;;;;;;;;;;;;;;;;;;0DAQtE,8OAAC;gDACC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,UAAU,KAAO;gEACjB,UAAU;gEACV,WAAU;;;;;;;;;;;sEAId,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;sFACvB,8OAAC;4EAAG,WAAU;sFAA+C;;;;;;;;;;;;8EAE/D,8OAAC;oEAAE,WAAU;8EAAmD;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oNAAA,CAAA,cAAW;4EAAC,MAAM;;;;;;sFACnB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUnB,cAAc,+BACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,OAAO,QAAQ;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;0DAIhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAiD;;;;;;0EAC/D,8OAAC;gEAAE,WAAU;0EAA6C;;;;;;;;;;;;;;;;;;;;;;;;kDAUhE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;kDAMnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,cAAc,CAAC,GAAG,CAAC,CAAC,yBAC5B,8OAAC;4DAEC,WAAU;;gEAET,aAAa,yBAAW,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAC5C,aAAa,sBAAQ,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEACtC,aAAa,6BAAe,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAClD,aAAa,8BAAgB,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACjD,aAAa,yBAAW,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EAChD,8OAAC;oEAAK,WAAU;;wEACb,aAAa,WAAW;wEACxB,aAAa,QAAQ;wEACrB,aAAa,eAAe;wEAC5B,aAAa,gBAAgB;wEAC7B,aAAa,WAAW;;;;;;;;2DAbtB;;;;;oDAiBR,CAAC,CAAC,SAAS,cAAc,IAAI,SAAS,cAAc,CAAC,MAAM,KAAK,CAAC,mBAChE,8OAAC;wDAAE,WAAU;kEAA8C;;;;;;;;;;;;;;;;;;kDAQjE,8OAAC;wCAAI,WAAU;;4CAEZ,OAAO,OAAO,CAAC,gIAAA,CAAA,qBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,aAAa;gDAC/D,4CAA4C;gDAC5C,IAAI,CAAC,SAAS,cAAc,CAAC,QAAQ,CAAC,WAAW,OAAO;gDAExD,kCAAkC;gDAClC,MAAM,oBAAoB,oBAAoB,MAAM,CAClD,CAAC,IAAM,EAAE,QAAQ,KAAK;gDAGxB,IAAI,kBAAkB,MAAM,KAAK,GAAG,OAAO;gDAE3C,qBACE,8OAAC;oDAAmB,WAAU;;sEAE5B,8OAAC;4DACC,WAAU;4DACV,SAAS,IAAM,sBAAsB;;8EAErC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;gFACZ,aAAa,yBAAW,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAC5C,aAAa,sBAAQ,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFACtC,aAAa,6BAAe,8OAAC,kNAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFAClD,aAAa,8BAAgB,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACjD,aAAa,yBAAW,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;;sFAElD,8OAAC;;8FACC,8OAAC;oFAAG,WAAU;8FAAmD,aAAa,IAAI;;;;;;8FAClF,8OAAC;oFAAE,WAAU;;wFACV,aAAa,WAAW;wFACxB,aAAa,QAAQ;wFACrB,aAAa,eAAe;wFAC5B,aAAa,gBAAgB;wFAC7B,aAAa,WAAW;;;;;;;;;;;;;;;;;;;8EAI/B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EACC,MAAK;4EACL,SAAS,CAAC;gFACR,EAAE,eAAe;gFACjB,wBAAwB;4EAC1B;4EACA,WAAU;sFACX;;;;;;wEAGA,eAAe,CAAC,SAAS,iBACxB,8OAAC,oNAAA,CAAA,cAAW;4EAAC,WAAU;;;;;iGAEvB,8OAAC,sNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;wDAM7B,eAAe,CAAC,SAAS,kBACxB,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,8OAAC;oEAAwB,WAAU;8EACjC,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFACC,MAAK;oFACL,IAAI,WAAW,EAAE;oFACjB,SAAS,SAAS,kBAAkB,CAAC,QAAQ,CAAC,WAAW,EAAE;oFAC3D,UAAU,IAAM,uBAAuB,WAAW,EAAE;oFACpD,WAAU;;;;;;;;;;;0FAId,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,SAAS,WAAW,EAAE;wFACtB,WAAU;kGAET,WAAW,IAAI;;;;;;kGAElB,8OAAC;wFAAE,WAAU;kGACV,WAAW,WAAW;;;;;;kGAEzB,8OAAC;wFAAI,WAAU;;4FAAmD;4FAC3D,WAAW,EAAE;;;;;;;;;;;;;;;;;;;mEAvBhB,WAAW,EAAE;;;;;;;;;;;mDAhDrB;;;;;4CAiFd;4CAGC,oBAAoB,MAAM,KAAK,mBAC9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CACE,cAAc,wBACb,8OAAC;oCACC,MAAK;oCACL,SAAS;wCACP,+CAA+C;wCAC/C,MAAM,UAAU;4CACd,SAAS;4CACT,aAAa;wCACf,CAAC,CAAC,UAAU;wCACZ,aAAa;oCACf;oCACA,WAAU;oCACV,UAAU;8CACX;;;;;;;;;;;0CAKL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,UAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,UAAU;kDAET,6BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC7B,8OAAC;8DAAK;;;;;;;yEAGR;;gDACG,cAAc,wBAAU,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;gDACpC,cAAc,2BAAa,8OAAC,sMAAA,CAAA,SAAM;oDAAC,MAAM;;;;;;gDACzC,cAAc,+BAAiB,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;8DAChD,8OAAC;8DACE,CAAC,QACA,AAAC,cAAc,gBAAiB,gBAAgB,cAEhD,CAAC,OAAO,EAAE,cAAc,SAAS,UACzB,cAAc,YAAY,YAC1B,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5C;uCAEe"}}, {"offset": {"line": 3065, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3071, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/components/admin/ProfessionUsersModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { X, Loader2, Search, RefreshCw, User, CheckCircle, XCircle, Mail, Phone } from \"lucide-react\";\r\nimport { professionsService } from \"@/app/modules/admin/services/professionsService\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport { ModuleSelect } from \"@/components/ui\";\r\n\r\nconst ProfessionUsersModal = ({ isOpen, onClose, professionId }) => {\r\n  const { toast_error } = useToast();\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [profession, setProfession] = useState(null);\r\n  const [users, setUsers] = useState([]);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [statusFilter, setStatusFilter] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    if (isOpen && professionId) {\r\n      loadUsers();\r\n    }\r\n  }, [isOpen, professionId]);\r\n\r\n  useEffect(() => {\r\n    if (users.length > 0) {\r\n      filterUsers();\r\n    }\r\n  }, [search, statusFilter, users]);\r\n\r\n  const loadUsers = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const params = {};\r\n      if (statusFilter) {\r\n        params.active = statusFilter === \"active\";\r\n      }\r\n\r\n      const data = await professionsService.getProfessionUsers(professionId, params);\r\n      setProfession(data.profession);\r\n      setUsers(data.users);\r\n      setFilteredUsers(data.users);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar usuários da profissão:\", error);\r\n      toast_error(\"Erro ao carregar usuários da profissão\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const filterUsers = () => {\r\n    let filtered = [...users];\r\n\r\n    // Filtrar por termo de busca\r\n    if (search.trim() !== \"\") {\r\n      const searchLower = search.toLowerCase();\r\n      filtered = filtered.filter(\r\n        (user) =>\r\n          user.fullName.toLowerCase().includes(searchLower) ||\r\n          user.email.toLowerCase().includes(searchLower) ||\r\n          user.login.toLowerCase().includes(searchLower)\r\n      );\r\n    }\r\n\r\n    // Filtrar por status\r\n    if (statusFilter === \"active\") {\r\n      filtered = filtered.filter((user) => user.active);\r\n    } else if (statusFilter === \"inactive\") {\r\n      filtered = filtered.filter((user) => !user.active);\r\n    }\r\n\r\n    setFilteredUsers(filtered);\r\n  };\r\n\r\n  const handleSearch = (e) => {\r\n    setSearch(e.target.value);\r\n  };\r\n\r\n  const handleStatusFilterChange = (value) => {\r\n    setStatusFilter(value);\r\n    loadUsers();\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setStatusFilter(\"\");\r\n    loadUsers();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n      <div className=\"fixed inset-0 bg-black/50\" onClick={onClose}></div>\r\n      <div className=\"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center p-4 border-b border-neutral-200 dark:border-gray-700\">\r\n          <h2 className=\"text-lg font-medium text-neutral-800 dark:text-neutral-100\">\r\n            {profession ? `Usuários com a profissão: ${profession.name}` : \"Carregando...\"}\r\n          </h2>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors\"\r\n          >\r\n            <X size={20} className=\"text-neutral-500 dark:text-neutral-400\" />\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-4\">\r\n          {/* Filtros */}\r\n          <div className=\"mb-4 flex flex-col md:flex-row gap-4\">\r\n            <div className=\"flex-1 relative\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\" />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Buscar por nome, email ou login...\"\r\n                value={search}\r\n                onChange={handleSearch}\r\n                className=\"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex-shrink-0 w-full md:w-40\">\r\n              <ModuleSelect\r\n                moduleColor=\"admin\"\r\n                value={statusFilter}\r\n                onChange={(e) => handleStatusFilterChange(e.target.value)}\r\n                placeholder=\"Status\"\r\n              >\r\n                <option value=\"\">Todos os status</option>\r\n                <option value=\"active\">Ativos</option>\r\n                <option value=\"inactive\">Inativos</option>\r\n              </ModuleSelect>\r\n            </div>\r\n\r\n            <button\r\n              onClick={handleResetFilters}\r\n              className=\"flex-shrink-0 flex items-center gap-2 px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\"\r\n            >\r\n              <RefreshCw size={16} />\r\n              <span>Limpar</span>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Lista de usuários */}\r\n          {isLoading ? (\r\n            <div className=\"flex justify-center items-center py-8\">\r\n              <Loader2 size={32} className=\"animate-spin text-primary-500 dark:text-primary-400\" />\r\n            </div>\r\n          ) : filteredUsers.length === 0 ? (\r\n            <div className=\"text-center py-8 text-neutral-500 dark:text-neutral-400\">\r\n              Nenhum usuário encontrado com esta profissão.\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n                <thead>\r\n                  <tr className=\"bg-neutral-50 dark:bg-gray-700\">\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider\">\r\n                      Usuário\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider\">\r\n                      Contato\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider\">\r\n                      Empresa\r\n                    </th>\r\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider\">\r\n                      Status\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n                  {filteredUsers.map((user) => (\r\n                    <tr key={user.id} className=\"hover:bg-neutral-50 dark:hover:bg-gray-700\">\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\">\r\n                            <User size={20} />\r\n                          </div>\r\n                          <div className=\"ml-4\">\r\n                            <div className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                              {user.fullName}\r\n                            </div>\r\n                            <div className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                              {user.login}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex flex-col\">\r\n                          <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n                            <Mail size={14} className=\"mr-1\" />\r\n                            {user.email}\r\n                          </div>\r\n                          {user.phone && (\r\n                            <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n                              <Phone size={14} className=\"mr-1\" />\r\n                              {user.phone}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-neutral-900 dark:text-neutral-100\">\r\n                          {user.company?.name || \"N/A\"}\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <span\r\n                          className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                            user.active\r\n                              ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                              : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                          }`}\r\n                        >\r\n                          {user.active ? (\r\n                            <>\r\n                              <CheckCircle size={12} />\r\n                              <span>Ativo</span>\r\n                            </>\r\n                          ) : (\r\n                            <>\r\n                              <XCircle size={12} />\r\n                              <span>Inativo</span>\r\n                            </>\r\n                          )}\r\n                        </span>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfessionUsersModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;AAAA;AAGA;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;AAQA,MAAM,uBAAuB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE;IAC7D,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,cAAc;YAC1B;QACF;IACF,GAAG;QAAC;QAAQ;KAAa;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB;QACF;IACF,GAAG;QAAC;QAAQ;QAAc;KAAM;IAEhC,MAAM,YAAY;QAChB,aAAa;QACb,IAAI;YACF,MAAM,SAAS,CAAC;YAChB,IAAI,cAAc;gBAChB,OAAO,MAAM,GAAG,iBAAiB;YACnC;YAEA,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,kBAAkB,CAAC,cAAc;YACvE,cAAc,KAAK,UAAU;YAC7B,SAAS,KAAK,KAAK;YACnB,iBAAiB,KAAK,KAAK;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,YAAY;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;eAAI;SAAM;QAEzB,6BAA6B;QAC7B,IAAI,OAAO,IAAI,OAAO,IAAI;YACxB,MAAM,cAAc,OAAO,WAAW;YACtC,WAAW,SAAS,MAAM,CACxB,CAAC,OACC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;QAExC;QAEA,qBAAqB;QACrB,IAAI,iBAAiB,UAAU;YAC7B,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,MAAM;QAClD,OAAO,IAAI,iBAAiB,YAAY;YACtC,WAAW,SAAS,MAAM,CAAC,CAAC,OAAS,CAAC,KAAK,MAAM;QACnD;QAEA,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU,EAAE,MAAM,CAAC,KAAK;IAC1B;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB;IACF;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,gBAAgB;QAChB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;gBAA4B,SAAS;;;;;;0BACpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,aAAa,CAAC,0BAA0B,EAAE,WAAW,IAAI,EAAE,GAAG;;;;;;0CAEjE,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kLAAA,CAAA,eAAY;4CACX,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;4CACxD,aAAY;;8DAEZ,8OAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;;;;;;kDAI7B,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,MAAM;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAKT,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;uCAE7B,cAAc,MAAM,KAAK,kBAC3B,8OAAC;gCAAI,WAAU;0CAA0D;;;;;qDAIzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;sDACC,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;kEAA0G;;;;;;kEAGxH,8OAAC;wDAAG,WAAU;kEAA0G;;;;;;kEAGxH,8OAAC;wDAAG,WAAU;kEAA0G;;;;;;kEAGxH,8OAAC;wDAAG,WAAU;kEAA0G;;;;;;;;;;;;;;;;;sDAK5H,8OAAC;4CAAM,WAAU;sDACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,MAAM;;;;;;;;;;;kFAEd,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACZ,KAAK,QAAQ;;;;;;0FAEhB,8OAAC;gFAAI,WAAU;0FACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kMAAA,CAAA,OAAI;gFAAC,MAAM;gFAAI,WAAU;;;;;;4EACzB,KAAK,KAAK;;;;;;;oEAEZ,KAAK,KAAK,kBACT,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,oMAAA,CAAA,QAAK;gFAAC,MAAM;gFAAI,WAAU;;;;;;4EAC1B,KAAK,KAAK;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;0EACZ,KAAK,OAAO,EAAE,QAAQ;;;;;;;;;;;sEAG3B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEACC,WAAW,CAAC,6DAA6D,EACvE,KAAK,MAAM,GACP,yEACA,gEACJ;0EAED,KAAK,MAAM,iBACV;;sFACE,8OAAC,2NAAA,CAAA,cAAW;4EAAC,MAAM;;;;;;sFACnB,8OAAC;sFAAK;;;;;;;iGAGR;;sFACE,8OAAC,4MAAA,CAAA,UAAO;4EAAC,MAAM;;;;;;sFACf,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;mDAnDP,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEpC;uCAEe"}}, {"offset": {"line": 3613, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3669, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/modules/admin/professions/ProfessionsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from \"react\";\r\nimport {\r\n  Plus,\r\n  Search,\r\n  Filter,\r\n  RefreshCw,\r\n  Edit,\r\n  Trash,\r\n  Tag,\r\n  Briefcase,\r\n  CheckCircle,\r\n  XCircle,\r\n  Users,\r\n  UserRound,\r\n  Building,\r\n  Layers\r\n} from \"lucide-react\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\nimport { Protected } from \"@/components/permissions/Protected\";\r\nimport { professionsService } from \"@/app/modules/admin/services/professionsService\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport ProfessionFormModal from \"@/components/admin/ProfessionFormModal\";\r\nimport ProfessionGroupFormModal from \"@/components/admin/ProfessionGroupFormModal\";\r\nimport ProfessionUsersModal from \"@/components/admin/ProfessionUsersModal\";\r\nimport { ModuleHeader, ModuleInput, ModuleSelect, ModuleTable, ModalButton, ModuleTabs, MultiSelect } from \"@/components/ui\";\r\n\r\nconst ProfessionsPage = () => {\r\n  const { user: currentUser } = useAuth();\r\n  const { toast_success, toast_error } = useToast();\r\n  const [professions, setProfessions] = useState([]);\r\n  const [allProfessions, setAllProfessions] = useState([]); // Armazena todas as profissões para paginação manual\r\n  const [groups, setGroups] = useState([]);\r\n  const [allGroups, setAllGroups] = useState([]); // Armazena todos os grupos para paginação manual\r\n  const [filteredGroups, setFilteredGroups] = useState([]);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isLoadingGroups, setIsLoadingGroups] = useState(true);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [groupFilter, setGroupFilter] = useState(\"\");\r\n  const [statusFilter, setStatusFilter] = useState(\"\");\r\n  const [companyFilter, setCompanyFilter] = useState(\"\");\r\n  const [professionsFilter, setProfessionsFilter] = useState([]);\r\n  const [professionOptions, setProfessionOptions] = useState([]);\r\n  const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = useState(false);\r\n  const [groupsFilter, setGroupsFilter] = useState([]);\r\n  const [groupOptions, setGroupOptions] = useState([]);\r\n  const [isLoadingGroupOptions, setIsLoadingGroupOptions] = useState(false);\r\n  const [professionFormOpen, setProfessionFormOpen] = useState(false);\r\n  const [groupFormOpen, setGroupFormOpen] = useState(false);\r\n  const [usersModalOpen, setUsersModalOpen] = useState(false);\r\n  const [selectedProfession, setSelectedProfession] = useState(null);\r\n  const [selectedGroup, setSelectedGroup] = useState(null);\r\n  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);\r\n  const [actionToConfirm, setActionToConfirm] = useState(null);\r\n  const [activeTab, setActiveTab] = useState(\"professions\"); // \"professions\" ou \"groups\"\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Estados para paginação de profissões\r\n  const [currentProfessionsPage, setCurrentProfessionsPage] = useState(1);\r\n  const [totalProfessionsPages, setTotalProfessionsPages] = useState(1);\r\n  const [totalProfessions, setTotalProfessions] = useState(0);\r\n\r\n  // Estados para paginação de grupos\r\n  const [currentGroupsPage, setCurrentGroupsPage] = useState(1);\r\n  const [totalGroupsPages, setTotalGroupsPages] = useState(1);\r\n  const [totalGroups, setTotalGroups] = useState(0);\r\n\r\n  // Constante para itens por página\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Verificar se o usuário é administrador do sistema\r\n  const isSystemAdmin = currentUser?.role === \"SYSTEM_ADMIN\";\r\n\r\n  // Função para carregar opções de profissões para o multi-select\r\n  const loadProfessionOptions = useCallback(async () => {\r\n    setIsLoadingProfessionOptions(true);\r\n    try {\r\n      // Carregar todas as profissões para o multi-select\r\n      const data = await professionsService.getProfessions({\r\n        active: true // Apenas profissões ativas por padrão\r\n      });\r\n\r\n      // Transformar os dados para o formato esperado pelo MultiSelect\r\n      const options = data.map(profession => ({\r\n        value: profession.id,\r\n        label: profession.name\r\n      }));\r\n\r\n      setProfessionOptions(options);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de profissões:\", error);\r\n    } finally {\r\n      setIsLoadingProfessionOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  // Função para carregar opções de grupos para o multi-select\r\n  const loadGroupOptions = useCallback(async () => {\r\n    setIsLoadingGroupOptions(true);\r\n    try {\r\n      // Carregar todos os grupos para o multi-select\r\n      const data = await professionsService.getProfessionGroups({\r\n        active: true // Apenas grupos ativos por padrão\r\n      });\r\n\r\n      // Transformar os dados para o formato esperado pelo MultiSelect\r\n      const options = data.map(group => ({\r\n        value: group.id,\r\n        label: group.name\r\n      }));\r\n\r\n      setGroupOptions(options);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de grupos:\", error);\r\n    } finally {\r\n      setIsLoadingGroupOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  // Carregar profissões\r\n  const loadProfessions = async (\r\n    page = currentProfessionsPage,\r\n    searchQuery = search,\r\n    groupId = groupFilter,\r\n    status = statusFilter,\r\n    company = companyFilter,\r\n    professionIds = professionsFilter,\r\n    sortField = \"name\",\r\n    sortDirection = \"asc\"\r\n  ) => {\r\n    setIsLoading(true);\r\n    try {\r\n      // Garantir que a página é um número\r\n      const pageNumber = parseInt(page, 10);\r\n\r\n      // Atualizar o estado da página atual\r\n      setCurrentProfessionsPage(pageNumber);\r\n\r\n      // Buscar todas as profissões\r\n      const data = await professionsService.getProfessions({\r\n        search: searchQuery || undefined,\r\n        groupId: groupId || undefined,\r\n        active: status === \"\" ? undefined : status === \"active\",\r\n        companyId: company || undefined,\r\n        professionIds: professionIds.length > 0 ? professionIds : undefined,\r\n        sortField: sortField,\r\n        sortDirection: sortDirection\r\n      });\r\n\r\n      // Armazenar todas as profissões para paginação manual\r\n      setAllProfessions(data);\r\n\r\n      // Calcular o total de itens e páginas\r\n      const total = data.length;\r\n      const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;\r\n\r\n      // Aplicar paginação manual\r\n      const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;\r\n      const endIndex = startIndex + ITEMS_PER_PAGE;\r\n      const paginatedProfessions = data.slice(startIndex, endIndex);\r\n\r\n      // Atualizar o estado com os dados paginados manualmente\r\n      setProfessions(paginatedProfessions);\r\n      setTotalProfessions(total);\r\n      setTotalProfessionsPages(pages);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar profissões:\", error);\r\n      toast_error(\"Erro ao carregar profissões\");\r\n      setProfessions([]);\r\n      setTotalProfessions(0);\r\n      setTotalProfessionsPages(1);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Carregar grupos de profissões\r\n  const loadGroups = async (page = currentGroupsPage) => {\r\n    setIsLoadingGroups(true);\r\n    try {\r\n      // Garantir que a página é um número\r\n      const pageNumber = parseInt(page, 10);\r\n\r\n      // Atualizar o estado da página atual\r\n      setCurrentGroupsPage(pageNumber);\r\n\r\n      // Buscar todos os grupos\r\n      const data = await professionsService.getProfessionGroups({\r\n        active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\r\n      });\r\n\r\n      // Armazenar todos os grupos para paginação manual\r\n      setAllGroups(data);\r\n\r\n      if (activeTab === \"professions\") {\r\n        // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\r\n        setGroups(data);\r\n      } else {\r\n        // Na tab de grupos, aplicamos paginação\r\n        // Calcular o total de itens e páginas\r\n        const total = data.length;\r\n        const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;\r\n\r\n        // Aplicar paginação manual\r\n        const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;\r\n        const endIndex = startIndex + ITEMS_PER_PAGE;\r\n        const paginatedGroups = data.slice(startIndex, endIndex);\r\n\r\n        // Atualizar o estado com os dados paginados manualmente\r\n        setGroups(data); // Mantemos todos os grupos para o filtro\r\n        setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\r\n        setTotalGroups(total);\r\n        setTotalGroupsPages(pages);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar grupos de profissões:\", error);\r\n      toast_error(\"Erro ao carregar grupos de profissões\");\r\n      setGroups([]);\r\n      setFilteredGroups([]);\r\n      setTotalGroups(0);\r\n      setTotalGroupsPages(1);\r\n    } finally {\r\n      setIsLoadingGroups(false);\r\n    }\r\n  };\r\n\r\n  // Filtrar grupos quando o usuário submeter o formulário\r\n  const filterGroups = (\r\n    searchTerm,\r\n    page = currentGroupsPage,\r\n    groupIds = groupsFilter\r\n  ) => {\r\n    // Garantir que a página é um número\r\n    const pageNumber = parseInt(page, 10);\r\n\r\n    // Atualizar o estado da página atual\r\n    setCurrentGroupsPage(pageNumber);\r\n\r\n    // Log para debug\r\n    console.log(\"Filtrando grupos com:\", {\r\n      searchTerm,\r\n      page: pageNumber,\r\n      groupIds,\r\n      statusFilter,\r\n      companyFilter\r\n    });\r\n\r\n    // Buscar grupos do servidor com os filtros aplicados\r\n    const loadFilteredGroups = async () => {\r\n      setIsLoadingGroups(true);\r\n      try {\r\n        const data = await professionsService.getProfessionGroups({\r\n          search: searchTerm || undefined,\r\n          active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\r\n          companyId: companyFilter || undefined,\r\n          groupIds: groupIds.length > 0 ? groupIds : undefined\r\n        });\r\n\r\n        // Armazenar todos os grupos para paginação manual\r\n        setAllGroups(data);\r\n\r\n        // Calcular o total de itens e páginas\r\n        const total = data.length;\r\n        const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;\r\n\r\n        // Aplicar paginação manual\r\n        const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;\r\n        const endIndex = startIndex + ITEMS_PER_PAGE;\r\n        const paginatedGroups = data.slice(startIndex, endIndex);\r\n\r\n        // Atualizar o estado com os dados paginados manualmente\r\n        setFilteredGroups(paginatedGroups);\r\n        setTotalGroups(total);\r\n        setTotalGroupsPages(pages);\r\n      } catch (error) {\r\n        console.error(\"Erro ao filtrar grupos:\", error);\r\n        toast_error(\"Erro ao filtrar grupos\");\r\n        setFilteredGroups([]);\r\n        setTotalGroups(0);\r\n        setTotalGroupsPages(1);\r\n      } finally {\r\n        setIsLoadingGroups(false);\r\n      }\r\n    };\r\n\r\n    // Executar a busca\r\n    loadFilteredGroups();\r\n  };\r\n\r\n  // Carregar empresas (apenas para system admin)\r\n  const loadCompanies = async () => {\r\n    if (!isSystemAdmin) return;\r\n\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const companies = await companyService.getCompaniesForSelect();\r\n      setCompanies(companies || []);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadProfessions();\r\n    loadGroups();\r\n    if (isSystemAdmin) {\r\n      loadCompanies();\r\n    }\r\n    // Carregar opções para os multi-selects\r\n    loadProfessionOptions();\r\n    loadGroupOptions();\r\n  }, [isSystemAdmin, loadProfessionOptions, loadGroupOptions]);\r\n\r\n  // Recarregar dados quando a tab mudar\r\n  useEffect(() => {\r\n    setSearch(\"\"); // Limpar a busca ao trocar de tab\r\n    setGroupFilter(\"\");\r\n    setStatusFilter(\"\");\r\n    setCompanyFilter(\"\");\r\n    setProfessionsFilter([]);\r\n    setGroupsFilter([]);\r\n\r\n    // Resetar para a primeira página\r\n    if (activeTab === \"professions\") {\r\n      setCurrentProfessionsPage(1);\r\n      loadProfessions(1);\r\n    } else {\r\n      setCurrentGroupsPage(1);\r\n      loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\r\n    }\r\n  }, [activeTab]);\r\n\r\n  // Função de busca removida, agora usamos estados locais nos componentes de filtro\r\n\r\n  const handleGroupFilterChange = (value) => {\r\n    setGroupFilter(value);\r\n    // Acionar a busca automaticamente quando o usuário selecionar um grupo\r\n    setCurrentProfessionsPage(1);\r\n    loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\r\n  };\r\n\r\n  const handleStatusFilterChange = (value) => {\r\n    setStatusFilter(value);\r\n    // Acionar a busca automaticamente quando o usuário selecionar um status\r\n    if (activeTab === \"professions\") {\r\n      setCurrentProfessionsPage(1);\r\n      loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\r\n    } else {\r\n      setCurrentGroupsPage(1);\r\n      filterGroups(search, 1, groupsFilter);\r\n    }\r\n  };\r\n\r\n  const handleCompanyFilterChange = (value) => {\r\n    setCompanyFilter(value);\r\n    // Acionar a busca automaticamente quando o usuário selecionar uma empresa\r\n    if (activeTab === \"professions\") {\r\n      setCurrentProfessionsPage(1);\r\n      loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\r\n    } else {\r\n      setCurrentGroupsPage(1);\r\n      filterGroups(search, 1, groupsFilter);\r\n    }\r\n  };\r\n\r\n  const handleProfessionsFilterChange = (value) => {\r\n    setProfessionsFilter(value);\r\n    // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\r\n    setCurrentProfessionsPage(1);\r\n    loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\r\n  };\r\n\r\n  const handleGroupsFilterChange = (value) => {\r\n    setGroupsFilter(value);\r\n    // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\r\n    setCurrentGroupsPage(1);\r\n    filterGroups(search, 1, value);\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setGroupFilter(\"\");\r\n    setStatusFilter(\"\");\r\n    setCompanyFilter(\"\");\r\n    setProfessionsFilter([]);\r\n    setGroupsFilter([]);\r\n\r\n    // Resetar para a primeira página\r\n    if (activeTab === \"professions\") {\r\n      setCurrentProfessionsPage(1);\r\n      loadProfessions(1, \"\", \"\", \"\", \"\", []);\r\n    } else {\r\n      setCurrentGroupsPage(1);\r\n      filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\r\n    }\r\n\r\n    // Forçar a re-renderização dos componentes de filtro\r\n    setTimeout(() => {\r\n      const event = new Event('reset');\r\n      document.dispatchEvent(event);\r\n    }, 0);\r\n  };\r\n\r\n  // Função para lidar com a mudança de página de profissões\r\n  const handleProfessionsPageChange = (page) => {\r\n    loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\r\n  };\r\n\r\n  // Função para lidar com a mudança de página de grupos\r\n  const handleGroupsPageChange = (page) => {\r\n    filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\r\n  };\r\n\r\n  const handleEditProfession = (profession) => {\r\n    setSelectedProfession(profession);\r\n    setProfessionFormOpen(true);\r\n  };\r\n\r\n  const handleEditGroup = (group) => {\r\n    setSelectedGroup(group); // Se group for null, será criação de novo grupo\r\n    setGroupFormOpen(true);\r\n  };\r\n\r\n  const handleDeleteProfession = (profession) => {\r\n    setSelectedProfession(profession);\r\n    setActionToConfirm({\r\n      type: \"delete-profession\",\r\n      message: `Excluir permanentemente a profissão ${profession.name}?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const handleViewUsers = (profession) => {\r\n    setSelectedProfession(profession);\r\n    setUsersModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteGroup = (group) => {\r\n    setSelectedGroup(group);\r\n    setActionToConfirm({\r\n      type: \"delete-group\",\r\n      message: `Excluir permanentemente o grupo ${group.name}?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  // Função para exportar profissões\r\n  const handleExportProfessions = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      await professionsService.exportProfessions({\r\n        search: search || undefined,\r\n        professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\r\n        groupId: groupFilter || undefined,\r\n        active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\r\n        companyId: companyFilter || undefined\r\n      }, format);\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar profissões:\", error);\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  // Função para exportar grupos de profissões\r\n  const handleExportGroups = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      await professionsService.exportProfessionGroups({\r\n        search: search || undefined,\r\n        groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\r\n        active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\r\n        companyId: companyFilter || undefined\r\n      }, format);\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar grupos de profissões:\", error);\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  const confirmAction = async () => {\r\n    try {\r\n      if (actionToConfirm.type === \"delete-profession\") {\r\n        await professionsService.deleteProfession(selectedProfession.id);\r\n        toast_success(\"Profissão excluída com sucesso\");\r\n        loadProfessions();\r\n      } else if (actionToConfirm.type === \"delete-group\") {\r\n        await professionsService.deleteProfessionGroup(selectedGroup.id);\r\n        toast_success(\"Grupo excluído com sucesso\");\r\n        loadGroups();\r\n        loadProfessions(); // Recarregar profissões para atualizar os grupos\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao executar ação:\", error);\r\n      toast_error(error.response?.data?.message || \"Erro ao executar ação\");\r\n    } finally {\r\n      setConfirmationDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  // Configuração das tabs para o ModuleTabs\r\n  const tabsConfig = [\r\n    {\r\n      id: \"professions\",\r\n      label: \"Profissões\",\r\n      icon: <Briefcase size={18} />,\r\n      permission: \"admin.professions.view\"\r\n    },\r\n    {\r\n      id: \"groups\",\r\n      label: \"Grupos de Profissões\",\r\n      icon: <Layers size={18} />,\r\n      permission: \"admin.profession-groups.view\"\r\n    }\r\n  ];\r\n\r\n  // Filtrar tabs com base nas permissões do usuário\r\n  const filteredTabs = tabsConfig.filter(tab => {\r\n    if (!tab.permission) return true;\r\n    return <Protected permission={tab.permission} showFallback={false}>\r\n      {true}\r\n    </Protected>;\r\n  });\r\n\r\n  // Componente de filtros para profissões\r\n  const ProfessionsFilters = () => {\r\n    // Estado local para o campo de busca\r\n    const [localSearch, setLocalSearch] = useState(search);\r\n\r\n    // Atualizar o estado local quando o estado global mudar\r\n    useEffect(() => {\r\n      setLocalSearch(search);\r\n    }, [search]);\r\n\r\n    // Ouvir o evento de reset\r\n    useEffect(() => {\r\n      const handleReset = () => {\r\n        setLocalSearch(\"\");\r\n      };\r\n      document.addEventListener('reset', handleReset);\r\n      return () => {\r\n        document.removeEventListener('reset', handleReset);\r\n      };\r\n    }, []);\r\n\r\n    // Função para atualizar o estado local sem afetar o estado global\r\n    const handleLocalSearchChange = (e) => {\r\n      setLocalSearch(e.target.value);\r\n    };\r\n\r\n    // Função para aplicar o filtro quando o botão for clicado\r\n    const applyFilter = () => {\r\n      setSearch(localSearch);\r\n      setCurrentProfessionsPage(1);\r\n\r\n      // Log para debug\r\n      console.log(\"Aplicando filtros de profissões:\", {\r\n        search: localSearch,\r\n        groupFilter,\r\n        statusFilter,\r\n        companyFilter,\r\n        professionsFilter\r\n      });\r\n\r\n      loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex flex-col gap-4 mt-4\">\r\n        <div className=\"flex flex-col md:flex-row gap-4\">\r\n          <div className=\"flex-1 relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Buscar por nome ou descrição...\"\r\n              value={localSearch}\r\n              onChange={handleLocalSearchChange}\r\n              className=\"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  applyFilter();\r\n                }\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex flex-col sm:flex-row gap-2\">\r\n            <ModuleSelect\r\n              moduleColor=\"admin\"\r\n              value={groupFilter}\r\n              onChange={(e) => handleGroupFilterChange(e.target.value)}\r\n              disabled={isLoadingGroups}\r\n            >\r\n              <option value=\"\">Todos os grupos</option>\r\n              <option value=\"null\">Sem grupo</option>\r\n              {groups.map((group) => (\r\n                <option key={group.id} value={group.id}>\r\n                  {group.name}\r\n                </option>\r\n              ))}\r\n            </ModuleSelect>\r\n\r\n            <ModuleSelect\r\n              moduleColor=\"admin\"\r\n              value={statusFilter}\r\n              onChange={(e) => handleStatusFilterChange(e.target.value)}\r\n            >\r\n              <option value=\"\">Todos os status</option>\r\n              <option value=\"active\">Ativas</option>\r\n              <option value=\"inactive\">Inativas</option>\r\n            </ModuleSelect>\r\n\r\n            {/* Filtro de empresa (apenas para system admin) */}\r\n            {isSystemAdmin && (\r\n              <ModuleSelect\r\n                moduleColor=\"admin\"\r\n                value={companyFilter}\r\n                onChange={(e) => handleCompanyFilterChange(e.target.value)}\r\n                disabled={isLoadingCompanies}\r\n              >\r\n                <option value=\"\">Todas as empresas</option>\r\n                {companies.map((company) => (\r\n                  <option key={company.id} value={company.id}>\r\n                    {company.name}\r\n                  </option>\r\n                ))}\r\n              </ModuleSelect>\r\n            )}\r\n\r\n            <ModalButton\r\n              moduleColor=\"admin\"\r\n              type=\"button\"\r\n              onClick={applyFilter}\r\n            >\r\n              <Filter size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Filtrar</span>\r\n            </ModalButton>\r\n\r\n            <ModalButton\r\n              moduleColor=\"admin\"\r\n              type=\"button\"\r\n              onClick={handleResetFilters}\r\n              variant=\"secondary\"\r\n            >\r\n              <RefreshCw size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Limpar</span>\r\n            </ModalButton>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Multi-select para filtrar por múltiplas profissões */}\r\n        <div className=\"w-full\">\r\n          <MultiSelect\r\n            label=\"Filtrar por Profissões\"\r\n            value={professionsFilter}\r\n            onChange={handleProfessionsFilterChange}\r\n            options={professionOptions}\r\n            placeholder=\"Selecione uma ou mais profissões pelo nome...\"\r\n            loading={isLoadingProfessionOptions}\r\n            moduleOverride=\"admin\"\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Componente de filtros para grupos\r\n  const GroupsFilters = () => {\r\n    // Estado local para o campo de busca\r\n    const [localSearch, setLocalSearch] = useState(search);\r\n\r\n    // Atualizar o estado local quando o estado global mudar\r\n    useEffect(() => {\r\n      setLocalSearch(search);\r\n    }, [search]);\r\n\r\n    // Ouvir o evento de reset\r\n    useEffect(() => {\r\n      const handleReset = () => {\r\n        setLocalSearch(\"\");\r\n      };\r\n      document.addEventListener('reset', handleReset);\r\n      return () => {\r\n        document.removeEventListener('reset', handleReset);\r\n      };\r\n    }, []);\r\n\r\n    // Função para atualizar o estado local sem afetar o estado global\r\n    const handleLocalSearchChange = (e) => {\r\n      setLocalSearch(e.target.value);\r\n    };\r\n\r\n    // Função para aplicar o filtro quando o botão for clicado\r\n    const applyFilter = () => {\r\n      setSearch(localSearch);\r\n      setCurrentGroupsPage(1);\r\n\r\n      // Log para debug\r\n      console.log(\"Aplicando filtros de grupos:\", {\r\n        search: localSearch,\r\n        statusFilter,\r\n        companyFilter,\r\n        groupsFilter\r\n      });\r\n\r\n      filterGroups(localSearch, 1, groupsFilter);\r\n    };\r\n\r\n    return (\r\n      <div className=\"flex flex-col gap-4 mt-4\">\r\n        <div className=\"flex flex-col md:flex-row gap-4\">\r\n          <div className=\"flex-1 relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\" />\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Buscar grupos por nome ou descrição...\"\r\n              value={localSearch}\r\n              onChange={handleLocalSearchChange}\r\n              className=\"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n              onKeyDown={(e) => {\r\n                if (e.key === 'Enter') {\r\n                  e.preventDefault();\r\n                  applyFilter();\r\n                }\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex flex-col sm:flex-row gap-2\">\r\n            <ModuleSelect\r\n              moduleColor=\"admin\"\r\n              value={statusFilter}\r\n              onChange={(e) => handleStatusFilterChange(e.target.value)}\r\n            >\r\n              <option value=\"\">Todos os status</option>\r\n              <option value=\"active\">Ativos</option>\r\n              <option value=\"inactive\">Inativos</option>\r\n            </ModuleSelect>\r\n\r\n            {/* Filtro de empresa (apenas para system admin) */}\r\n            {isSystemAdmin && (\r\n              <ModuleSelect\r\n                moduleColor=\"admin\"\r\n                value={companyFilter}\r\n                onChange={(e) => handleCompanyFilterChange(e.target.value)}\r\n                disabled={isLoadingCompanies}\r\n              >\r\n                <option value=\"\">Todas as empresas</option>\r\n                {companies.map((company) => (\r\n                  <option key={company.id} value={company.id}>\r\n                    {company.name}\r\n                  </option>\r\n                ))}\r\n              </ModuleSelect>\r\n            )}\r\n\r\n            <ModalButton\r\n              moduleColor=\"admin\"\r\n              type=\"button\"\r\n              onClick={applyFilter}\r\n            >\r\n              <Filter size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Filtrar</span>\r\n            </ModalButton>\r\n\r\n            <ModalButton\r\n              moduleColor=\"admin\"\r\n              type=\"button\"\r\n              onClick={handleResetFilters}\r\n              variant=\"secondary\"\r\n            >\r\n              <RefreshCw size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Limpar</span>\r\n            </ModalButton>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Multi-select para filtrar por múltiplos grupos */}\r\n        <div className=\"w-full\">\r\n          <MultiSelect\r\n            label=\"Filtrar por Grupos\"\r\n            value={groupsFilter}\r\n            onChange={handleGroupsFilterChange}\r\n            options={groupOptions}\r\n            placeholder=\"Selecione um ou mais grupos pelo nome...\"\r\n            loading={isLoadingGroupOptions}\r\n            moduleOverride=\"admin\"\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Import tutorial steps from tutorialMapping\r\n  const professionsGroupsTutorialSteps = useMemo(() => {\r\n    // Import dynamically to avoid circular dependencies\r\n    const tutorialMap = require('@/tutorials/tutorialMapping').default;\r\n    return tutorialMap['/dashboard/admin/professions'] || [];\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <Briefcase size={24} className=\"mr-2 text-slate-600 dark:text-slate-400\" />\r\n          {activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"}\r\n        </h1>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Botão de exportar */}\r\n          {activeTab === \"professions\" && (\r\n            <ExportMenu\r\n              onExport={handleExportProfessions}\r\n              isExporting={isExporting}\r\n              disabled={isLoading || professions.length === 0}\r\n              className=\"text-slate-700 dark:text-slate-300\"\r\n            />\r\n          )}\r\n          {activeTab === \"groups\" && (\r\n            <ExportMenu\r\n              onExport={handleExportGroups}\r\n              isExporting={isExporting}\r\n              disabled={isLoadingGroups || filteredGroups.length === 0}\r\n              className=\"text-slate-700 dark:text-slate-300\"\r\n            />\r\n          )}\r\n\r\n          {/* Botão de adicionar */}\r\n          {activeTab === \"professions\" && (\r\n            <button\r\n              onClick={() => {\r\n                setSelectedProfession(null);\r\n                setProfessionFormOpen(true);\r\n              }}\r\n              className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all\"\r\n            >\r\n              <Plus size={18} />\r\n              <span className=\"font-medium\">Nova Profissão</span>\r\n            </button>\r\n          )}\r\n          {activeTab === \"groups\" && (\r\n            <button\r\n              onClick={() => {\r\n                setSelectedGroup(null);\r\n                setGroupFormOpen(true);\r\n              }}\r\n              className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all\"\r\n            >\r\n              <Plus size={18} />\r\n              <span className=\"font-medium\">Novo Grupo</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <ModuleHeader\r\n        title=\"Filtros\"\r\n        icon={<Filter size={22} className=\"text-module-admin-icon dark:text-module-admin-icon-dark\" />}\r\n        moduleColor=\"admin\"\r\n        tutorialSteps={professionsGroupsTutorialSteps}\r\n        tutorialName=\"admin-professions-groups-overview\"\r\n        filters={\r\n          <>\r\n            <ModuleTabs\r\n              tabs={filteredTabs}\r\n              activeTab={activeTab}\r\n              onTabChange={setActiveTab}\r\n              moduleColor=\"admin\"\r\n            />\r\n            {activeTab === \"professions\" ? <ProfessionsFilters /> : <GroupsFilters />}\r\n          </>\r\n        }\r\n      />\r\n\r\n      {/* Tabela de Profissões */}\r\n      {activeTab === \"professions\" && (\r\n        <Protected permission=\"admin.professions.view\">\r\n          <ModuleTable\r\n            moduleColor=\"admin\"\r\n            columns={[\r\n              { header: 'Profissão', field: 'name', width: '20%' },\r\n              { header: 'Grupo', field: 'group', width: '15%' },\r\n              ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),\r\n              { header: 'Descrição', field: 'description', width: '20%' },\r\n              { header: 'Usuários', field: 'users', width: '10%' },\r\n              { header: 'Status', field: 'active', width: '10%' },\r\n              { header: 'Ações', field: 'actions', className: 'text-right', width: '10%', sortable: false }\r\n            ]}\r\n            data={professions}\r\n            isLoading={isLoading}\r\n            emptyMessage=\"Nenhuma profissão encontrada\"\r\n            emptyIcon={<Briefcase size={24} />}\r\n            tableId=\"admin-professions-table\"\r\n            enableColumnToggle={true}\r\n            defaultSortField=\"name\"\r\n            defaultSortDirection=\"asc\"\r\n            currentPage={currentProfessionsPage}\r\n            totalPages={totalProfessionsPages}\r\n            totalItems={totalProfessions}\r\n            onPageChange={handleProfessionsPageChange}\r\n            onSort={(field, direction) => {\r\n              // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\r\n              loadProfessions(\r\n                currentProfessionsPage,\r\n                search,\r\n                groupFilter,\r\n                statusFilter,\r\n                companyFilter,\r\n                professionsFilter,\r\n                field,\r\n                direction\r\n              );\r\n            }}\r\n            showPagination={true}\r\n            itemsPerPage={ITEMS_PER_PAGE}\r\n            renderRow={(profession, index, moduleColors, visibleColumns) => (\r\n              <tr key={profession.id} className={moduleColors.hoverBg}>\r\n                {visibleColumns.includes('name') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\">\r\n                        <Briefcase size={20} />\r\n                      </div>\r\n                      <div className=\"ml-4\">\r\n                        <div className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                          {profession.name}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('group') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {profession.group ? (\r\n                      <span className=\"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\">\r\n                        {profession.group.name}\r\n                      </span>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-neutral-500 italic\">\r\n                        Sem grupo\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                )}\r\n\r\n                {isSystemAdmin && visibleColumns.includes('company') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {profession.company ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Building size={14} className=\"text-neutral-500\" />\r\n                        <span className=\"text-sm text-neutral-600 dark:text-neutral-300\">\r\n                          {profession.company.name}\r\n                        </span>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-neutral-500 italic\">\r\n                        Sem empresa\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('description') && (\r\n                  <td className=\"px-6 py-4\">\r\n                    <div className=\"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\">\r\n                      {profession.description || (\r\n                        <span className=\"text-neutral-400 dark:text-neutral-500 italic\">\r\n                          Sem descrição\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('users') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <Users size={16} className=\"text-neutral-400 dark:text-neutral-500 mr-2\" />\r\n                      <span className=\"text-sm text-neutral-600 dark:text-neutral-300\">\r\n                        {profession._count?.users || 0} usuários\r\n                      </span>\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('active') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <span\r\n                      className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                        profession.active\r\n                          ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                          : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                      }`}\r\n                    >\r\n                      {profession.active ? (\r\n                        <>\r\n                          <CheckCircle size={12} />\r\n                          <span>Ativa</span>\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <XCircle size={12} />\r\n                          <span>Inativa</span>\r\n                        </>\r\n                      )}\r\n                    </span>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('actions') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex justify-end gap-2\">\r\n                    <button\r\n                      onClick={() => handleViewUsers(profession)}\r\n                      className=\"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-colors\"\r\n                      title=\"Ver usuários com esta profissão\"\r\n                    >\r\n                      <UserRound size={18} />\r\n                    </button>\r\n                    <Protected permission=\"admin.professions.edit\">\r\n                      <button\r\n                        onClick={() => handleEditProfession(profession)}\r\n                        className=\"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-colors\"\r\n                        title=\"Editar profissão\"\r\n                      >\r\n                        <Edit size={18} />\r\n                      </button>\r\n                    </Protected>\r\n                    <Protected permission=\"admin.professions.delete\">\r\n                      <button\r\n                        onClick={() => handleDeleteProfession(profession)}\r\n                        className=\"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors\"\r\n                        title=\"Excluir profissão\"\r\n                        disabled={profession._count?.users > 0}\r\n                      >\r\n                        <Trash size={18} className={profession._count?.users > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"} />\r\n                      </button>\r\n                    </Protected>\r\n                  </div>\r\n                </td>\r\n                )}\r\n              </tr>\r\n            )}\r\n          />\r\n        </Protected>\r\n      )}\r\n\r\n      {/* Tabela de Grupos de Profissões */}\r\n      {activeTab === \"groups\" && (\r\n        <Protected permission=\"admin.profession-groups.view\">\r\n          <ModuleTable\r\n            moduleColor=\"admin\"\r\n            columns={[\r\n              { header: 'Grupo', field: 'name', width: '20%' },\r\n              { header: 'Descrição', field: 'description', width: '25%' },\r\n              ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),\r\n              { header: 'Profissões', field: 'professions', width: '15%' },\r\n              { header: 'Status', field: 'active', width: '10%' },\r\n              { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }\r\n            ]}\r\n            data={filteredGroups}\r\n            isLoading={isLoadingGroups}\r\n            emptyMessage=\"Nenhum grupo encontrado\"\r\n            emptyIcon={<Tag size={24} />}\r\n            tableId=\"admin-profession-groups-table\"\r\n            enableColumnToggle={true}\r\n            defaultSortField=\"name\"\r\n            defaultSortDirection=\"asc\"\r\n            currentPage={currentGroupsPage}\r\n            totalPages={totalGroupsPages}\r\n            totalItems={totalGroups}\r\n            onPageChange={handleGroupsPageChange}\r\n            onSort={(field, direction) => {\r\n              // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\r\n              // Implementar ordenação para grupos\r\n              const sortedGroups = [...filteredGroups].sort((a, b) => {\r\n                if (field === 'name') {\r\n                  return direction === 'asc'\r\n                    ? a.name.localeCompare(b.name)\r\n                    : b.name.localeCompare(a.name);\r\n                }\r\n                return 0;\r\n              });\r\n              setFilteredGroups(sortedGroups);\r\n            }}\r\n            showPagination={true}\r\n            itemsPerPage={ITEMS_PER_PAGE}\r\n            renderRow={(group, index, moduleColors, visibleColumns) => (\r\n              <tr key={group.id} className={moduleColors.hoverBg}>\r\n                {visibleColumns.includes('name') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\">\r\n                        <Tag size={20} />\r\n                      </div>\r\n                      <div className=\"ml-4\">\r\n                        <div className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                          {group.name}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('description') && (\r\n                  <td className=\"px-6 py-4\">\r\n                    <div className=\"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\">\r\n                      {group.description || (\r\n                        <span className=\"text-neutral-400 dark:text-neutral-500 italic\">\r\n                          Sem descrição\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {isSystemAdmin && visibleColumns.includes('company') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {group.company ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Building size={14} className=\"text-neutral-500\" />\r\n                        <span className=\"text-sm text-neutral-600 dark:text-neutral-300\">\r\n                          {group.company.name}\r\n                        </span>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-neutral-500 italic\">\r\n                        Sem empresa\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('professions') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <Briefcase size={16} className=\"text-neutral-400 dark:text-neutral-500 mr-2\" />\r\n                      <span className=\"text-sm text-neutral-600 dark:text-neutral-300\">\r\n                        {group._count?.professions || 0} profissões\r\n                      </span>\r\n                    </div>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('active') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <span\r\n                      className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                        group.active\r\n                          ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                          : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                      }`}\r\n                    >\r\n                      {group.active ? (\r\n                        <>\r\n                          <CheckCircle size={12} />\r\n                          <span>Ativo</span>\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <XCircle size={12} />\r\n                          <span>Inativo</span>\r\n                        </>\r\n                      )}\r\n                    </span>\r\n                  </td>\r\n                )}\r\n\r\n                {visibleColumns.includes('actions') && (\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex justify-end gap-2\">\r\n                    <Protected permission=\"admin.profession-groups.edit\">\r\n                      <button\r\n                        onClick={() => handleEditGroup(group)}\r\n                        className=\"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-colors\"\r\n                        title=\"Editar grupo\"\r\n                      >\r\n                        <Edit size={18} />\r\n                      </button>\r\n                    </Protected>\r\n                    <Protected permission=\"admin.profession-groups.delete\">\r\n                      <button\r\n                        onClick={() => handleDeleteGroup(group)}\r\n                        className=\"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors\"\r\n                        title=\"Excluir grupo\"\r\n                        disabled={group._count?.professions > 0}\r\n                      >\r\n                        <Trash size={18} className={group._count?.professions > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"} />\r\n                      </button>\r\n                    </Protected>\r\n                  </div>\r\n                </td>\r\n                )}\r\n              </tr>\r\n            )}\r\n          />\r\n        </Protected>\r\n      )}\r\n\r\n      {/* Modais */}\r\n      <ProfessionFormModal\r\n        isOpen={professionFormOpen}\r\n        onClose={() => setProfessionFormOpen(false)}\r\n        profession={selectedProfession}\r\n        groups={groups}\r\n        onSuccess={() => {\r\n          setProfessionFormOpen(false);\r\n          loadProfessions();\r\n        }}\r\n      />\r\n\r\n      <ProfessionGroupFormModal\r\n        isOpen={groupFormOpen}\r\n        onClose={() => setGroupFormOpen(false)}\r\n        group={selectedGroup}\r\n        onSuccess={() => {\r\n          setGroupFormOpen(false);\r\n          loadGroups();\r\n          loadProfessions(); // Recarregar profissões para atualizar os grupos\r\n        }}\r\n      />\r\n\r\n      <ProfessionUsersModal\r\n        isOpen={usersModalOpen}\r\n        onClose={() => setUsersModalOpen(false)}\r\n        professionId={selectedProfession?.id}\r\n      />\r\n\r\n      <ConfirmationDialog\r\n        isOpen={confirmationDialogOpen}\r\n        onClose={() => setConfirmationDialogOpen(false)}\r\n        onConfirm={confirmAction}\r\n        title=\"Confirmar ação\"\r\n        message={actionToConfirm?.message || \"\"}\r\n        variant={actionToConfirm?.type.includes(\"delete\") ? \"danger\" : \"primary\"}\r\n        confirmText={actionToConfirm?.type.includes(\"delete\") ? \"Excluir\" : \"Confirmar\"}\r\n      />\r\n\r\n      {/* Botão flutuante removido, mantendo apenas o botão de ajuda/tutorial que é adicionado pelo ModuleHeader */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfessionsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA;AAAA;AAAA;AA0BA;AAAA;AA1BA;AAAA;AA0BA;AA1BA;AA0BA;AAAA;AAAA;AA1BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;;;;;;AA+BA,MAAM,kBAAkB;IACtB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,GAAG,qDAAqD;IAC/G,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,GAAG,iDAAiD;IACjG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,4BAA4B;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,mCAAmC;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,iBAAiB;IAEvB,oDAAoD;IACpD,MAAM,gBAAgB,aAAa,SAAS;IAE5C,gEAAgE;IAChE,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,8BAA8B;QAC9B,IAAI;YACF,mDAAmD;YACnD,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBACnD,QAAQ,KAAK,sCAAsC;YACrD;YAEA,gEAAgE;YAChE,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,aAAc,CAAC;oBACtC,OAAO,WAAW,EAAE;oBACpB,OAAO,WAAW,IAAI;gBACxB,CAAC;YAED,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D,SAAU;YACR,8BAA8B;QAChC;IACF,GAAG,EAAE;IAEL,4DAA4D;IAC5D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,yBAAyB;QACzB,IAAI;YACF,+CAA+C;YAC/C,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;gBACxD,QAAQ,KAAK,kCAAkC;YACjD;YAEA,gEAAgE;YAChE,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;oBACjC,OAAO,MAAM,EAAE;oBACf,OAAO,MAAM,IAAI;gBACnB,CAAC;YAED,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD,SAAU;YACR,yBAAyB;QAC3B;IACF,GAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,kBAAkB,OACtB,OAAO,sBAAsB,EAC7B,cAAc,MAAM,EACpB,UAAU,WAAW,EACrB,SAAS,YAAY,EACrB,UAAU,aAAa,EACvB,gBAAgB,iBAAiB,EACjC,YAAY,MAAM,EAClB,gBAAgB,KAAK;QAErB,aAAa;QACb,IAAI;YACF,oCAAoC;YACpC,MAAM,aAAa,SAAS,MAAM;YAElC,qCAAqC;YACrC,0BAA0B;YAE1B,6BAA6B;YAC7B,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBACnD,QAAQ,eAAe;gBACvB,SAAS,WAAW;gBACpB,QAAQ,WAAW,KAAK,YAAY,WAAW;gBAC/C,WAAW,WAAW;gBACtB,eAAe,cAAc,MAAM,GAAG,IAAI,gBAAgB;gBAC1D,WAAW;gBACX,eAAe;YACjB;YAEA,sDAAsD;YACtD,kBAAkB;YAElB,sCAAsC;YACtC,MAAM,QAAQ,KAAK,MAAM;YACzB,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,mBAAmB;YAEnD,2BAA2B;YAC3B,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI;YACtC,MAAM,WAAW,aAAa;YAC9B,MAAM,uBAAuB,KAAK,KAAK,CAAC,YAAY;YAEpD,wDAAwD;YACxD,eAAe;YACf,oBAAoB;YACpB,yBAAyB;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,YAAY;YACZ,eAAe,EAAE;YACjB,oBAAoB;YACpB,yBAAyB;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,gCAAgC;IAChC,MAAM,aAAa,OAAO,OAAO,iBAAiB;QAChD,mBAAmB;QACnB,IAAI;YACF,oCAAoC;YACpC,MAAM,aAAa,SAAS,MAAM;YAElC,qCAAqC;YACrC,qBAAqB;YAErB,yBAAyB;YACzB,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;gBACxD,QAAQ,cAAc,gBAAgB,OAAO,UAAU,6CAA6C;YACtG;YAEA,kDAAkD;YAClD,aAAa;YAEb,IAAI,cAAc,eAAe;gBAC/B,yFAAyF;gBACzF,UAAU;YACZ,OAAO;gBACL,wCAAwC;gBACxC,sCAAsC;gBACtC,MAAM,QAAQ,KAAK,MAAM;gBACzB,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,mBAAmB;gBAEnD,2BAA2B;gBAC3B,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI;gBACtC,MAAM,WAAW,aAAa;gBAC9B,MAAM,kBAAkB,KAAK,KAAK,CAAC,YAAY;gBAE/C,wDAAwD;gBACxD,UAAU,OAAO,yCAAyC;gBAC1D,kBAAkB,kBAAkB,qCAAqC;gBACzE,eAAe;gBACf,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,YAAY;YACZ,UAAU,EAAE;YACZ,kBAAkB,EAAE;YACpB,eAAe;YACf,oBAAoB;QACtB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,wDAAwD;IACxD,MAAM,eAAe,CACnB,YACA,OAAO,iBAAiB,EACxB,WAAW,YAAY;QAEvB,oCAAoC;QACpC,MAAM,aAAa,SAAS,MAAM;QAElC,qCAAqC;QACrC,qBAAqB;QAErB,iBAAiB;QACjB,QAAQ,GAAG,CAAC,yBAAyB;YACnC;YACA,MAAM;YACN;YACA;YACA;QACF;QAEA,qDAAqD;QACrD,MAAM,qBAAqB;YACzB,mBAAmB;YACnB,IAAI;gBACF,MAAM,OAAO,MAAM,gKAAA,CAAA,qBAAkB,CAAC,mBAAmB,CAAC;oBACxD,QAAQ,cAAc;oBACtB,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;oBAC3D,WAAW,iBAAiB;oBAC5B,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;gBAC7C;gBAEA,kDAAkD;gBAClD,aAAa;gBAEb,sCAAsC;gBACtC,MAAM,QAAQ,KAAK,MAAM;gBACzB,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,mBAAmB;gBAEnD,2BAA2B;gBAC3B,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI;gBACtC,MAAM,WAAW,aAAa;gBAC9B,MAAM,kBAAkB,KAAK,KAAK,CAAC,YAAY;gBAE/C,wDAAwD;gBACxD,kBAAkB;gBAClB,eAAe;gBACf,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,YAAY;gBACZ,kBAAkB,EAAE;gBACpB,eAAe;gBACf,oBAAoB;YACtB,SAAU;gBACR,mBAAmB;YACrB;QACF;QAEA,mBAAmB;QACnB;IACF;IAEA,+CAA+C;IAC/C,MAAM,gBAAgB;QACpB,IAAI,CAAC,eAAe;QAEpB,sBAAsB;QACtB,IAAI;YACF,MAAM,YAAY,MAAM,4JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC5D,aAAa,aAAa,EAAE;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;QACA,IAAI,eAAe;YACjB;QACF;QACA,wCAAwC;QACxC;QACA;IACF,GAAG;QAAC;QAAe;QAAuB;KAAiB;IAE3D,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU,KAAK,kCAAkC;QACjD,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB,EAAE;QACvB,gBAAgB,EAAE;QAElB,iCAAiC;QACjC,IAAI,cAAc,eAAe;YAC/B,0BAA0B;YAC1B,gBAAgB;QAClB,OAAO;YACL,qBAAqB;YACrB,WAAW,IAAI,2DAA2D;QAC5E;IACF,GAAG;QAAC;KAAU;IAEd,kFAAkF;IAElF,MAAM,0BAA0B,CAAC;QAC/B,eAAe;QACf,uEAAuE;QACvE,0BAA0B;QAC1B,gBAAgB,GAAG,QAAQ,OAAO,cAAc,eAAe;IACjE;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,wEAAwE;QACxE,IAAI,cAAc,eAAe;YAC/B,0BAA0B;YAC1B,gBAAgB,GAAG,QAAQ,aAAa,OAAO,eAAe;QAChE,OAAO;YACL,qBAAqB;YACrB,aAAa,QAAQ,GAAG;QAC1B;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,0EAA0E;QAC1E,IAAI,cAAc,eAAe;YAC/B,0BAA0B;YAC1B,gBAAgB,GAAG,QAAQ,aAAa,cAAc,OAAO;QAC/D,OAAO;YACL,qBAAqB;YACrB,aAAa,QAAQ,GAAG;QAC1B;IACF;IAEA,MAAM,gCAAgC,CAAC;QACrC,qBAAqB;QACrB,uFAAuF;QACvF,0BAA0B;QAC1B,gBAAgB,GAAG,QAAQ,aAAa,cAAc,eAAe;IACvE;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,kFAAkF;QAClF,qBAAqB;QACrB,aAAa,QAAQ,GAAG;IAC1B;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,eAAe;QACf,gBAAgB;QAChB,iBAAiB;QACjB,qBAAqB,EAAE;QACvB,gBAAgB,EAAE;QAElB,iCAAiC;QACjC,IAAI,cAAc,eAAe;YAC/B,0BAA0B;YAC1B,gBAAgB,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;QACvC,OAAO;YACL,qBAAqB;YACrB,aAAa,IAAI,GAAG,EAAE,GAAG,yCAAyC;QACpE;QAEA,qDAAqD;QACrD,WAAW;YACT,MAAM,QAAQ,IAAI,MAAM;YACxB,SAAS,aAAa,CAAC;QACzB,GAAG;IACL;IAEA,0DAA0D;IAC1D,MAAM,8BAA8B,CAAC;QACnC,gBAAgB,MAAM,QAAQ,aAAa,cAAc,eAAe;IAC1E;IAEA,sDAAsD;IACtD,MAAM,yBAAyB,CAAC;QAC9B,aAAa,QAAQ,MAAM,eAAe,wCAAwC;IACpF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,sBAAsB;IACxB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB,QAAQ,gDAAgD;QACzE,iBAAiB;IACnB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,sBAAsB;QACtB,mBAAmB;YACjB,MAAM;YACN,SAAS,CAAC,oCAAoC,EAAE,WAAW,IAAI,CAAC,CAAC,CAAC;QACpE;QACA,0BAA0B;IAC5B;IAEA,MAAM,kBAAkB,CAAC;QACvB,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,mBAAmB;YACjB,MAAM;YACN,SAAS,CAAC,gCAAgC,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;QAC3D;QACA,0BAA0B;IAC5B;IAEA,kCAAkC;IAClC,MAAM,0BAA0B,OAAO;QACrC,eAAe;QACf,IAAI;YACF,oDAAoD;YACpD,MAAM,gKAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBACzC,QAAQ,UAAU;gBAClB,eAAe,kBAAkB,MAAM,GAAG,IAAI,oBAAoB;gBAClE,SAAS,eAAe;gBACxB,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;gBAC3D,WAAW,iBAAiB;YAC9B,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,4CAA4C;IAC5C,MAAM,qBAAqB,OAAO;QAChC,eAAe;QACf,IAAI;YACF,oDAAoD;YACpD,MAAM,gKAAA,CAAA,qBAAkB,CAAC,sBAAsB,CAAC;gBAC9C,QAAQ,UAAU;gBAClB,UAAU,aAAa,MAAM,GAAG,IAAI,eAAe;gBACnD,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;gBAC3D,WAAW,iBAAiB;YAC9B,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,IAAI,gBAAgB,IAAI,KAAK,qBAAqB;gBAChD,MAAM,gKAAA,CAAA,qBAAkB,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;gBAC/D,cAAc;gBACd;YACF,OAAO,IAAI,gBAAgB,IAAI,KAAK,gBAAgB;gBAClD,MAAM,gKAAA,CAAA,qBAAkB,CAAC,qBAAqB,CAAC,cAAc,EAAE;gBAC/D,cAAc;gBACd;gBACA,mBAAmB,iDAAiD;YACtE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY,MAAM,QAAQ,EAAE,MAAM,WAAW;QAC/C,SAAU;YACR,0BAA0B;QAC5B;IACF;IAEA,0CAA0C;IAC1C,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,MAAM;;;;;;YACvB,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,YAAY;QACd;KACD;IAED,kDAAkD;IAClD,MAAM,eAAe,WAAW,MAAM,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,UAAU,EAAE,OAAO;QAC5B,qBAAO,8OAAC,6IAAA,CAAA,YAAS;YAAC,YAAY,IAAI,UAAU;YAAE,cAAc;sBACzD;;;;;;IAEL;IAEA,wCAAwC;IACxC,MAAM,qBAAqB;QACzB,qCAAqC;QACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAE/C,wDAAwD;QACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,eAAe;QACjB,GAAG;YAAC;SAAO;QAEX,0BAA0B;QAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,cAAc;gBAClB,eAAe;YACjB;YACA,SAAS,gBAAgB,CAAC,SAAS;YACnC,OAAO;gBACL,SAAS,mBAAmB,CAAC,SAAS;YACxC;QACF,GAAG,EAAE;QAEL,kEAAkE;QAClE,MAAM,0BAA0B,CAAC;YAC/B,eAAe,EAAE,MAAM,CAAC,KAAK;QAC/B;QAEA,0DAA0D;QAC1D,MAAM,cAAc;YAClB,UAAU;YACV,0BAA0B;YAE1B,iBAAiB;YACjB,QAAQ,GAAG,CAAC,oCAAoC;gBAC9C,QAAQ;gBACR;gBACA;gBACA;gBACA;YACF;YAEA,gBAAgB,GAAG,aAAa,aAAa,cAAc,eAAe;QAC5E;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,WAAU;oCACV,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB,EAAE,cAAc;4CAChB;wCACF;oCACF;;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;oCACvD,UAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;wCACpB,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gDAAsB,OAAO,MAAM,EAAE;0DACnC,MAAM,IAAI;+CADA,MAAM,EAAE;;;;;;;;;;;8CAMzB,8OAAC,kLAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;;sDAExD,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;gCAI1B,+BACC,8OAAC,kLAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;oCACzD,UAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;gDAAwB,OAAO,QAAQ,EAAE;0DACvC,QAAQ,IAAI;+CADF,QAAQ,EAAE;;;;;;;;;;;8CAO7B,8OAAC,gLAAA,CAAA,cAAW;oCACV,aAAY;oCACZ,MAAK;oCACL,SAAS;;sDAET,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAGrC,8OAAC,gLAAA,CAAA,cAAW;oCACV,aAAY;oCACZ,MAAK;oCACL,SAAS;oCACT,SAAQ;;sDAER,8OAAC,gNAAA,CAAA,YAAS;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC/B,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;8BAMzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oLAAA,CAAA,cAAW;wBACV,OAAM;wBACN,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,aAAY;wBACZ,SAAS;wBACT,gBAAe;;;;;;;;;;;;;;;;;IAKzB;IAEA,oCAAoC;IACpC,MAAM,gBAAgB;QACpB,qCAAqC;QACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAE/C,wDAAwD;QACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,eAAe;QACjB,GAAG;YAAC;SAAO;QAEX,0BAA0B;QAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,cAAc;gBAClB,eAAe;YACjB;YACA,SAAS,gBAAgB,CAAC,SAAS;YACnC,OAAO;gBACL,SAAS,mBAAmB,CAAC,SAAS;YACxC;QACF,GAAG,EAAE;QAEL,kEAAkE;QAClE,MAAM,0BAA0B,CAAC;YAC/B,eAAe,EAAE,MAAM,CAAC,KAAK;QAC/B;QAEA,0DAA0D;QAC1D,MAAM,cAAc;YAClB,UAAU;YACV,qBAAqB;YAErB,iBAAiB;YACjB,QAAQ,GAAG,CAAC,gCAAgC;gBAC1C,QAAQ;gBACR;gBACA;gBACA;YACF;YAEA,aAAa,aAAa,GAAG;QAC/B;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,WAAU;oCACV,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB,EAAE,cAAc;4CAChB;wCACF;oCACF;;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;;sDAExD,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;gCAI1B,+BACC,8OAAC,kLAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;oCACzD,UAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;gDAAwB,OAAO,QAAQ,EAAE;0DACvC,QAAQ,IAAI;+CADF,QAAQ,EAAE;;;;;;;;;;;8CAO7B,8OAAC,gLAAA,CAAA,cAAW;oCACV,aAAY;oCACZ,MAAK;oCACL,SAAS;;sDAET,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;8CAGrC,8OAAC,gLAAA,CAAA,cAAW;oCACV,aAAY;oCACZ,MAAK;oCACL,SAAS;oCACT,SAAQ;;sDAER,8OAAC,gNAAA,CAAA,YAAS;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC/B,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;8BAMzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,oLAAA,CAAA,cAAW;wBACV,OAAM;wBACN,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,aAAY;wBACZ,SAAS;wBACT,gBAAe;;;;;;;;;;;;;;;;;IAKzB;IAEA,6CAA6C;IAC7C,MAAM,iCAAiC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC7C,oDAAoD;QACpD,MAAM,cAAc,2FAAuC,OAAO;QAClE,OAAO,WAAW,CAAC,+BAA+B,IAAI,EAAE;IAC1D,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,4MAAA,CAAA,YAAS;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAC9B,cAAc,gBAAgB,eAAe;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,+BACb,8OAAC,qIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,aAAa,YAAY,MAAM,KAAK;gCAC9C,WAAU;;;;;;4BAGb,cAAc,0BACb,8OAAC,qIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,mBAAmB,eAAe,MAAM,KAAK;gCACvD,WAAU;;;;;;4BAKb,cAAc,+BACb,8OAAC;gCACC,SAAS;oCACP,sBAAsB;oCACtB,sBAAsB;gCACxB;gCACA,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;4BAGjC,cAAc,0BACb,8OAAC;gCACC,SAAS;oCACP,iBAAiB;oCACjB,iBAAiB;gCACnB;gCACA,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC,kLAAA,CAAA,eAAY;gBACX,OAAM;gBACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAClC,aAAY;gBACZ,eAAe;gBACf,cAAa;gBACb,uBACE;;sCACE,8OAAC,8KAAA,CAAA,aAAU;4BACT,MAAM;4BACN,WAAW;4BACX,aAAa;4BACb,aAAY;;;;;;wBAEb,cAAc,8BAAgB,8OAAC;;;;mDAAwB,8OAAC;;;;;;;;;;;;YAM9D,cAAc,+BACb,8OAAC,6IAAA,CAAA,YAAS;gBAAC,YAAW;0BACpB,cAAA,8OAAC,gLAAA,CAAA,cAAW;oBACV,aAAY;oBACZ,SAAS;wBACP;4BAAE,QAAQ;4BAAa,OAAO;4BAAQ,OAAO;wBAAM;wBACnD;4BAAE,QAAQ;4BAAS,OAAO;4BAAS,OAAO;wBAAM;2BAC5C,gBAAgB;4BAAC;gCAAE,QAAQ;gCAAW,OAAO;gCAAW,OAAO;4BAAM;yBAAE,GAAG,EAAE;wBAChF;4BAAE,QAAQ;4BAAa,OAAO;4BAAe,OAAO;wBAAM;wBAC1D;4BAAE,QAAQ;4BAAY,OAAO;4BAAS,OAAO;wBAAM;wBACnD;4BAAE,QAAQ;4BAAU,OAAO;4BAAU,OAAO;wBAAM;wBAClD;4BAAE,QAAQ;4BAAS,OAAO;4BAAW,WAAW;4BAAc,OAAO;4BAAO,UAAU;wBAAM;qBAC7F;oBACD,MAAM;oBACN,WAAW;oBACX,cAAa;oBACb,yBAAW,8OAAC,4MAAA,CAAA,YAAS;wBAAC,MAAM;;;;;;oBAC5B,SAAQ;oBACR,oBAAoB;oBACpB,kBAAiB;oBACjB,sBAAqB;oBACrB,aAAa;oBACb,YAAY;oBACZ,YAAY;oBACZ,cAAc;oBACd,QAAQ,CAAC,OAAO;wBACd,0FAA0F;wBAC1F,gBACE,wBACA,QACA,aACA,cACA,eACA,mBACA,OACA;oBAEJ;oBACA,gBAAgB;oBAChB,cAAc;oBACd,WAAW,CAAC,YAAY,OAAO,cAAc,+BAC3C,8OAAC;4BAAuB,WAAW,aAAa,OAAO;;gCACpD,eAAe,QAAQ,CAAC,yBACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;gCAOzB,eAAe,QAAQ,CAAC,0BACvB,8OAAC;oCAAG,WAAU;8CACX,WAAW,KAAK,iBACf,8OAAC;wCAAK,WAAU;kDACb,WAAW,KAAK,CAAC,IAAI;;;;;+DAGxB,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;gCAOrE,iBAAiB,eAAe,QAAQ,CAAC,4BACxC,8OAAC;oCAAG,WAAU;8CACX,WAAW,OAAO,iBACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC9B,8OAAC;gDAAK,WAAU;0DACb,WAAW,OAAO,CAAC,IAAI;;;;;;;;;;;+DAI5B,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;gCAOrE,eAAe,QAAQ,CAAC,gCACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;kDACZ,WAAW,WAAW,kBACrB,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;;;;;;gCAQvE,eAAe,QAAQ,CAAC,0BACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,8OAAC;gDAAK,WAAU;;oDACb,WAAW,MAAM,EAAE,SAAS;oDAAE;;;;;;;;;;;;;;;;;;gCAMtC,eAAe,QAAQ,CAAC,2BACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCACC,WAAW,CAAC,6DAA6D,EACvE,WAAW,MAAM,GACb,yEACA,gEACJ;kDAED,WAAW,MAAM,iBAChB;;8DACE,8OAAC,2NAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;yEAGR;;8DACE,8OAAC,4MAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;gCAOf,eAAe,QAAQ,CAAC,4BACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACf,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;gDACV,OAAM;0DAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,MAAM;;;;;;;;;;;0DAEnB,8OAAC,6IAAA,CAAA,YAAS;gDAAC,YAAW;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,qBAAqB;oDACpC,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;;;;;;0DAGhB,8OAAC,6IAAA,CAAA,YAAS;gDAAC,YAAW;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,uBAAuB;oDACtC,WAAU;oDACV,OAAM;oDACN,UAAU,WAAW,MAAM,EAAE,QAAQ;8DAErC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAW,WAAW,MAAM,EAAE,QAAQ,IAAI,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAxH9F,WAAW,EAAE;;;;;;;;;;;;;;;YAqI7B,cAAc,0BACb,8OAAC,6IAAA,CAAA,YAAS;gBAAC,YAAW;0BACpB,cAAA,8OAAC,gLAAA,CAAA,cAAW;oBACV,aAAY;oBACZ,SAAS;wBACP;4BAAE,QAAQ;4BAAS,OAAO;4BAAQ,OAAO;wBAAM;wBAC/C;4BAAE,QAAQ;4BAAa,OAAO;4BAAe,OAAO;wBAAM;2BACtD,gBAAgB;4BAAC;gCAAE,QAAQ;gCAAW,OAAO;gCAAW,OAAO;4BAAM;yBAAE,GAAG,EAAE;wBAChF;4BAAE,QAAQ;4BAAc,OAAO;4BAAe,OAAO;wBAAM;wBAC3D;4BAAE,QAAQ;4BAAU,OAAO;4BAAU,OAAO;wBAAM;wBAClD;4BAAE,QAAQ;4BAAS,OAAO;4BAAW,WAAW;4BAAc,OAAO;4BAAO,UAAU;wBAAM;qBAC7F;oBACD,MAAM;oBACN,WAAW;oBACX,cAAa;oBACb,yBAAW,8OAAC,gMAAA,CAAA,MAAG;wBAAC,MAAM;;;;;;oBACtB,SAAQ;oBACR,oBAAoB;oBACpB,kBAAiB;oBACjB,sBAAqB;oBACrB,aAAa;oBACb,YAAY;oBACZ,YAAY;oBACZ,cAAc;oBACd,QAAQ,CAAC,OAAO;wBACd,sFAAsF;wBACtF,oCAAoC;wBACpC,MAAM,eAAe;+BAAI;yBAAe,CAAC,IAAI,CAAC,CAAC,GAAG;4BAChD,IAAI,UAAU,QAAQ;gCACpB,OAAO,cAAc,QACjB,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,IAC3B,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;4BACjC;4BACA,OAAO;wBACT;wBACA,kBAAkB;oBACpB;oBACA,gBAAgB;oBAChB,cAAc;oBACd,WAAW,CAAC,OAAO,OAAO,cAAc,+BACtC,8OAAC;4BAAkB,WAAW,aAAa,OAAO;;gCAC/C,eAAe,QAAQ,CAAC,yBACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,MAAM;;;;;;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;gCAOpB,eAAe,QAAQ,CAAC,gCACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;kDACZ,MAAM,WAAW,kBAChB,8OAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;;;;;;gCAQvE,iBAAiB,eAAe,QAAQ,CAAC,4BACxC,8OAAC;oCAAG,WAAU;8CACX,MAAM,OAAO,iBACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC9B,8OAAC;gDAAK,WAAU;0DACb,MAAM,OAAO,CAAC,IAAI;;;;;;;;;;;+DAIvB,8OAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;gCAOrE,eAAe,QAAQ,CAAC,gCACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4MAAA,CAAA,YAAS;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC/B,8OAAC;gDAAK,WAAU;;oDACb,MAAM,MAAM,EAAE,eAAe;oDAAE;;;;;;;;;;;;;;;;;;gCAMvC,eAAe,QAAQ,CAAC,2BACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCACC,WAAW,CAAC,6DAA6D,EACvE,MAAM,MAAM,GACR,yEACA,gEACJ;kDAED,MAAM,MAAM,iBACX;;8DACE,8OAAC,2NAAA,CAAA,cAAW;oDAAC,MAAM;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;yEAGR;;8DACE,8OAAC,4MAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;gCAOf,eAAe,QAAQ,CAAC,4BACvB,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAI,WAAU;;0DACf,8OAAC,6IAAA,CAAA,YAAS;gDAAC,YAAW;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;oDACV,OAAM;8DAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,MAAM;;;;;;;;;;;;;;;;0DAGhB,8OAAC,6IAAA,CAAA,YAAS;gDAAC,YAAW;0DACpB,cAAA,8OAAC;oDACC,SAAS,IAAM,kBAAkB;oDACjC,WAAU;oDACV,OAAM;oDACN,UAAU,MAAM,MAAM,EAAE,cAAc;8DAEtC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,MAAM;wDAAI,WAAW,MAAM,MAAM,EAAE,cAAc,IAAI,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnG/F,MAAM,EAAE;;;;;;;;;;;;;;;0BAgHzB,8OAAC,iJAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,YAAY;gBACZ,QAAQ;gBACR,WAAW;oBACT,sBAAsB;oBACtB;gBACF;;;;;;0BAGF,8OAAC,sJAAA,CAAA,UAAwB;gBACvB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,OAAO;gBACP,WAAW;oBACT,iBAAiB;oBACjB;oBACA,mBAAmB,iDAAiD;gBACtE;;;;;;0BAGF,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,cAAc,oBAAoB;;;;;;0BAGpC,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,OAAM;gBACN,SAAS,iBAAiB,WAAW;gBACrC,SAAS,iBAAiB,KAAK,SAAS,YAAY,WAAW;gBAC/D,aAAa,iBAAiB,KAAK,SAAS,YAAY,YAAY;;;;;;;;;;;;AAM5E;uCAEe"}}, {"offset": {"line": 5630, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Projeto%20X/high-tide-systems-frontend/src/app/dashboard/admin/professions/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport ProfessionsPage from \"@/app/modules/admin/professions/ProfessionsPage\";\r\nimport { Protected } from \"@/components/permissions/Protected\";\r\n\r\nexport default function ProfessionsRoute() {\r\n  return (\r\n    <Protected permission={[\"admin.professions.view\", \"admin.profession-groups.view\"]} requireAll={false}>\r\n      <ProfessionsPage />\r\n    </Protected>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC,6IAAA,CAAA,YAAS;QAAC,YAAY;YAAC;YAA0B;SAA+B;QAAE,YAAY;kBAC7F,cAAA,8OAAC,gKAAA,CAAA,UAAe;;;;;;;;;;AAGtB"}}, {"offset": {"line": 5664, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}